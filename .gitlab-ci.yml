include:
  - project: "arc-studio-ai/packages/ci-templates"
    ref: main
    file: "/Deploy-ECS.gitlab-ci.yml"

variables:
  CI_AWS_ECS_CLUSTER: BackendCluster
  CI_PULUMI_AWS_ENVIRONMENT: bond/aws/prod

.golang: &golang
  image: golang:latest
  before_script:
    - go mod tidy
    - go mod download
  only:
    - main

format:
  <<: *golang
  script:
    - go fmt $(go list ./...)
    - go vet $(go list ./...)

test:
  <<: *golang
  script:
    - go test -race $(go list ./...)

deploy_ecs_lowes:
  extends: .deploy_ecs
  variables:
    CI_PULUMI_AWS_ENVIRONMENT: bond/aws/lowes.prod
