-- migrate:up
ALTER TABLE renditions
ADD COLUMN room_design_id UUID REFERENCES design.room_designs (id) ON DELETE CASCADE;

-- Populate the new column with data from the existing join table
UPDATE renditions
SET
    room_design_id = (
        SELECT
            room_design_id
        FROM
            design.room_design_renditions
        WHERE
            rendition_id = renditions.id
        LIMIT
            1
    );

-- migrate:down
ALTER TABLE renditions
DROP COLUMN room_design_id;