package db

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"os"
	"strconv"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
)

// PoolConfig holds configuration for database connection pools
type PoolConfig struct {
	MaxConns          int32         // Maximum number of connections in the pool
	MinConns          int32         // Minimum number of connections in the pool
	MaxConnLifetime   time.Duration // Maximum lifetime of a connection
	MaxConnIdleTime   time.Duration // Maximum idle time of a connection
	HealthCheckPeriod time.Duration // Period between health checks
}

// DefaultPoolConfig returns a default pool configuration
func DefaultPoolConfig() *PoolConfig {
	return &PoolConfig{
		MaxConns:          30,               // Default max connections
		MinConns:          5,                // Default min connections
		MaxConnLifetime:   time.Hour,        // 1 hour max connection lifetime
		MaxConnIdleTime:   30 * time.Minute, // 30 minutes max idle time
		HealthCheckPeriod: time.Minute,      // 1 minute health check period
	}
}

// LoadPoolConfigFromEnv loads pool configuration from environment variables
func LoadPoolConfigFromEnv() *PoolConfig {
	config := DefaultPoolConfig()

	if maxConns := os.Getenv("DB_MAX_CONNS"); maxConns != "" {
		if val, err := strconv.ParseInt(maxConns, 10, 32); err == nil {
			config.MaxConns = int32(val)
		}
	}

	if minConns := os.Getenv("DB_MIN_CONNS"); minConns != "" {
		if val, err := strconv.ParseInt(minConns, 10, 32); err == nil {
			config.MinConns = int32(val)
		}
	}

	if maxLifetime := os.Getenv("DB_MAX_CONN_LIFETIME"); maxLifetime != "" {
		if val, err := time.ParseDuration(maxLifetime); err == nil {
			config.MaxConnLifetime = val
		}
	}

	if maxIdleTime := os.Getenv("DB_MAX_CONN_IDLE_TIME"); maxIdleTime != "" {
		if val, err := time.ParseDuration(maxIdleTime); err == nil {
			config.MaxConnIdleTime = val
		}
	}

	if healthCheckPeriod := os.Getenv("DB_HEALTH_CHECK_PERIOD"); healthCheckPeriod != "" {
		if val, err := time.ParseDuration(healthCheckPeriod); err == nil {
			config.HealthCheckPeriod = val
		}
	}

	return config
}

// createPoolConfig creates a pgxpool.Config with the specified settings
func createPoolConfig(connString string, poolConfig *PoolConfig) (*pgxpool.Config, error) {
	config, err := pgxpool.ParseConfig(connString)
	if err != nil {
		return nil, fmt.Errorf("failed to parse connection string: %w", err)
	}

	config.MaxConns = poolConfig.MaxConns
	config.MinConns = poolConfig.MinConns
	config.MaxConnLifetime = poolConfig.MaxConnLifetime
	config.MaxConnIdleTime = poolConfig.MaxConnIdleTime
	config.HealthCheckPeriod = poolConfig.HealthCheckPeriod

	return config, nil
}

func NewPostgres(ctx context.Context, pgHost string, pgDatabase string, pwd string, logger *slog.Logger) *gateways.Postgres {
	log.Printf("Connecting to database %s on %s.", pgDatabase, pgHost)
	if pwd != "" {
		pwd = "password=" + pwd
	}
	dbCfg := fmt.Sprintf("host=%s user=postgres %s dbname=%s", pgHost, pwd, pgDatabase)
	dbPool, err := pgxpool.New(ctx, dbCfg)
	if err != nil {
		log.Fatalf("Error connecting to database: %v", err)
	}
	return gateways.NewPostgres(dbPool, logger)
}

// NewRelationalDb creates a RelationalDb with optional custom pool configuration
func NewRelationalDb(ctx context.Context,
	dbUrl string, logger *slog.Logger, poolConfig *PoolConfig) *gateways.RelationalDb {

	dbCfg := fmt.Sprint(dbUrl)
	if poolConfig == nil {
		// Load pool configuration from environment variables
		poolConfig = LoadPoolConfigFromEnv()
	}

	// Create pool configuration
	config, err := createPoolConfig(dbCfg, poolConfig)
	if err != nil {
		log.Fatalf("Error creating pool configuration: %v", err)
	}

	// Create the connection pool
	dbPool, err := NewPool(ctx, config)
	if err != nil {
		log.Fatalf("Error connecting to database: %v", err)
	}

	logger.Info("RelationalDb connection pool created",
		"max_conns", poolConfig.MaxConns,
		"min_conns", poolConfig.MinConns,
		"max_conn_lifetime", poolConfig.MaxConnLifetime,
		"max_conn_idle_time", poolConfig.MaxConnIdleTime,
	)

	return gateways.NewRelationalDb(dbPool, logger)
}

// LogPoolStats logs current pool statistics for monitoring
func LogPoolStats(pool *pgxpool.Pool, logger *slog.Logger) {
	if pool == nil {
		logger.Warn("Cannot log pool stats: pool is nil")
		return
	}

	stats := pool.Stat()
	logger.Info("Database pool statistics",
		"acquired_conns", stats.AcquiredConns(),
		"constructing_conns", stats.ConstructingConns(),
		"idle_conns", stats.IdleConns(),
		"max_conns", stats.MaxConns(),
		"total_conns", stats.TotalConns(),
		"acquire_count", stats.AcquireCount(),
		"acquire_duration", stats.AcquireDuration(),
		"max_lifetime_destroy_count", stats.MaxLifetimeDestroyCount(),
	)
}
