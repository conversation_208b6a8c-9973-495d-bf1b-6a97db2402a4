-- migrate:up

-- Create a comprehensive view that joins all template-related tables
-- This simplifies template retrieval by consolidating the complex joins
-- into a single view that can be queried directly.
CREATE VIEW template.template_details AS
SELECT 
    -- Main template fields
    t.id,
    t.created_at,
    t.updated_at,
    t.color_scheme,
    t.style,
    t.render_priority,
    t.name,
    t.description,
    t.image_url,
    t.inspiration,
    t.atmosphere,
    t.color_palette,
    t.material_palette,
    t.highlighted_brand_urls,
    
    -- Product selections
    tps.floor_tile,
    tps.lighting,
    tps.mirror,
    tps.paint,
    tps.shelving,
    tps.toilet,
    tps.shower_floor_tile,
    tps.shower_wall_tile,
    tps.tub_filler,
    tps.wall_tile_placement,
    tps.wall_tile,
    tps.wallpaper_placement,
    tps.wallpaper,
    
    -- Template options
    to_.alcove_tub,
    to_.freestanding_tub,
    to_.shower_glass_fixed,
    to_.shower_glass_sliding,
    to_.shower_system_full,
    to_.shower_system_shower,
    to_.tub_door_fixed,
    to_.tub_door_sliding,
    
    -- Template provenance (brand information) - nullable
    tp.lighting_brand,
    tp.plumbing_brand,
    tp.toilet_brand,
    tp.vanity_brand,
    tp.vanity_storage,
    
    -- Legacy lookup - nullable
    ll.id as legacy_id

FROM template.templates t
LEFT JOIN template.template_product_selections tps ON t.id = tps.template_id
LEFT JOIN template.template_options to_ ON t.id = to_.template_id
LEFT JOIN template.template_provenance tp ON t.id = tp.template_id
LEFT JOIN public.legacy_lookup ll ON t.id = ll.template_id;

-- Add a comment explaining the view's purpose
COMMENT ON VIEW template.template_details IS 
'Comprehensive view that joins all template-related tables except vanity_scaling_options. 
This view simplifies template retrieval by consolidating complex joins into a single queryable view.
Note: vanity_scaling_options are still fetched separately due to their one-to-many relationship.';

-- migrate:down

-- Drop the view
DROP VIEW IF EXISTS template.template_details;
