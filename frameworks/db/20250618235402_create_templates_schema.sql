-- migrate:up
CREATE SCHEMA template;
SET
    search_path TO public,
    template;

CREATE TABLE template.templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    color_scheme color_scheme_enum NOT NULL,
    style style_enum NOT NULL,
    render_priority posint,
    name TEXT UNIQUE NOT NULL,
    description TEXT NOT NULL,
    image_url http_img_url UNIQUE NOT NULL,
    inspiration TEXT NOT NULL,
    atmosphere TEXT[] NOT NULL,
    color_palette TEXT[] NOT NULL,
    material_palette TEXT[] NOT NULL,
    highlighted_brand_urls http_img_url[]
);
CREATE TRIGGER refresh_templates_updated_at BEFORE
UPDATE ON template.templates FOR EACH ROW EXECUTE FUNCTION maintain_updated_at_column();

CREATE TABLE template.template_product_selections (
    template_id UUID PRIMARY KEY REFERENCES template.templates (id) ON DELETE CASCADE,
    floor_tile UUID NOT NULL,
    lighting UUID NOT NULL,
    mirror UUID NOT NULL,
    paint UUID NOT NULL,
    shelving UUID NOT NULL,
    toilet UUID NOT NULL,
    shower_floor_tile UUID NOT NULL,
    shower_wall_tile UUID NOT NULL,
    tub_filler UUID NOT NULL,
    wall_tile_placement TEXT NOT NULL,
    wall_tile UUID NOT NULL,
    wallpaper_placement TEXT NOT NULL,
    wallpaper UUID
);

CREATE TABLE template.template_options (
    template_id UUID PRIMARY KEY REFERENCES template.templates (id) ON DELETE CASCADE,
    alcove_tub UUID NOT NULL,
    freestanding_tub UUID NOT NULL,
    shower_glass_fixed UUID NOT NULL,
    shower_glass_sliding UUID NOT NULL,
    shower_system_full UUID NOT NULL,
    shower_system_shower UUID NOT NULL,
    tub_door_fixed UUID NOT NULL,
    tub_door_sliding UUID NOT NULL
);

CREATE TABLE template.vanity_scaling_options (
    template_id UUID NOT NULL REFERENCES template.templates (id) ON DELETE CASCADE,
    min_vanity_length_inches posint NOT NULL,
    UNIQUE (template_id, min_vanity_length_inches),
    vanity_product_id UUID NOT NULL,
    faucet_product_id UUID NOT NULL
);

CREATE TABLE template.template_provenance (
    template_id UUID PRIMARY KEY REFERENCES template.templates (id) ON DELETE CASCADE,
    lighting_brand TEXT NOT NULL,
    plumbing_brand TEXT NOT NULL,
    toilet_brand TEXT NOT NULL,
    vanity_brand TEXT NOT NULL,
    vanity_storage TEXT NOT NULL
);

CREATE TABLE public.legacy_lookup (
    id VARCHAR(2) PRIMARY KEY,
    template_id UUID UNIQUE NOT NULL REFERENCES template.templates (id) ON DELETE CASCADE
);

-- migrate:down
DROP TABLE IF EXISTS public.legacy_lookup;
DROP SCHEMA IF EXISTS template CASCADE;