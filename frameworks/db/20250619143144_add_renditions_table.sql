-- migrate:up
CREATE TYPE public.rendition_status_enum AS ENUM (
    'Pending',
    'Started',
    'Completed',
    'Outdated',
    'Archived'
);

CREATE TABLE public.renditions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    status public.rendition_status_enum NOT NULL,
    url http_img_url,
    CONSTRAINT chk_rendition_url CHECK (
        status <> 'Completed'
        OR url IS NOT NULL
    )
);

CREATE TRIGGER refresh_renditions_updated_at BEFORE
UPDATE ON public.renditions FOR EACH ROW EXECUTE FUNCTION maintain_updated_at_column ();

CREATE TABLE design.room_design_renditions (
    room_design_id UUID NOT NULL REFERENCES design.room_designs (id) ON DELETE CASCADE,
    rendition_id UUID NOT NULL REFERENCES public.renditions (id) ON DELETE CASCADE,
    PRIMARY KEY (room_design_id, rendition_id)
);

CREATE INDEX ON design.room_design_renditions (room_design_id);

CREATE INDEX ON design.room_design_renditions (rendition_id);

ALTER TABLE public.legacy_lookup
ADD COLUMN room_design_id UUID REFERENCES design.room_designs (id) ON DELETE CASCADE,
ADD COLUMN rendition_id UUID UNIQUE REFERENCES public.renditions (id) ON DELETE CASCADE;

ALTER TABLE public.legacy_lookup ADD CONSTRAINT preset_fkey FOREIGN KEY (room_design_id, rendition_id) REFERENCES design.room_design_renditions (room_design_id, rendition_id);

-- migrate:down
ALTER TABLE public.legacy_lookup
DROP CONSTRAINT IF EXISTS preset_fkey;

ALTER TABLE public.legacy_lookup
DROP COLUMN IF EXISTS rendition_id;

ALTER TABLE public.legacy_lookup
DROP COLUMN IF EXISTS room_design_id;

DROP TABLE IF EXISTS design.room_design_renditions;

DROP TRIGGER IF EXISTS refresh_renditions_updated_at ON public.renditions;

DROP TABLE IF EXISTS public.renditions;

DROP TYPE IF EXISTS rendition_status_enum;