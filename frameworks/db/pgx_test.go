package db

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDefaultPoolConfig(t *testing.T) {
	config := DefaultPoolConfig()

	assert.Equal(t, int32(30), config.<PERSON>Conns)
	assert.Equal(t, int32(5), config.MinConns)
	assert.Equal(t, time.Hour, config.MaxConnLifetime)
	assert.Equal(t, 30*time.Minute, config.MaxConnIdleTime)
	assert.Equal(t, time.Minute, config.HealthCheckPeriod)
}

func TestLoadPoolConfigFromEnv(t *testing.T) {
	// Save original environment
	originalEnv := map[string]string{
		"DB_MAX_CONNS":           os.Getenv("DB_MAX_CONNS"),
		"DB_MIN_CONNS":           os.Getenv("DB_MIN_CONNS"),
		"DB_MAX_CONN_LIFETIME":   os.Getenv("DB_MAX_CONN_LIFETIME"),
		"DB_MAX_CONN_IDLE_TIME":  os.Getenv("DB_MAX_CONN_IDLE_TIME"),
		"DB_HEALTH_CHECK_PERIOD": os.Getenv("DB_HEALTH_CHECK_PERIOD"),
	}

	// Clean up after test
	defer func() {
		for key, value := range originalEnv {
			if value == "" {
				if err := os.Unsetenv(key); err != nil {
					t.Errorf("Failed to unset environment variable: %v", err)
				}
			} else {
				if err := os.Setenv(key, value); err != nil {
					t.Errorf("Failed to set environment variable: %v", err)
				}
			}
		}
	}()

	t.Run("with environment variables", func(t *testing.T) {
		// Set test environment variables
		require.NoError(t, os.Setenv("DB_MAX_CONNS", "50"))
		require.NoError(t, os.Setenv("DB_MIN_CONNS", "10"))
		require.NoError(t, os.Setenv("DB_MAX_CONN_LIFETIME", "2h"))
		require.NoError(t, os.Setenv("DB_MAX_CONN_IDLE_TIME", "45m"))
		require.NoError(t, os.Setenv("DB_HEALTH_CHECK_PERIOD", "30s"))

		config := LoadPoolConfigFromEnv()

		assert.Equal(t, int32(50), config.MaxConns)
		assert.Equal(t, int32(10), config.MinConns)
		assert.Equal(t, 2*time.Hour, config.MaxConnLifetime)
		assert.Equal(t, 45*time.Minute, config.MaxConnIdleTime)
		assert.Equal(t, 30*time.Second, config.HealthCheckPeriod)
	})

	t.Run("with invalid environment variables", func(t *testing.T) {
		// Set invalid environment variables
		require.NoError(t, os.Setenv("DB_MAX_CONNS", "invalid"))
		require.NoError(t, os.Setenv("DB_MIN_CONNS", "not-a-number"))
		require.NoError(t, os.Setenv("DB_MAX_CONN_LIFETIME", "invalid-duration"))
		require.NoError(t, os.Setenv("DB_MAX_CONN_IDLE_TIME", "bad-time"))
		require.NoError(t, os.Setenv("DB_HEALTH_CHECK_PERIOD", "wrong"))

		config := LoadPoolConfigFromEnv()

		// Should fall back to defaults when parsing fails
		assert.Equal(t, int32(30), config.MaxConns)
		assert.Equal(t, int32(5), config.MinConns)
		assert.Equal(t, time.Hour, config.MaxConnLifetime)
		assert.Equal(t, 30*time.Minute, config.MaxConnIdleTime)
		assert.Equal(t, time.Minute, config.HealthCheckPeriod)
	})

	t.Run("without environment variables", func(t *testing.T) {
		// Clear all environment variables
		require.NoError(t, os.Unsetenv("DB_MAX_CONNS"))
		require.NoError(t, os.Unsetenv("DB_MIN_CONNS"))
		require.NoError(t, os.Unsetenv("DB_MAX_CONN_LIFETIME"))
		require.NoError(t, os.Unsetenv("DB_MAX_CONN_IDLE_TIME"))
		require.NoError(t, os.Unsetenv("DB_HEALTH_CHECK_PERIOD"))

		config := LoadPoolConfigFromEnv()

		// Should use defaults
		assert.Equal(t, int32(30), config.MaxConns)
		assert.Equal(t, int32(5), config.MinConns)
		assert.Equal(t, time.Hour, config.MaxConnLifetime)
		assert.Equal(t, 30*time.Minute, config.MaxConnIdleTime)
		assert.Equal(t, time.Minute, config.HealthCheckPeriod)
	})
}

func TestCreatePoolConfig(t *testing.T) {
	poolConfig := &PoolConfig{
		MaxConns:          25,
		MinConns:          3,
		MaxConnLifetime:   2 * time.Hour,
		MaxConnIdleTime:   45 * time.Minute,
		HealthCheckPeriod: 30 * time.Second,
	}

	t.Run("valid connection string", func(t *testing.T) {
		connString := "postgres://user:password@localhost:5432/testdb"

		config, err := createPoolConfig(connString, poolConfig)

		require.NoError(t, err)
		assert.Equal(t, poolConfig.MaxConns, config.MaxConns)
		assert.Equal(t, poolConfig.MinConns, config.MinConns)
		assert.Equal(t, poolConfig.MaxConnLifetime, config.MaxConnLifetime)
		assert.Equal(t, poolConfig.MaxConnIdleTime, config.MaxConnIdleTime)
		assert.Equal(t, poolConfig.HealthCheckPeriod, config.HealthCheckPeriod)
	})

	t.Run("invalid connection string", func(t *testing.T) {
		connString := "invalid-connection-string"

		config, err := createPoolConfig(connString, poolConfig)

		assert.Error(t, err)
		assert.Nil(t, config)
		assert.Contains(t, err.Error(), "failed to parse connection string")
	})
}
