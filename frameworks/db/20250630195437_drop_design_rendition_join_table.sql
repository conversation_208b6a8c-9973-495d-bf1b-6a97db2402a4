-- migrate:up
ALTER TABLE public.renditions
ALTER COLUMN room_design_id
SET
    NOT NULL;

ALTER TABLE public.legacy_lookup
DROP CONSTRAINT IF EXISTS preset_fkey;

DROP TABLE IF EXISTS design.room_design_renditions;

ALTER TABLE public.legacy_lookup
DROP COLUMN IF EXISTS room_design_id;

-- migrate:down
ALTER TABLE public.legacy_lookup
ADD COLUMN room_design_id UUID REFERENCES design.room_designs (id) ON DELETE CASCADE;

CREATE TABLE design.room_design_renditions (
    room_design_id UUID NOT NULL REFERENCES design.room_designs (id) ON DELETE CASCADE,
    rendition_id UUID NOT NULL REFERENCES public.renditions (id) ON DELETE CASCADE,
    PRIMARY KEY (room_design_id, rendition_id)
);

ALTER TABLE public.legacy_lookup ADD CONSTRAINT preset_fkey FOREIGN KEY (room_design_id, rendition_id) REFERENCES design.room_design_renditions (room_design_id, rendition_id);

ALTER TABLE public.renditions
ALTER COLUMN room_design_id
DROP NOT NULL;