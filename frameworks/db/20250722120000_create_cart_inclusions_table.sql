-- migrate:up

-- Create location enum type
CREATE TYPE design.location_type AS ENUM (
    'Floor',
    'ShowerFloor',
    'ShowerNiche',
    'ShowerWall',
    'Wall',
    'Unspecified'
);

-- Create cart_inclusions table in the design schema
-- This table tracks cart inclusion selections in the room design materials list
-- allowing users to include/exclude products and adjust quantities
CREATE TABLE design.cart_inclusions (
    room_design_id UUID NOT NULL REFERENCES design.room_designs (id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    product_id UUID NOT NULL,
    location design.location_type NOT NULL DEFAULT 'Unspecified',
    include BOOLEAN NOT NULL DEFAULT TRUE,
    quantity_diff INTEGER NOT NULL DEFAULT 0,
    PRIMARY KEY (room_design_id, product_id, location)
);

-- Note: No additional indexes needed
-- The composite primary key (room_design_id, product_id, location) efficiently handles:
-- - Queries by room_design_id only (most common use case)
-- - Queries by room_design_id and product_id (common lookups)
-- - Exact lookups by all three columns for unique constraint enforcement

-- Add trigger to automatically update the updated_at timestamp
CREATE TRIGGER refresh_cart_inclusions_updated_at
BEFORE UPDATE ON design.cart_inclusions
FOR EACH ROW EXECUTE FUNCTION maintain_updated_at_column();

-- Add table comment for documentation
COMMENT ON TABLE design.cart_inclusions IS
'Records product inclusion preferences for room designs. Each row represents a user decision to
include/exclude a specific product at a particular location, with optional quantity adjustments.
This allows customization of the materials list without modifying the base design.';

-- Add column comments for clarity
COMMENT ON COLUMN design.cart_inclusions.room_design_id IS 'References the room design this inclusion applies to';
COMMENT ON COLUMN design.cart_inclusions.product_id IS 'Identifier of the product being included or excluded';
COMMENT ON COLUMN design.cart_inclusions.location IS 'Physical location for product placement: FLOOR, SHOWER_FLOOR, SHOWER_NICHE, SHOWER_WALL, WALL, or UNSPECIFIED when location is unknown/irrelevant';
COMMENT ON COLUMN design.cart_inclusions.include IS 'True to include product in cart, false to exclude from cart';
COMMENT ON COLUMN design.cart_inclusions.quantity_diff IS 'Quantity adjustment from base design quantity (e.g., -1, 0, +2)';

-- migrate:down

-- Drop the table and all associated objects
DROP TABLE IF EXISTS design.cart_inclusions CASCADE;

-- Drop the location enum type
DROP TYPE IF EXISTS design.location_type CASCADE;
