package web_test

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const previewsSearchPath = "/projects/v0/previews/search"

// setupProjectHandler creates a project handler with all necessary dependencies
func setupProjectHandler() *web.ProjectHandler {
	logger := slog.Default()
	r := gateways.NewFakeRelDb()
	designFetcher := usecases.NewDesignRetriever(r)
	projectIdRetriever := usecases.NewProjectIdRetriever(r)
	projectSynthesizer := usecases.NewProjectSynthesizer(r, gateways.NewFakeMonolith())
	ai := gateways.NewFakeLLM()
	generator := usecases.NewDesignGenerator(r, gateways.NewFakeMonolith(), gateways.NewRooms([]byte(`{}`)),
		gateways.NewFakeProductSearch(), ai, logger)
	queryController := controllers.NewDesignRetrievalController(designFetcher, projectIdRetriever, projectSynthesizer, generator)
	return web.NewProjectHandler(queryController, logger)
}

func TestProjectHandler_HandleGetRandomProject(t *testing.T) {
	handler := setupProjectHandler()

	req := httptest.NewRequest("GET", previewsSearchPath+"?designId="+uuid.New().String(), nil)
	w := httptest.NewRecorder()

	handler.HandleGetRandomProject(w, req)
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	expectedContentType := "application/json"
	if contentType := w.Header().Get("Content-Type"); contentType != expectedContentType {
		t.Errorf("Expected content type %s, got %s", expectedContentType, contentType)
	}

	expectedCORS := "*"
	if cors := w.Header().Get("Access-Control-Allow-Origin"); cors != expectedCORS {
		t.Errorf("Expected CORS header %s, got %s", expectedCORS, cors)
	}

	var project map[string]any
	if err := json.Unmarshal(w.Body.Bytes(), &project); err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	requiredFields := []string{"name", "cost_usd", "num_items", "area_sq_ft", "updated_at"}
	for _, field := range requiredFields {
		if _, exists := project[field]; !exists {
			t.Errorf("Required field %s is missing from response", field)
		}
	}

	if name, ok := project["name"].(string); !ok || name == "" {
		t.Error("Field 'name' should be a non-empty string")
	}
	if costUSD, ok := project["cost_usd"].(float64); !ok || costUSD < 0 {
		t.Error("Field 'cost_usd' should be a positive number")
	}
	if numItems, ok := project["num_items"].(float64); !ok || numItems < 0 {
		t.Error("Field 'num_items' should be a positive number")
	}
	if areaSqFt, ok := project["area_sq_ft"].(float64); !ok || areaSqFt < 0 {
		t.Error("Field 'area_sq_ft' should be a positive number")
	}

	if updatedAtStr, ok := project["updated_at"].(string); !ok {
		t.Error("Field 'updated_at' should be a string")
	} else {
		// Validate it's a valid RFC3339 timestamp
		if _, err := time.Parse(time.RFC3339, updatedAtStr); err != nil {
			t.Errorf("Field 'updated_at' should be a valid RFC3339 timestamp, got: %s", updatedAtStr)
		}
	}

	// Validate image_url if present (it's optional)
	if imageURL, exists := project["image_url"]; exists && imageURL != nil {
		if _, ok := imageURL.(string); !ok {
			t.Error("Field 'image_url' should be a string when present")
		}
	}

	// Check that no additional properties are present
	allowedFields := map[string]bool{
		"name":       true,
		"cost_usd":   true,
		"num_items":  true,
		"area_sq_ft": true,
		"image_url":  true,
		"updated_at": true,
	}

	for field := range project {
		if !allowedFields[field] {
			t.Errorf("Unexpected field %s in response", field)
		}
	}
}

// setupTestServer creates a test server with the project handler
func setupTestServer() *httptest.Server {
	mux := http.NewServeMux()
	handler := setupProjectHandler()
	mux.HandleFunc("GET "+previewsSearchPath, handler.HandleGetRandomProject)
	return httptest.NewServer(mux)
}

func TestProjectEndpointIntegration(t *testing.T) {
	server := setupTestServer()
	defer server.Close()

	resp, err := http.Get(fmt.Sprintf("%s%s%s%s", server.URL, previewsSearchPath, "?designId=", uuid.New().String()))
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}
	defer func() {
		if err = resp.Body.Close(); err != nil {
			t.Fatalf("Failed to close response body: %v", err)
		}
	}()

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}

	expectedContentType := "application/json"
	if contentType := resp.Header.Get("Content-Type"); contentType != expectedContentType {
		t.Errorf("Expected content type %s, got %s", expectedContentType, contentType)
	}

	var project map[string]any
	if err := json.NewDecoder(resp.Body).Decode(&project); err != nil {
		t.Fatalf("Failed to decode JSON response: %v", err)
	}

	// Validate the response structure matches the JSON schema
	requiredFields := []string{"name", "cost_usd", "num_items", "area_sq_ft", "updated_at"}
	for _, field := range requiredFields {
		if _, exists := project[field]; !exists {
			t.Errorf("Required field %s is missing from response", field)
		}
	}

	t.Logf("Generated project: %+v", project)
}

func TestProjectEndpointWrongMethod(t *testing.T) {
	server := setupTestServer()
	defer server.Close()

	resp, err := http.Post(server.URL+previewsSearchPath, "application/json", nil)
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}
	defer func() {
		if err = resp.Body.Close(); err != nil {
			t.Fatalf("Failed to close response body: %v", err)
		}
	}()

	if resp.StatusCode != http.StatusMethodNotAllowed {
		t.Errorf("Expected status code %d for POST request, got %d", http.StatusMethodNotAllowed, resp.StatusCode)
	}
}
