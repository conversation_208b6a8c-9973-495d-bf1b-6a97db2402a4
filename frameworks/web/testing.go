package web

import (
	"bytes"
	"context"
	"encoding/json"
	"log"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestCorrelationContext provides utilities for testing correlation context
type TestCorrelationContext struct {
	RequestID     string
	CorrelationID string
	UserID        string
	SessionID     string
}

// NewTestCorrelationContext creates a test correlation context with predictable IDs
func NewTestCorrelationContext() *TestCorrelationContext {
	return &TestCorrelationContext{
		RequestID:     "req_test123",
		CorrelationID: "corr_test456",
		UserID:        "user_test789",
		SessionID:     "session_testabc",
	}
}

// ToCorrelationContext converts to a real CorrelationContext
func (tcc *TestCorrelationContext) ToCorrelationContext() *CorrelationContext {
	return &CorrelationContext{
		RequestID:     tcc.RequestID,
		CorrelationID: tcc.CorrelationID,
		UserID:        tcc.UserID,
		SessionID:     tcc.SessionID,
	}
}

// WithTestCorrelation adds test correlation context to a context
func WithTestCorrelation(ctx context.Context, tcc *TestCorrelationContext) context.Context {
	return WithCorrelationContext(ctx, tcc.ToCorrelationContext())
}

// LogCapture captures log output for testing
type LogCapture struct {
	buffer *bytes.Buffer
	logger *slog.Logger
}

// NewLogCapture creates a new log capture for testing
func NewLogCapture() *LogCapture {
	var buf bytes.Buffer
	logger := slog.New(slog.NewJSONHandler(&buf, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))
	return &LogCapture{
		buffer: &buf,
		logger: logger,
	}
}

// Logger returns the logger for testing
func (lc *LogCapture) Logger() *slog.Logger {
	return lc.logger
}

// Logs returns all captured log entries as a slice of maps
func (lc *LogCapture) Logs() []map[string]interface{} {
	content := lc.buffer.String()
	if content == "" {
		return []map[string]interface{}{}
	}

	lines := strings.Split(strings.TrimSpace(content), "\n")
	logs := make([]map[string]interface{}, 0, len(lines))

	for _, line := range lines {
		if line == "" {
			continue
		}
		var logEntry map[string]interface{}
		if err := json.Unmarshal([]byte(line), &logEntry); err != nil {
			continue // Skip malformed log entries
		}
		logs = append(logs, logEntry)
	}

	return logs
}

// FindLogsByMessage finds log entries containing the specified message
func (lc *LogCapture) FindLogsByMessage(message string) []map[string]interface{} {
	logs := lc.Logs()
	var matches []map[string]interface{}

	for _, log := range logs {
		if msg, ok := log["msg"].(string); ok && strings.Contains(msg, message) {
			matches = append(matches, log)
		}
	}

	return matches
}

// FindLogsByOperation finds log entries with the specified operation
func (lc *LogCapture) FindLogsByOperation(operation string) []map[string]interface{} {
	logs := lc.Logs()
	var matches []map[string]interface{}

	for _, log := range logs {
		if op, ok := log["operation"].(string); ok && op == operation {
			matches = append(matches, log)
		}
	}

	return matches
}

// AssertLogContains asserts that a log entry contains the expected fields
func (lc *LogCapture) AssertLogContains(t *testing.T, expectedFields map[string]interface{}) {
	t.Helper()
	logs := lc.Logs()

	for _, log := range logs {
		matches := true
		for key, expectedValue := range expectedFields {
			if actualValue, exists := log[key]; !exists || actualValue != expectedValue {
				matches = false
				break
			}
		}
		if matches {
			return // Found a matching log entry
		}
	}

	t.Errorf("Expected log entry with fields %v not found in logs: %v", expectedFields, logs)
}

// AssertCorrelationInLogs asserts that correlation IDs are present in logs
func (lc *LogCapture) AssertCorrelationInLogs(t *testing.T, expectedRequestID, expectedCorrelationID string) {
	t.Helper()
	logs := lc.Logs()

	foundCorrelation := false
	for _, log := range logs {
		requestID, hasRequestID := log["request_id"].(string)
		correlationID, hasCorrelationID := log["correlation_id"].(string)

		if hasRequestID && hasCorrelationID {
			if requestID == expectedRequestID && correlationID == expectedCorrelationID {
				foundCorrelation = true
				break
			}
		}
	}

	assert.True(t, foundCorrelation, "Expected correlation IDs not found in logs")
}

// MockHTTPHandler creates a mock HTTP handler for testing middleware
type MockHTTPHandler struct {
	Called       bool
	CapturedCtx  context.Context
	ResponseCode int
	ResponseBody string
	ShouldPanic  bool
	PanicMessage string
}

// NewMockHTTPHandler creates a new mock HTTP handler
func NewMockHTTPHandler() *MockHTTPHandler {
	return &MockHTTPHandler{
		ResponseCode: http.StatusOK,
		ResponseBody: "OK",
	}
}

// ServeHTTP implements http.Handler
func (m *MockHTTPHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	m.Called = true
	m.CapturedCtx = r.Context()

	if m.ShouldPanic {
		panic(m.PanicMessage)
	}

	w.WriteHeader(m.ResponseCode)
	if m.ResponseBody != "" {
		if _, err := w.Write([]byte(m.ResponseBody)); err != nil {
			log.Printf("failed to write response: %s", err.Error())
		}
	}
}

// TestMiddleware provides utilities for testing middleware
func TestMiddleware(t *testing.T, middleware func(http.Handler) http.Handler, handler *MockHTTPHandler) *httptest.ResponseRecorder {
	t.Helper()

	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	wrappedHandler := middleware(handler)
	wrappedHandler.ServeHTTP(w, req)

	return w
}

// TestMiddlewareWithRequest tests middleware with a custom request
func TestMiddlewareWithRequest(t *testing.T, middleware func(http.Handler) http.Handler, handler *MockHTTPHandler, req *http.Request) *httptest.ResponseRecorder {
	t.Helper()

	w := httptest.NewRecorder()
	wrappedHandler := middleware(handler)
	wrappedHandler.ServeHTTP(w, req)

	return w
}

// AssertCorrelationHeaders asserts that correlation headers are present in the response
func AssertCorrelationHeaders(t *testing.T, w *httptest.ResponseRecorder) {
	t.Helper()

	requestID := w.Header().Get(HeaderRequestID)
	correlationID := w.Header().Get(HeaderCorrelationID)

	assert.NotEmpty(t, requestID, "Request ID header should be present")
	assert.NotEmpty(t, correlationID, "Correlation ID header should be present")
	assert.True(t, strings.HasPrefix(requestID, "req_"), "Request ID should have req_ prefix")
	assert.True(t, strings.HasPrefix(correlationID, "corr_"), "Correlation ID should have corr_ prefix")
}

// AssertCorrelationInContext asserts that correlation context is present in the captured context
func AssertCorrelationInContext(t *testing.T, ctx context.Context) {
	t.Helper()

	corrCtx := GetCorrelationContext(ctx)
	assert.NotEmpty(t, corrCtx.RequestID, "Request ID should be present in context")
	assert.NotEmpty(t, corrCtx.CorrelationID, "Correlation ID should be present in context")
}

// CreateTestRequestWithCorrelation creates a test request with correlation headers
func CreateTestRequestWithCorrelation(method, url string, tcc *TestCorrelationContext) *http.Request {
	req := httptest.NewRequest(method, url, nil)

	if tcc != nil {
		if tcc.CorrelationID != "" {
			req.Header.Set(HeaderCorrelationID, tcc.CorrelationID)
		}
		if tcc.UserID != "" {
			req.Header.Set(HeaderUserID, tcc.UserID)
		}
		if tcc.SessionID != "" {
			req.Header.Set(HeaderSessionID, tcc.SessionID)
		}
	}

	return req
}

// IntegrationTestHelper provides utilities for integration testing with correlation
type IntegrationTestHelper struct {
	LogCapture *LogCapture
	TestCorr   *TestCorrelationContext
	Context    context.Context
}

// NewIntegrationTestHelper creates a new integration test helper
func NewIntegrationTestHelper() *IntegrationTestHelper {
	logCapture := NewLogCapture()
	testCorr := NewTestCorrelationContext()
	ctx := WithTestCorrelation(context.Background(), testCorr)

	return &IntegrationTestHelper{
		LogCapture: logCapture,
		TestCorr:   testCorr,
		Context:    ctx,
	}
}

// AssertOperationLogged asserts that an operation was logged with correlation
func (ith *IntegrationTestHelper) AssertOperationLogged(t *testing.T, operation string) {
	t.Helper()

	logs := ith.LogCapture.FindLogsByOperation(operation)
	require.NotEmpty(t, logs, "Expected operation %s to be logged", operation)

	// Check that correlation IDs are present
	for _, log := range logs {
		assert.Equal(t, ith.TestCorr.RequestID, log["request_id"], "Request ID should match")
		assert.Equal(t, ith.TestCorr.CorrelationID, log["correlation_id"], "Correlation ID should match")
	}
}
