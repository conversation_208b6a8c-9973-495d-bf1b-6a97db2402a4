package web_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const (
	jsonSchemaFilename = "room-design.schema.json"
	projId             = "PRJ-FOOBAR"
)

func setup(t *testing.T) (*web.CommandHandler, *web.DesignQueryHandler, *gateways.FakeRelDb, *gateways.FakeMonolith) {
	t.Helper()
	logger := slog.Default()
	schema := controllers.Schema(jsonSchemaFilename)
	r := gateways.NewFakeRelDb()
	catalog := gateways.NewFakeCatalog()
	designFetcher := usecases.NewDesignRetriever(r)
	projectIdRetriever := usecases.NewProjectIdRetriever(r)
	projectSynthesizer := usecases.NewProjectSynthesizer(r, gateways.NewFakeMonolith())
	ai := gateways.NewFakeLLM()
	generator := usecases.NewDesignGenerator(r, gateways.NewFakeMonolith(), gateways.NewRooms([]byte(`{}`)),
		gateways.NewFakeProductSearch(), ai, logger)
	queryController := controllers.NewDesignRetrievalController(designFetcher, projectIdRetriever, projectSynthesizer, generator)
	readHandler := web.NewDesignQueryHandler(logger, queryController)
	designCreator := usecases.NewDesignCreater(r, r, catalog, ai, logger)
	designUpdater := usecases.NewDesignUpdater(r)
	designSaver := usecases.NewDesignSaver(r, catalog, ai, logger)
	proseRegenerator := usecases.NewDesignProseRegenerator(r, catalog, ai, logger)
	fakeMonolith := gateways.NewFakeMonolith()
	bulkDesignReplacer := usecases.NewBulkDesignReplacer(r, fakeMonolith, logger)
	designDeleter := usecases.NewDesignDeleter(r)
	productSearch := gateways.NewFakeProductSearch()
	designEvolver := usecases.NewDesignEvolver(catalog, productSearch, r, logger)
	cmdController := controllers.NewDesignWriteController(
		designCreator, designUpdater, designSaver, proseRegenerator, bulkDesignReplacer, designDeleter,
		designEvolver, designFetcher, logger)
	writeHandler := web.NewCommandHandler(logger, schema, cmdController, queryController, fakeMonolith)
	return writeHandler, readHandler, r, fakeMonolith
}

func genDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	title := "Test Design"
	return adapters.Design{
		FloorTile: &floorTile,
		Toilet:    &toilet,
		Vanity:    &vanity,
		Faucet:    &faucet,
		Mirror:    &mirror,
		Title:     &title,
	}
}

func TestAddingNewDesign(t *testing.T) {
	writeHandler, readHandler, memStore, _ := setup(t)
	testDesign := genDesign()
	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	req := httptest.NewRequest("POST", fmt.Sprintf("/projects/%s/designs", projId), bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()
	writeHandler.HandlePost(recorder, req)
	if status := recorder.Code; status != http.StatusCreated {
		t.Fatalf("handler returned wrong status code: got %v want %v", status, http.StatusCreated)
	}
	storedDesigns, err := memStore.DesignsForProject(t.Context(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(storedDesigns), 1; got != want {
		t.Fatalf("wrong number of stored designs: got %d want %d", got, want)
	}
	req = httptest.NewRequest("GET", fmt.Sprintf("/projects/%s/designs", projId), nil)
	req.SetPathValue("projectId", projId)
	recorder = httptest.NewRecorder()
	readHandler.HandleListDesignsForProject(recorder, req)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	var designs []adapters.Design
	if err := json.Unmarshal(recorder.Body.Bytes(), &designs); err != nil {
		t.Fatal(err)
	}
	got, err := designs[0].ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	testDesign.ID = got.ID.String()
	want, err := testDesign.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	want.LastUpdated = got.LastUpdated
	want.Title = got.Title
	want.Description = got.Description
	assert.Equal(t, want, got)
}

func TestAddingMinimalDesign(t *testing.T) {
	writeHandler, readHandler, memStore, _ := setup(t)
	testDesign := adapters.Design{}
	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	req := httptest.NewRequest("POST", fmt.Sprintf("/projects/%s/designs", projId), bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()
	writeHandler.HandlePost(recorder, req)
	if status := recorder.Code; status != http.StatusCreated {
		t.Fatalf("handler returned wrong status code: got %v want %v", status, http.StatusCreated)
	}
	storedDesigns, err := memStore.DesignsForProject(t.Context(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(storedDesigns), 1; got != want {
		t.Fatalf("wrong number of stored designs: got %d want %d", got, want)
	}
	req = httptest.NewRequest("GET", fmt.Sprintf("/projects/%s/designs", projId), nil)
	req.SetPathValue("projectId", projId)
	recorder = httptest.NewRecorder()
	readHandler.HandleListDesignsForProject(recorder, req)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	var designs []adapters.Design
	if err := json.Unmarshal(recorder.Body.Bytes(), &designs); err != nil {
		t.Fatal(err)
	}
	if got, want := len(designs), 1; got != want {
		t.Fatalf("wrong number of designs returned: got %d want %d", got, want)
	}
}

func TestChangingDesign(t *testing.T) {
	writeHandler, readHandler, memStore, _ := setup(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	ucDesign, err := testDesign.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	if _, err := memStore.UpsertDesign(t.Context(), ucDesign); err != nil {
		t.Fatal(err)
	}
	vanityWall := usecases.VanityWall
	testDesign.WallpaperPlacement = &vanityWall
	want, err := testDesign.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
	req := httptest.NewRequest("PUT", url, bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder := httptest.NewRecorder()
	writeHandler.HandlePut(recorder, req)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	req = httptest.NewRequest("GET", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder = httptest.NewRecorder()
	readHandler.HandleGetSpecifiedDesign(recorder, req)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	var design adapters.Design
	if err := json.Unmarshal(recorder.Body.Bytes(), &design); err != nil {
		t.Fatal(err)
	}
	got, err := design.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	want.LastUpdated = got.LastUpdated
	want.Title = got.Title
	want.Description = got.Description
	assert.Equal(t, want, got)
}

func TestRemovingDesign(t *testing.T) {
	writeHandler, readHandler, memStore, _ := setup(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	ucDesign, err := testDesign.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	if _, err := memStore.UpsertDesign(t.Context(), ucDesign); err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
	req := httptest.NewRequest("DELETE", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder := httptest.NewRecorder()
	writeHandler.HandleDelete(recorder, req)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	req = httptest.NewRequest("GET", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder = httptest.NewRecorder()
	readHandler.HandleGetSpecifiedDesign(recorder, req)
	if status := recorder.Code; status != http.StatusNotFound {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusNotFound)
	}
}

func TestRetrievingDesignsForMultipleProjects(t *testing.T) {
	const paramName = "ids"
	_, readHandler, memStore, _ := setup(t)
	projId2 := entities.NewProjectId("PRJ-FOOBAR2")
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	ucDesign, err := testDesign.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	if _, err := memStore.UpsertDesign(t.Context(), ucDesign); err != nil {
		t.Fatal(err)
	}
	testDesign2 := genDesign()
	testDesign2.ID = uuid.NewString()
	ucDesign2, err := testDesign2.ToUsecaseDesign(projId2)
	if err != nil {
		t.Fatal(err)
	}
	if _, err := memStore.UpsertDesign(t.Context(), ucDesign2); err != nil {
		t.Fatal(err)
	}
	params := url.Values{}
	params.Add(paramName, fmt.Sprintf("%s,%s", projId, projId2))
	req := httptest.NewRequest("GET", fmt.Sprintf("/projects/designs?%s", params.Encode()), nil)
	recorder := httptest.NewRecorder()
	readHandler.HandleGetAllDesignsForMultipleProjects(recorder, req)
	if status := recorder.Code; status != http.StatusMultiStatus {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusMultiStatus)
	}
	params.Set(paramName, fmt.Sprintf("%s,%s", projId, entities.NewProjectId("PRJ-PHANTOM")))
	req = httptest.NewRequest("GET", fmt.Sprintf("/projects/designs?%s", params.Encode()), nil)
	recorder = httptest.NewRecorder()
	readHandler.HandleGetAllDesignsForMultipleProjects(recorder, req)
	if status := recorder.Code; status != http.StatusMultiStatus {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusMultiStatus)
	}
}

func TestAddingMultipleDesignsForProject(t *testing.T) {
	writeHandler, _, memStore, _ := setup(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	testDesign2 := genDesign()
	testDesign2.ID = uuid.NewString()
	data, err := json.Marshal([]adapters.Design{testDesign, testDesign2})
	if err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("/projects/%s/designs", projId)
	req := httptest.NewRequest("PUT", url, bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()
	writeHandler.HandlePutAllDesignsForProject(recorder, req)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	designs, err := memStore.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(designs), 2; got != want {
		t.Errorf("wrong number of results: got %d want %d", got, want)
	}
}

func TestEvolveDesignsForLayout(t *testing.T) {
	writeHandler, _, memStore, fakeMonolith := setup(t)

	// First, add some designs to the project
	testDesign1 := genDesign()
	testDesign1.ID = uuid.NewString()
	ucDesign1, err := testDesign1.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	if _, err := memStore.UpsertDesign(t.Context(), ucDesign1); err != nil {
		t.Fatal(err)
	}

	testDesign2 := genDesign()
	testDesign2.ID = uuid.NewString()
	ucDesign2, err := testDesign2.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	if _, err := memStore.UpsertDesign(t.Context(), ucDesign2); err != nil {
		t.Fatal(err)
	}

	// Create test payload with layout and measurements
	payload := map[string]any{
		"scan": map[string]any{
			"floors": []map[string]any{
				{
					"identifier": uuid.NewString(),
					"position":   map[string]float64{"x": 0, "y": 0, "z": 0},
					"rotation":   map[string]float64{"x": 0, "y": 0, "z": 0},
					"shape": []map[string]float64{
						{"x": 0, "y": 0},
						{"x": 10, "y": 0},
						{"x": 10, "y": 10},
						{"x": 0, "y": 10},
					},
				},
			},
			"walls":         []map[string]any{},
			"ceilings":      []map[string]any{},
			"doors":         []map[string]any{},
			"lights":        []map[string]any{},
			"linenCabinets": []map[string]any{},
			"mirrors":       []map[string]any{},
			"niches":        []map[string]any{},
			"openings":      []map[string]any{},
			"robeHooks":     []map[string]any{},
			"shelves":       []map[string]any{},
			"showerSystems": []map[string]any{},
			"sinks":         []map[string]any{},
			"toilets":       []map[string]any{},
			"towelBars":     []map[string]any{},
			"towelRings":    []map[string]any{},
			"tpHolders":     []map[string]any{},
			"tubFillers":    []map[string]any{},
			"tubs":          []map[string]any{},
			"vanities":      []map[string]any{},
			"windows":       []map[string]any{},
			"areas": map[string]any{
				"showers": []map[string]any{},
			},
		},
		"measurements": map[string]any{
			"floorArea": 100.0,
		},
	}

	data, err := json.Marshal(payload)
	if err != nil {
		t.Fatal(err)
	}

	url := fmt.Sprintf("/projects/%s/designs/evolve", projId)
	req := httptest.NewRequest("POST", url, bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()

	writeHandler.EvolveProjectDesignsForNewLayout(recorder, req)

	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v, body: %s",
			status, http.StatusOK, recorder.Body.String())
	}

	// Verify that we get back evolved designs
	var evolvedDesigns []adapters.Design
	if err := json.Unmarshal(recorder.Body.Bytes(), &evolvedDesigns); err != nil {
		t.Fatal(err)
	}

	// We should get back the same number of designs that were evolved
	if got, want := len(evolvedDesigns), 2; got != want {
		t.Errorf("wrong number of evolved designs: got %d want %d", got, want)
	}

	// Verify that the FakeMonolith received the correct call with valid JSON
	calls := fakeMonolith.GetUpdateScanAndMeasurementsCalls()
	if got, want := len(calls), 1; got != want {
		t.Errorf("expected %d call to UpdateScanAndMeasurementsForProject, got %d", want, got)
	}

	if len(calls) > 0 {
		call := calls[0]
		if got, want := call.ProjectId, entities.ProjectId(projId); got != want {
			t.Errorf("wrong project ID in monolith call: got %s want %s", got, want)
		}

		// Verify the JSON was captured correctly by unmarshaling it
		var capturedScan map[string]any
		if err := json.Unmarshal(call.Scan, &capturedScan); err != nil {
			t.Errorf("failed to unmarshal captured scan JSON: %v", err)
		} else {
			if _, exists := capturedScan["floors"]; !exists {
				t.Error("captured scan JSON missing 'floors' field")
			}
		}

		var capturedMeasurements map[string]any
		if err := json.Unmarshal(call.Measurements, &capturedMeasurements); err != nil {
			t.Errorf("failed to unmarshal captured measurements JSON: %v", err)
		} else {
			if floorArea, exists := capturedMeasurements["floorArea"]; !exists {
				t.Error("captured measurements JSON missing 'floorArea' field")
			} else if got, want := floorArea, 100.0; got != want {
				t.Errorf("wrong floorArea in captured measurements: got %v want %v", got, want)
			}
		}
	}
}

func TestHandleGetProjectIdForDesign_Integration(t *testing.T) {
	// Setup
	fakeDb := gateways.NewFakeRelDb()
	designRetriever := usecases.NewDesignRetriever(fakeDb)
	projectIdRetriever := usecases.NewProjectIdRetriever(fakeDb)

	// Create test data
	projectId := entities.NewProjectId("PRJ-12345")
	designId := uuid.New()
	testDesign := usecases.Design{
		ID:                 designId,
		ProjectID:          projectId,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
	}

	// Insert test design into fake database
	_, err := fakeDb.UpsertDesign(context.Background(), testDesign)
	assert.NoError(t, err)

	// Create handler
	designGenerator := usecases.NewDesignGenerator(fakeDb, gateways.NewFakeMonolith(), gateways.NewRooms([]byte(`{}`)),
		gateways.NewFakeProductSearch(), gateways.NewFakeLLM(), nil)
	projSynth := usecases.NewProjectSynthesizer(fakeDb, gateways.NewFakeMonolith())
	controller := controllers.NewDesignRetrievalController(designRetriever, projectIdRetriever, projSynth, designGenerator)
	handler := web.NewDesignQueryHandler(nil, controller)

	t.Run("should return project ID for valid design ID", func(t *testing.T) {
		// Create request
		req := httptest.NewRequest(http.MethodGet, "/designs/"+designId.String()+"/project", nil)
		req.SetPathValue("designId", designId.String())
		recorder := httptest.NewRecorder()

		// Execute
		handler.HandleGetProjectIdForDesign(recorder, req)

		// Assert
		assert.Equal(t, http.StatusOK, recorder.Code)
		assert.Equal(t, "application/json", recorder.Header().Get("Content-Type"))
		assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"))
		assert.Contains(t, recorder.Body.String(), `"projectId":"PRJ-12345"`)
	})

	t.Run("should return 400 for invalid design ID", func(t *testing.T) {
		// Create request with invalid UUID
		req := httptest.NewRequest(http.MethodGet, "/designs/invalid-uuid/project", nil)
		req.SetPathValue("designId", "invalid-uuid")
		recorder := httptest.NewRecorder()

		// Execute
		handler.HandleGetProjectIdForDesign(recorder, req)

		// Assert
		assert.Equal(t, http.StatusBadRequest, recorder.Code)
		assert.Contains(t, recorder.Body.String(), "Invalid design UUID")
	})

	t.Run("should return 404 for non-existent design ID", func(t *testing.T) {
		// Create request with valid but non-existent UUID
		nonExistentId := uuid.New()
		req := httptest.NewRequest(http.MethodGet, "/designs/"+nonExistentId.String()+"/project", nil)
		req.SetPathValue("designId", nonExistentId.String())
		recorder := httptest.NewRecorder()

		// Execute
		handler.HandleGetProjectIdForDesign(recorder, req)

		// Assert
		assert.Equal(t, http.StatusNotFound, recorder.Code)
		assert.Contains(t, recorder.Body.String(), usecases.ErrNotFound.Error())
	})
}
