package web

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strings"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
)

type RenditionQueryHandler struct {
	logger     *slog.Logger
	controller *controllers.RenditionRetrievalController
	baseCdnURL string
}

func NewRenditionQueryHandler(l *slog.Logger, c *controllers.RenditionRetrievalController, baseCdnHostname string) *RenditionQueryHandler {
	if c == nil {
		panic("controller cannot be nil")
	}
	if baseCdnHostname == "" {
		baseCdnHostname = "cdn.arcstudio.ai"
	}
	if l == nil {
		l = slog.Default()
	}
	return &RenditionQueryHandler{logger: l, controller: c, baseCdnURL: "https://" + baseCdnHostname}
}

func (h *RenditionQueryHandler) HandleGetRenditionsById(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	ids := r.URL.Query().Get("ids")
	if ids == "" {
		http.Error(w, "No rendition IDs provided", http.StatusBadRequest)
		return
	}
	idList := strings.SplitSeq(ids, ",")
	uuidList := []uuid.UUID{}
	for id := range idList {
		uuid, err := uuid.Parse(id)
		if err != nil {
			http.Error(w, "Invalid rendition ID", http.StatusBadRequest)
			return
		}
		uuidList = append(uuidList, uuid)
	}
	presenter := presenters.NewRenditionsPresenter(h.logger, h.baseCdnURL, w)
	h.controller.FetchRenditions(ctx, uuidList, presenter)
}
func (h *RenditionQueryHandler) HandleGetAllRenditionsForDesign(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	design_id := r.PathValue("designId")
	designUUID, err := uuid.Parse(design_id)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}
	presenter := presenters.NewRenditionsPresenter(h.logger, h.baseCdnURL, w)
	h.controller.FetchRenditionsForDesign(ctx, designUUID, presenter)
}

type RenditionWriteHandler struct {
	logger     *slog.Logger
	controller *controllers.RenditionWriteController
}

func NewRenditionWriteHandler(l *slog.Logger, c *controllers.RenditionWriteController) *RenditionWriteHandler {
	if c == nil {
		panic("controller cannot be nil")
	}
	if l == nil {
		l = slog.Default()
	}
	return &RenditionWriteHandler{logger: l, controller: c}
}

func (h *RenditionWriteHandler) HandlePost(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error reading request body: %s", err.Error()), http.StatusBadRequest)
		return
	}
	if len(body) == 0 {
		http.Error(w, "Empty request body", http.StatusBadRequest)
		return
	}
	rendition := adapters.Rendition{}
	err = json.Unmarshal(body, &rendition)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error parsing payload: %s", err.Error()), http.StatusBadRequest)
		return
	}
	presenter := presenters.NewRenditionCreationOutcomePresenter(h.logger, w, designUUID)
	h.controller.AddRendition(ctx, designUUID, rendition, presenter)
}

func (h *RenditionWriteHandler) HandlePut(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error reading request body: %s", err.Error()), http.StatusBadRequest)
		return
	}
	if len(body) == 0 {
		http.Error(w, "Empty request body", http.StatusBadRequest)
		return
	}
	rendition := adapters.Rendition{}
	err = json.Unmarshal(body, &rendition)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error parsing payload: %s", err.Error()), http.StatusBadRequest)
		return
	}
	if err := rendition.AlignId(r.PathValue("renditionId")); err != nil {
		http.Error(w, "Rendition ID in payload does not match URL", http.StatusBadRequest)
		return
	}
	presenter := presenters.NewOutcomePresenter(h.logger, w)
	h.controller.SaveRendition(ctx, designUUID, rendition, presenter)
}

func (h *RenditionWriteHandler) HandlePatch(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error reading request body: %s", err.Error()), http.StatusBadRequest)
		return
	}
	if len(body) == 0 {
		http.Error(w, "Empty request body", http.StatusBadRequest)
		return
	}
	rendition := adapters.Rendition{}
	err = json.Unmarshal(body, &rendition)
	if err != nil {
		h.logger.ErrorContext(ctx, "Error parsing payload", slog.String("error", err.Error()))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	if err := rendition.AlignId(r.PathValue("renditionId")); err != nil {
		http.Error(w, "Rendition ID in payload does not match URL", http.StatusBadRequest)
		return
	}
	if rendition.Status == "" {
		http.Error(w, "Rendition status cannot be empty", http.StatusBadRequest)
		return
	}
	presenter := presenters.NewOutcomePresenter(h.logger, w)
	h.controller.UpdateRendition(ctx, rendition, presenter)
}

func (h *RenditionWriteHandler) HandleDelete(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	renditionId := r.PathValue("renditionId")
	renditionUUID, err := uuid.Parse(renditionId)
	if err != nil {
		http.Error(w, "Invalid rendition UUID", http.StatusBadRequest)
		return
	}
	presenter := presenters.NewOutcomePresenter(h.logger, w)
	h.controller.DeleteRendition(ctx, renditionUUID, presenter)
}
