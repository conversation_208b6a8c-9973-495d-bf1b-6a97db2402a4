package web

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
)

type PresetQueryHandler struct {
	logger            *slog.Logger
	defaultRoomLayout json.RawMessage
	presetController  *controllers.PresetRetrievalController
}

func NewPresetQueryHandler(logger *slog.Logger, defaultRoomLayout json.RawMessage, pc *controllers.PresetRetrievalController) *PresetQueryHandler {
	if pc == nil {
		panic("preset controller cannot be nil")
	}
	if defaultRoomLayout == nil {
		panic("default room layout cannot be empty")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &PresetQueryHandler{logger: logger, defaultRoomLayout: defaultRoomLayout, presetController: pc}
}

func (h *PresetQueryHandler) HandleGetPreset(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	id := r.PathValue("id")
	pp := presenters.NewPresetPresenter(w, h.logger, h.defaultRoomLayout, json.RawMessage(gateways.DefaultMeasurements))
	switch len(id) {
	case 2: // Legacy ID
		h.presetController.FetchPresetByLegacyId(ctx, id, pp)
	default:
		http.Error(w, fmt.Sprintf("Invalid preset ID: %s", id), http.StatusBadRequest)
		return
	}
}
