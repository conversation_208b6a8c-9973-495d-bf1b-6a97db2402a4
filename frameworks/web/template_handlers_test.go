package web_test

import (
	"bytes"
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"

	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FakeMonolith is a fake implementation of the monolith interface for testing
type FakeMonolith struct {
	LayoutToReturn           entities.RoomLayout
	GetLayoutForProjectCalls []entities.ProjectId
}

func (f *FakeMonolith) UpdateCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID) error {
	return nil
}

func (f *FakeMonolith) GetLayoutForProject(ctx context.Context, projectId entities.ProjectId) (entities.RoomLayout, error) {
	f.GetLayoutForProjectCalls = append(f.GetLayoutForProjectCalls, projectId)
	return f.LayoutToReturn, nil
}

func NewFakeMonolith() *FakeMonolith {
	return &FakeMonolith{
		GetLayoutForProjectCalls: make([]entities.ProjectId, 0),
	}
}

func setupTemplateQueryHandler(t *testing.T) (*web.TemplateQueryHandler, *gateways.FakeRelDb, *FakeMonolith) {
	t.Helper()
	logger := slog.Default()
	db := gateways.NewFakeRelDb()
	monolith := NewFakeMonolith()

	retriever := usecases.NewTemplateRetriever(db)
	rooms := gateways.NewRooms([]byte(`{}`))
	ai := gateways.NewFakeLLM()
	productSearch := gateways.NewFakeProductSearch()
	generator := usecases.NewDesignGenerator(db, monolith, rooms, productSearch, ai, logger)
	controller := controllers.NewTemplateRetrievalController(retriever, generator)
	handler := web.NewTemplateQueryHandler(logger, controller)

	return handler, db, monolith
}

// Tests for TemplateWriteHandler error cases
func TestTemplateWriteHandler_ErrorCases(t *testing.T) {
	logger := slog.Default()
	// Create a handler with logger but no controller for testing error cases
	handler := web.NewTemplateWriteHandler(logger, nil)

	templateId := uuid.New()

	t.Run("should return 400 for invalid template UUID", func(t *testing.T) {
		req := httptest.NewRequest("PUT", "/templates/invalid-uuid", bytes.NewReader([]byte("{}")))
		req.SetPathValue("templateId", "invalid-uuid")
		w := httptest.NewRecorder()

		handler.HandlePutTemplate(w, req)

		if w.Code != http.StatusBadRequest {
			t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
		}
		if !bytes.Contains(w.Body.Bytes(), []byte("Invalid template UUID")) {
			t.Errorf("Expected error message about invalid UUID, got: %s", w.Body.String())
		}
	})

	t.Run("should return 400 for empty request body", func(t *testing.T) {
		req := httptest.NewRequest("PUT", "/templates/"+templateId.String(), bytes.NewReader([]byte{}))
		req.SetPathValue("templateId", templateId.String())
		w := httptest.NewRecorder()

		handler.HandlePutTemplate(w, req)

		if w.Code != http.StatusBadRequest {
			t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
		}
		if !bytes.Contains(w.Body.Bytes(), []byte("empty request body")) {
			t.Errorf("Expected error message about empty body, got: %s", w.Body.String())
		}
	})

	t.Run("should return 400 for invalid JSON", func(t *testing.T) {
		req := httptest.NewRequest("PUT", "/templates/"+templateId.String(), bytes.NewReader([]byte("invalid json")))
		req.SetPathValue("templateId", templateId.String())
		w := httptest.NewRecorder()

		handler.HandlePutTemplate(w, req)

		if w.Code != http.StatusBadRequest {
			t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
		}
		if !bytes.Contains(w.Body.Bytes(), []byte("invalid JSON payload")) {
			t.Errorf("Expected error message about invalid JSON, got: %s", w.Body.String())
		}
	})
}

func TestTemplateQueryHandler_HandleGenerateDesignsFromTemplates(t *testing.T) {
	handler, store, monolith := setupTemplateQueryHandler(t)

	// Setup a template in the store
	templateId := uuid.New()
	template := usecases.Template{
		ID:   templateId,
		Name: "My Awesome Design",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Bold,
			Style:       usecases.Modern,
		},
	}
	_, err := store.InsertTemplate(context.Background(), template, "")
	require.NoError(t, err)

	// Setup fake monolith to return a layout
	monolith.LayoutToReturn = entities.RoomLayout{Id: uuid.New()}

	projectId := "PRJ-XYZ-789"
	url := fmt.Sprintf("/templates/projects/%s/generate-designs?templateIds=%s", projectId, templateId)
	req := httptest.NewRequest("GET", url, nil)
	w := httptest.NewRecorder()
	req.SetPathValue("projectId", projectId)

	handler.HandleGenerateDesignsFromTemplates(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), `"title":"My Awesome Design"`)
	assert.Contains(t, w.Body.String(), `"colorScheme":"Bold"`)
	assert.Len(t, monolith.GetLayoutForProjectCalls, 1)
	assert.Equal(t, entities.ProjectId(projectId), monolith.GetLayoutForProjectCalls[0])
}

func TestTemplateQueryHandler_HandleGenerateDesignsFromTemplates_ErrorCases(t *testing.T) {
	handler, _, _ := setupTemplateQueryHandler(t)

	t.Run("should return 400 for missing project ID in path", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/templates/projects//generate-designs?templateIds="+uuid.NewString(), nil)
		req.SetPathValue("projectId", "")
		w := httptest.NewRecorder()
		handler.HandleGenerateDesignsFromTemplates(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "projectId is required in path")
	})

	t.Run("should return 400 for missing template IDs", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/templates/projects/PRJ-123/generate-designs", nil)
		req.SetPathValue("projectId", "PRJ-123")
		w := httptest.NewRecorder()
		handler.HandleGenerateDesignsFromTemplates(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "templateIds are required")
	})

	t.Run("should return 400 for invalid template UUID", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/templates/projects/PRJ-123/generate-designs?templateIds=not-a-uuid", nil)
		req.SetPathValue("projectId", "PRJ-123")
		w := httptest.NewRecorder()
		handler.HandleGenerateDesignsFromTemplates(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "Invalid template UUID")
	})
}
