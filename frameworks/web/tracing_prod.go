//go:build !dev

package web

import (
	"log"

	httptrace "github.com/DataDog/dd-trace-go/contrib/net/http/v2"
	"github.com/DataDog/dd-trace-go/v2/ddtrace/tracer"
)

// InitTracing initializes DataDog tracing for production builds
func InitTracing() {
	s := tracer.NewRateSampler(0.25)
	if err := tracer.Start(tracer.WithSampler(s)); err != nil {
		log.Fatal(err)
	}
}

// StopTracing stops DataDog tracing
func StopTracing() {
	tracer.Stop()
}

// NewServeMux creates a new HTTP ServeMux with DataDog tracing
func NewServeMux() *httptrace.ServeMux {
	return httptrace.NewServeMux()
}
