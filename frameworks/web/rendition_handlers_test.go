package web_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func genRendition() adapters.Rendition {
	url := "https://example.com/rendition.jpg"
	return adapters.Rendition{
		Id:        uuid.New(),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Status:    adapters.RenditionPending,
		URL:       &url,
	}
}

func setupRenditionHandlers(t *testing.T) (*web.RenditionWriteHandler, *web.RenditionQueryHandler, *gateways.FakeRelDb) {
	t.Helper()
	logger := slog.Default()
	fakeDb := gateways.NewFakeRelDb()

	// Setup query handler
	retriever := usecases.NewRenditionRetriever(fakeDb)
	queryController := controllers.NewRenditionRetrievalController(retriever)
	queryHandler := web.NewRenditionQueryHandler(logger, queryController, "")

	// Setup write handler
	catalog := gateways.NewFakeCatalog()
	ai := gateways.NewFakeLLM()
	creator := usecases.NewRenditionCreator(fakeDb, logger)
	saver := usecases.NewRenditionSaver(fakeDb)
	updater := usecases.NewRenditionUpdater(fakeDb, ai, fakeDb, catalog, logger)
	deleter := usecases.NewRenditionDeleter(fakeDb)
	writeController := controllers.NewRenditionWriteController(logger, creator, saver, updater, deleter)
	writeHandler := web.NewRenditionWriteHandler(logger, writeController)

	return writeHandler, queryHandler, fakeDb
}

func TestRenditionWriteHandler_HandlePost(t *testing.T) {
	writeHandler, _, fakeDb := setupRenditionHandlers(t)
	testRendition := genRendition()
	// Keep the ID for the fake DB - in real implementation, DB would generate it
	designId := uuid.New()
	testRendition.DesignId = &designId

	data, err := json.Marshal(testRendition)
	require.NoError(t, err)

	req := httptest.NewRequest("POST", fmt.Sprintf("/designs/%s/renditions", designId), bytes.NewReader(data))
	req.SetPathValue("designId", designId.String())
	recorder := httptest.NewRecorder()

	writeHandler.HandlePost(recorder, req)

	if status := recorder.Code; status != http.StatusCreated {
		t.Fatalf("handler returned wrong status code: got %v want %v, body: %s",
			status, http.StatusCreated, recorder.Body.String())
	}

	// Verify the rendition was stored by fetching it
	storedRenditions, err := fakeDb.RenditionsForDesign(context.Background(), designId)
	require.NoError(t, err)
	require.Len(t, storedRenditions, 1)

	storedRendition := storedRenditions[0]
	// Convert domain status to adapter status for comparison
	expectedStatus := adapters.RenditionStatus(strings.ToLower(string(storedRendition.Status)))
	require.Equal(t, expectedStatus, testRendition.Status)
	require.NotEqual(t, uuid.UUID{}, storedRendition.Id) // Should have generated an ID
}

func TestRenditionWriteHandler_HandlePut(t *testing.T) {
	writeHandler, _, fakeDb := setupRenditionHandlers(t)
	designId := uuid.New()
	testRendition := genRendition()
	testRendition.DesignId = &designId

	// Update the rendition (PUT is for upsert, so no need to pre-populate)
	newUrl := "https://example.com/updated-rendition.jpg"
	testRendition.URL = &newUrl
	testRendition.Status = adapters.RenditionCompleted

	data, err := json.Marshal(testRendition)
	require.NoError(t, err)

	url := fmt.Sprintf("/designs/%s/renditions/%s", designId, testRendition.Id)
	req := httptest.NewRequest("PUT", url, bytes.NewReader(data))
	req.SetPathValue("designId", designId.String())
	req.SetPathValue("renditionId", testRendition.Id.String())
	recorder := httptest.NewRecorder()

	writeHandler.HandlePut(recorder, req)

	if status := recorder.Code; status != http.StatusNoContent {
		t.Fatalf("handler returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}

	// Verify the rendition was created/updated by fetching it
	storedRenditions, err := fakeDb.RenditionsForDesign(context.Background(), designId)
	require.NoError(t, err)
	require.Len(t, storedRenditions, 1)

	storedRendition := storedRenditions[0]
	require.Equal(t, newUrl, storedRendition.URL.String())
	require.Equal(t, entities.RenditionCompleted, storedRendition.Status)
}

func TestRenditionWriteHandler_HandlePatch(t *testing.T) {
	writeHandler, _, fakeDb := setupRenditionHandlers(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	ucDesign, err := testDesign.ToUsecaseDesign(projId)
	require.NoError(t, err)
	designId, err := fakeDb.UpsertDesign(context.Background(), ucDesign)
	require.NoError(t, err)
	testRendition := genRendition()
	testRendition.DesignId = &designId

	// Pre-populate the fake DB with a rendition
	domainRendition, err := testRendition.ToDomain()
	require.NoError(t, err)
	_, err = fakeDb.InsertRendition(context.Background(), designId, domainRendition)
	require.NoError(t, err)

	// Update only the status - need to include URL for completed status
	completedUrl := "https://example.com/completed-rendition.jpg"
	patchRendition := adapters.Rendition{
		Id:     testRendition.Id,
		Status: adapters.RenditionCompleted,
		URL:    &completedUrl,
	}

	data, err := json.Marshal(patchRendition)
	require.NoError(t, err)

	url := fmt.Sprintf("/renditions/%s", testRendition.Id)
	req := httptest.NewRequest("PATCH", url, bytes.NewReader(data))
	req.SetPathValue("renditionId", testRendition.Id.String())
	recorder := httptest.NewRecorder()

	writeHandler.HandlePatch(recorder, req)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Fatalf("handler returned wrong status code: got %v want %v, body: %s",
			status, http.StatusNoContent, recorder.Body.String())
	}

	// Verify the rendition was updated
	storedRenditions, err := fakeDb.RenditionsForDesign(context.Background(), designId)
	require.NoError(t, err)
	require.Len(t, storedRenditions, 1)

	storedRendition := storedRenditions[0]
	require.Equal(t, entities.RenditionCompleted, storedRendition.Status)
	require.Equal(t, completedUrl, storedRendition.URL.String())
}

func TestRenditionWriteHandler_HandleDelete(t *testing.T) {
	writeHandler, _, fakeDb := setupRenditionHandlers(t)
	designId := uuid.New()
	testRendition := genRendition()
	testRendition.DesignId = &designId

	// Pre-populate the fake DB with a rendition
	domainRendition, err := testRendition.ToDomain()
	require.NoError(t, err)
	_, err = fakeDb.InsertRendition(context.Background(), designId, domainRendition)
	require.NoError(t, err)

	url := fmt.Sprintf("/renditions/%s", testRendition.Id)
	req := httptest.NewRequest("DELETE", url, nil)
	req.SetPathValue("renditionId", testRendition.Id.String())
	recorder := httptest.NewRecorder()

	writeHandler.HandleDelete(recorder, req)

	if status := recorder.Code; status != http.StatusNoContent {
		t.Fatalf("handler returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}

	// Verify the rendition was deleted
	storedRenditions, err := fakeDb.RenditionsForDesign(context.Background(), designId)
	require.NoError(t, err)
	require.Len(t, storedRenditions, 0)
}

func TestRenditionQueryHandler_HandleGetRenditionsById(t *testing.T) {
	_, queryHandler, fakeDb := setupRenditionHandlers(t)
	designId := uuid.New()

	// Create multiple renditions
	testRendition1 := genRendition()
	testRendition1.DesignId = &designId
	testRendition2 := genRendition()
	testRendition2.DesignId = &designId

	// Pre-populate the fake DB
	domainRendition1, err := testRendition1.ToDomain()
	require.NoError(t, err)
	_, err = fakeDb.InsertRendition(context.Background(), designId, domainRendition1)
	require.NoError(t, err)

	domainRendition2, err := testRendition2.ToDomain()
	require.NoError(t, err)
	_, err = fakeDb.InsertRendition(context.Background(), designId, domainRendition2)
	require.NoError(t, err)

	// Query for both renditions
	idsParam := fmt.Sprintf("%s,%s", testRendition1.Id, testRendition2.Id)
	req := httptest.NewRequest("GET", fmt.Sprintf("/renditions?ids=%s", idsParam), nil)
	recorder := httptest.NewRecorder()

	queryHandler.HandleGetRenditionsById(recorder, req)

	if status := recorder.Code; status != http.StatusOK {
		t.Fatalf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	// Parse response
	var response map[string][]adapters.Rendition
	err = json.Unmarshal(recorder.Body.Bytes(), &response)
	require.NoError(t, err)

	renditions, exists := response["data"]
	require.True(t, exists, "Response should have 'data' field")
	require.Len(t, renditions, 2)

	// Verify both renditions are returned
	renditionIds := []uuid.UUID{renditions[0].Id, renditions[1].Id}
	require.Contains(t, renditionIds, testRendition1.Id)
	require.Contains(t, renditionIds, testRendition2.Id)

	// Verify both renditions are returned and belong to the correct design
	for _, rendition := range renditions {
		require.NotNil(t, rendition.DesignId, "DesignId should not be nil")
		require.Equal(t, designId, *rendition.DesignId)
	}
}

func TestRenditionQueryHandler_HandleGetAllRenditionsForDesign(t *testing.T) {
	_, queryHandler, fakeDb := setupRenditionHandlers(t)
	designId := uuid.New()

	// Create multiple renditions for the same design
	testRendition1 := genRendition()
	testRendition1.DesignId = &designId
	testRendition2 := genRendition()
	testRendition2.DesignId = &designId

	// Pre-populate the fake DB
	domainRendition1, err := testRendition1.ToDomain()
	require.NoError(t, err)
	_, err = fakeDb.InsertRendition(context.Background(), designId, domainRendition1)
	require.NoError(t, err)

	domainRendition2, err := testRendition2.ToDomain()
	require.NoError(t, err)
	_, err = fakeDb.InsertRendition(context.Background(), designId, domainRendition2)
	require.NoError(t, err)

	// Query for all renditions for the design
	req := httptest.NewRequest("GET", fmt.Sprintf("/designs/%s/renditions", designId), nil)
	req.SetPathValue("designId", designId.String())
	recorder := httptest.NewRecorder()

	queryHandler.HandleGetAllRenditionsForDesign(recorder, req)

	if status := recorder.Code; status != http.StatusOK {
		t.Fatalf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	// Parse response
	var response map[string][]adapters.Rendition
	err = json.Unmarshal(recorder.Body.Bytes(), &response)
	require.NoError(t, err)

	renditions, exists := response["data"]
	require.True(t, exists, "Response should have 'data' field")
	require.Len(t, renditions, 2)

}

// Integration test that exercises both write and read operations
func TestRenditionHandlers_Integration(t *testing.T) {
	writeHandler, queryHandler, _ := setupRenditionHandlers(t)
	designId := uuid.New()

	// Step 1: Create a rendition
	testRendition := genRendition()
	// Keep the ID for the fake DB - in real implementation, DB would generate it
	testRendition.DesignId = &designId

	data, err := json.Marshal(testRendition)
	require.NoError(t, err)

	req := httptest.NewRequest("POST", fmt.Sprintf("/designs/%s/renditions", designId), bytes.NewReader(data))
	req.SetPathValue("designId", designId.String())
	recorder := httptest.NewRecorder()

	writeHandler.HandlePost(recorder, req)
	require.Equal(t, http.StatusCreated, recorder.Code)

	// Step 2: Verify it was created by reading all renditions for the design
	req = httptest.NewRequest("GET", fmt.Sprintf("/designs/%s/renditions", designId), nil)
	req.SetPathValue("designId", designId.String())
	recorder = httptest.NewRecorder()

	queryHandler.HandleGetAllRenditionsForDesign(recorder, req)
	require.Equal(t, http.StatusOK, recorder.Code)

	var response map[string][]adapters.Rendition
	err = json.Unmarshal(recorder.Body.Bytes(), &response)
	require.NoError(t, err)

	renditions, exists := response["data"]
	require.True(t, exists)
	require.Len(t, renditions, 1)

	createdRendition := renditions[0]
	require.Equal(t, testRendition.Status, createdRendition.Status)
	require.Equal(t, designId, *createdRendition.DesignId)

	// Step 3: Update the rendition status
	createdRendition.Status = adapters.RenditionCompleted
	data, err = json.Marshal(createdRendition)
	require.NoError(t, err)

	url := fmt.Sprintf("/designs/%s/renditions/%s", designId, createdRendition.Id)
	req = httptest.NewRequest("PUT", url, bytes.NewReader(data))
	req.SetPathValue("designId", designId.String())
	req.SetPathValue("renditionId", createdRendition.Id.String())
	recorder = httptest.NewRecorder()

	writeHandler.HandlePut(recorder, req)
	require.Equal(t, http.StatusNoContent, recorder.Code)

	// Step 4: Verify the update by reading the specific rendition
	req = httptest.NewRequest("GET", fmt.Sprintf("/renditions?ids=%s", createdRendition.Id.String()), nil)
	recorder = httptest.NewRecorder()

	queryHandler.HandleGetRenditionsById(recorder, req)
	require.Equal(t, http.StatusOK, recorder.Code)

	err = json.Unmarshal(recorder.Body.Bytes(), &response)
	require.NoError(t, err)

	renditions, exists = response["data"]
	require.True(t, exists)
	require.Len(t, renditions, 1)

	updatedRendition := renditions[0]
	require.Equal(t, adapters.RenditionCompleted, updatedRendition.Status)
}
