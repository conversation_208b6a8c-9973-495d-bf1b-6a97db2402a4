package web

import (
	"context"
	"io"
	"log"
	"log/slog"
	"net/http"
	"time"
)

// CorrelationHTTPClient wraps http.Client with correlation context propagation
type CorrelationHTTPClient struct {
	client *http.Client
	logger *slog.Logger
}

// NewCorrelationHTTPClient creates a new HTTP client that propagates correlation context
func NewCorrelationHTTPClient(logger *slog.Logger) *CorrelationHTTPClient {
	return &CorrelationHTTPClient{
		client: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &correlationRoundTripper{
				base:   http.DefaultTransport,
				logger: logger,
			},
		},
		logger: logger,
	}
}

// NewCorrelationHTTPClientWithTimeout creates a new HTTP client with custom timeout
func NewCorrelationHTTPClientWithTimeout(timeout time.Duration, logger *slog.Logger) *CorrelationHTTPClient {
	return &CorrelationHTTPClient{
		client: &http.Client{
			Timeout: timeout,
			Transport: &correlationRoundTripper{
				base:   http.DefaultTransport,
				logger: logger,
			},
		},
		logger: logger,
	}
}

// Do executes an HTTP request with correlation context propagation
func (c *CorrelationHTTPClient) Do(req *http.Request) (*http.Response, error) {
	return c.client.Do(req)
}

// Get performs a GET request with correlation context
func (c *CorrelationHTTPClient) Get(ctx context.Context, url string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	return c.Do(req)
}

// Post performs a POST request with correlation context
func (c *CorrelationHTTPClient) Post(ctx context.Context, url, contentType string, body interface{}) (*http.Response, error) {
	// This would need to be implemented based on your body serialization needs
	// For now, just showing the pattern
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", contentType)
	return c.Do(req)
}

// correlationRoundTripper implements http.RoundTripper to add correlation headers
type correlationRoundTripper struct {
	base   http.RoundTripper
	logger *slog.Logger
}

func (rt *correlationRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	// Extract correlation context from request context
	corrCtx := GetCorrelationContext(req.Context())

	// Add correlation headers to outgoing request
	if corrCtx.RequestID != "" {
		req.Header.Set(HeaderRequestID, corrCtx.RequestID)
	}
	if corrCtx.CorrelationID != "" {
		req.Header.Set(HeaderCorrelationID, corrCtx.CorrelationID)
	}
	if corrCtx.UserID != "" {
		req.Header.Set(HeaderUserID, corrCtx.UserID)
	}
	if corrCtx.SessionID != "" {
		req.Header.Set(HeaderSessionID, corrCtx.SessionID)
	}

	// Log outgoing request
	if rt.logger != nil {
		rt.logger.DebugContext(req.Context(), "Outgoing HTTP request",
			slog.String("method", req.Method),
			slog.String("url", req.URL.String()),
			slog.String("request_id", corrCtx.RequestID),
			slog.String("correlation_id", corrCtx.CorrelationID),
		)
	}

	start := time.Now()
	resp, err := rt.base.RoundTrip(req)
	duration := time.Since(start)

	// Log response
	if rt.logger != nil {
		if err != nil {
			rt.logger.ErrorContext(req.Context(), "HTTP request failed",
				slog.String("method", req.Method),
				slog.String("url", req.URL.String()),
				slog.String("error", err.Error()),
				slog.Duration("duration", duration),
				slog.String("request_id", corrCtx.RequestID),
				slog.String("correlation_id", corrCtx.CorrelationID),
			)
		} else {
			rt.logger.DebugContext(req.Context(), "HTTP request completed",
				slog.String("method", req.Method),
				slog.String("url", req.URL.String()),
				slog.Int("status_code", resp.StatusCode),
				slog.Duration("duration", duration),
				slog.String("request_id", corrCtx.RequestID),
				slog.String("correlation_id", corrCtx.CorrelationID),
			)
		}
	}

	return resp, err
}

// HTTPClientConfig holds configuration for HTTP clients
type HTTPClientConfig struct {
	Timeout         time.Duration
	MaxIdleConns    int
	IdleConnTimeout time.Duration
	RetryAttempts   int
	RetryDelay      time.Duration
}

// DefaultHTTPClientConfig returns a default HTTP client configuration
func DefaultHTTPClientConfig() *HTTPClientConfig {
	return &HTTPClientConfig{
		Timeout:         30 * time.Second,
		MaxIdleConns:    100,
		IdleConnTimeout: 90 * time.Second,
		RetryAttempts:   3,
		RetryDelay:      1 * time.Second,
	}
}

// NewConfiguredHTTPClient creates an HTTP client with custom configuration
func NewConfiguredHTTPClient(config *HTTPClientConfig, logger *slog.Logger) *CorrelationHTTPClient {
	if config == nil {
		config = DefaultHTTPClientConfig()
	}

	transport := &http.Transport{
		MaxIdleConns:    config.MaxIdleConns,
		IdleConnTimeout: config.IdleConnTimeout,
	}

	return &CorrelationHTTPClient{
		client: &http.Client{
			Timeout: config.Timeout,
			Transport: &correlationRoundTripper{
				base:   transport,
				logger: logger,
			},
		},
		logger: logger,
	}
}

// RetryableHTTPClient wraps CorrelationHTTPClient with retry logic
type RetryableHTTPClient struct {
	*CorrelationHTTPClient
	maxRetries int
	retryDelay time.Duration
}

// NewRetryableHTTPClient creates a new HTTP client with retry capabilities
func NewRetryableHTTPClient(maxRetries int, retryDelay time.Duration, logger *slog.Logger) *RetryableHTTPClient {
	return &RetryableHTTPClient{
		CorrelationHTTPClient: NewCorrelationHTTPClient(logger),
		maxRetries:            maxRetries,
		retryDelay:            retryDelay,
	}
}

// Do executes an HTTP request with retry logic
func (c *RetryableHTTPClient) Do(req *http.Request) (*http.Response, error) {
	var lastErr error

	for attempt := 0; attempt <= c.maxRetries; attempt++ {
		if attempt > 0 {
			c.logger.WarnContext(req.Context(), "Retrying HTTP request",
				slog.Int("attempt", attempt),
				slog.String("method", req.Method),
				slog.String("url", req.URL.String()),
			)
			time.Sleep(c.retryDelay)
		}

		resp, err := c.CorrelationHTTPClient.Do(req)
		if err == nil && resp.StatusCode < 500 {
			return resp, nil
		}

		lastErr = err
		if resp != nil {
			closeBody(resp.Body)
		}
	}

	return nil, lastErr
}

// RequestBuilder helps build HTTP requests with correlation context
type RequestBuilder struct {
	ctx    context.Context
	logger *slog.Logger
}

// NewRequestBuilder creates a new request builder
func NewRequestBuilder(ctx context.Context, logger *slog.Logger) *RequestBuilder {
	return &RequestBuilder{
		ctx:    ctx,
		logger: logger,
	}
}

// Build creates an HTTP request with correlation headers
func (rb *RequestBuilder) Build(method, url string, body interface{}) (*http.Request, error) {
	req, err := http.NewRequestWithContext(rb.ctx, method, url, nil)
	if err != nil {
		return nil, err
	}

	// Add correlation headers
	corrCtx := GetCorrelationContext(rb.ctx)
	if corrCtx.RequestID != "" {
		req.Header.Set(HeaderRequestID, corrCtx.RequestID)
	}
	if corrCtx.CorrelationID != "" {
		req.Header.Set(HeaderCorrelationID, corrCtx.CorrelationID)
	}
	if corrCtx.UserID != "" {
		req.Header.Set(HeaderUserID, corrCtx.UserID)
	}
	if corrCtx.SessionID != "" {
		req.Header.Set(HeaderSessionID, corrCtx.SessionID)
	}

	return req, nil
}

func closeBody(body io.ReadCloser) {
	if err := body.Close(); err != nil {
		log.Printf("failed to close response body: %s", err.Error())
	}
}
