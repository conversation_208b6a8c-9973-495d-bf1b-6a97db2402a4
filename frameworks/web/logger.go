package web

import (
	"context"
	"log/slog"
	"os"
)

// CorrelationLogger wraps slog.Logger with correlation context awareness
type CorrelationLogger struct {
	*slog.Logger
}

// NewCorrelationLogger creates a new correlation-aware logger
func NewCorrelationLogger(logger *slog.Logger) *CorrelationLogger {
	if logger == nil {
		logger = slog.Default()
	}
	return &CorrelationLogger{Logger: logger}
}

// WithCorrelation creates a logger with correlation context attributes
func (cl *CorrelationLogger) WithCorrelation(ctx context.Context) *slog.Logger {
	corrCtx := GetCorrelationContext(ctx)

	args := make([]any, 0, 8)
	if corrCtx.RequestID != "" {
		args = append(args, "request_id", corrCtx.RequestID)
	}
	if corrCtx.CorrelationID != "" {
		args = append(args, "correlation_id", corrCtx.CorrelationID)
	}
	if corrCtx.UserID != "" {
		args = append(args, "user_id", corrCtx.UserID)
	}
	if corrCtx.SessionID != "" {
		args = append(args, "session_id", corrCtx.SessionID)
	}

	return cl.With(args...)
}

// InfoContext logs at Info level with correlation context
func (cl *CorrelationLogger) InfoContext(ctx context.Context, msg string, args ...any) {
	cl.WithCorrelation(ctx).InfoContext(ctx, msg, args...)
}

// ErrorContext logs at Error level with correlation context
func (cl *CorrelationLogger) ErrorContext(ctx context.Context, msg string, args ...any) {
	cl.WithCorrelation(ctx).ErrorContext(ctx, msg, args...)
}

// WarnContext logs at Warn level with correlation context
func (cl *CorrelationLogger) WarnContext(ctx context.Context, msg string, args ...any) {
	cl.WithCorrelation(ctx).WarnContext(ctx, msg, args...)
}

// DebugContext logs at Debug level with correlation context
func (cl *CorrelationLogger) DebugContext(ctx context.Context, msg string, args ...any) {
	cl.WithCorrelation(ctx).DebugContext(ctx, msg, args...)
}

// LoggerConfig holds configuration for logger setup
type LoggerConfig struct {
	Level      slog.Level
	Format     string // "text" or "json"
	AddSource  bool
	TimeFormat string
}

// DefaultLoggerConfig returns a default logger configuration
func DefaultLoggerConfig() *LoggerConfig {
	return &LoggerConfig{
		Level:      slog.LevelInfo,
		Format:     "json",
		AddSource:  false,
		TimeFormat: "",
	}
}

// NewStructuredLogger creates a new structured logger with the given configuration
func NewStructuredLogger(config *LoggerConfig) *slog.Logger {
	if config == nil {
		config = DefaultLoggerConfig()
	}

	opts := &slog.HandlerOptions{
		Level:     config.Level,
		AddSource: config.AddSource,
	}

	// Custom time format if specified
	if config.TimeFormat != "" {
		opts.ReplaceAttr = func(groups []string, a slog.Attr) slog.Attr {
			if a.Key == slog.TimeKey {
				return slog.Attr{}
			}
			return a
		}
	}

	var handler slog.Handler
	switch config.Format {
	case "text":
		handler = slog.NewTextHandler(os.Stderr, opts)
	default:
		handler = slog.NewJSONHandler(os.Stderr, opts)
	}

	logger := slog.New(handler)
	slog.SetDefault(logger)
	return logger
}

// LoggerFromContext extracts a correlation-aware logger from context
func LoggerFromContext(ctx context.Context, baseLogger *slog.Logger) *slog.Logger {
	if baseLogger == nil {
		baseLogger = slog.Default()
	}

	corrCtx := GetCorrelationContext(ctx)
	if corrCtx.RequestID == "" && corrCtx.CorrelationID == "" {
		return baseLogger
	}

	args := make([]any, 0, 8)
	if corrCtx.RequestID != "" {
		args = append(args, "request_id", corrCtx.RequestID)
	}
	if corrCtx.CorrelationID != "" {
		args = append(args, "correlation_id", corrCtx.CorrelationID)
	}
	if corrCtx.UserID != "" {
		args = append(args, "user_id", corrCtx.UserID)
	}
	if corrCtx.SessionID != "" {
		args = append(args, "session_id", corrCtx.SessionID)
	}

	return baseLogger.With(args...)
}

// OperationLogger provides structured logging for operations with correlation context
type OperationLogger struct {
	logger    *slog.Logger
	operation string
	ctx       context.Context
}

// NewOperationLogger creates a new operation logger
func NewOperationLogger(ctx context.Context, logger *slog.Logger, operation string) *OperationLogger {
	return &OperationLogger{
		logger:    LoggerFromContext(ctx, logger),
		operation: operation,
		ctx:       ctx,
	}
}

// Start logs the beginning of an operation
func (ol *OperationLogger) Start(msg string, args ...any) {
	allArgs := append([]any{slog.String("operation", ol.operation)}, args...)
	ol.logger.InfoContext(ol.ctx, "Operation started: "+msg, allArgs...)
}

// Success logs successful completion of an operation
func (ol *OperationLogger) Success(msg string, args ...any) {
	allArgs := append([]any{slog.String("operation", ol.operation), slog.String("status", "success")}, args...)
	ol.logger.InfoContext(ol.ctx, "Operation completed: "+msg, allArgs...)
}

// Error logs an error during an operation
func (ol *OperationLogger) Error(msg string, err error, args ...any) {
	allArgs := append([]any{
		slog.String("operation", ol.operation),
		slog.String("status", "error"),
		slog.String("error", err.Error()),
	}, args...)
	ol.logger.ErrorContext(ol.ctx, "Operation failed: "+msg, allArgs...)
}

// Warn logs a warning during an operation
func (ol *OperationLogger) Warn(msg string, args ...any) {
	allArgs := append([]any{slog.String("operation", ol.operation)}, args...)
	ol.logger.WarnContext(ol.ctx, "Operation warning: "+msg, allArgs...)
}

// Debug logs debug information during an operation
func (ol *OperationLogger) Debug(msg string, args ...any) {
	allArgs := append([]any{slog.String("operation", ol.operation)}, args...)
	ol.logger.DebugContext(ol.ctx, "Operation debug: "+msg, allArgs...)
}
