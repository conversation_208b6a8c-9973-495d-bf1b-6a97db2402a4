package web

import (
	"fmt"
	"net/http"
)

func handleOptions(w http.ResponseWriter, _ *http.Request) {
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Origin", "*")
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, PATCH, DELETE, PUT")
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Write<PERSON>eader(http.StatusNoContent)
}

type DesignsReadHandler interface {
	HandleGetSpecifiedDesign(w http.ResponseWriter, r *http.Request)
	HandleListDesignsForProject(w http.ResponseWriter, r *http.Request)
	HandleGetAllDesignsForMultipleProjects(w http.ResponseWriter, r *http.Request)
	HandleGetProjectIdForDesign(w http.ResponseWriter, r *http.Request)
	HandleGenerateDesignViaAI(w http.ResponseWriter, r *http.Request)
}

type DesignsWriteHandler interface {
	HandlePost(w http.ResponseWriter, r *http.Request)
	HandlePut(w http.ResponseWriter, r *http.Request)
	HandlePatch(w http.ResponseWriter, r *http.Request)
	HandleRegenerateProse(w http.ResponseWriter, r *http.Request)
	HandleDelete(w http.ResponseWriter, r *http.Request)
	HandlePutAllDesignsForProject(w http.ResponseWriter, r *http.Request)
	EvolveProjectDesignsForNewLayout(w http.ResponseWriter, r *http.Request)
}

type PresetsReadHandler interface {
	HandleGetPreset(w http.ResponseWriter, r *http.Request)
}

type TemplatesReadHandler interface {
	HandleGetTemplate(w http.ResponseWriter, r *http.Request)
	HandleGetAllTemplates(w http.ResponseWriter, r *http.Request)
	HandleGenerateDesignsFromTemplates(w http.ResponseWriter, r *http.Request)
}

type TemplatesWriteHandler interface {
	HandlePutTemplate(w http.ResponseWriter, r *http.Request)
}

type ServeMux interface {
	HandleFunc(pattern string, handler func(http.ResponseWriter, *http.Request))
}

func RegisterGlobalHandlers(mux ServeMux, readHandler DesignsReadHandler) {
	mux.HandleFunc("/healthz", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})
}

func RegisterPresetHandlers(mux ServeMux, prefix string, readHandler PresetsReadHandler) {
	mux.HandleFunc(fmt.Sprintf("GET %s/presets/{id}", prefix), readHandler.HandleGetPreset)
}

func RegisterTemplateHandlers(mux ServeMux, prefix string, readHandler TemplatesReadHandler, writeHandler TemplatesWriteHandler) {
	mux.HandleFunc(fmt.Sprintf("GET %s/{templateId}", prefix), readHandler.HandleGetTemplate)
	mux.HandleFunc(fmt.Sprintf("GET %s/", prefix), readHandler.HandleGetAllTemplates)
	mux.HandleFunc(fmt.Sprintf("GET %s", prefix), readHandler.HandleGetAllTemplates)
	mux.HandleFunc(fmt.Sprintf("PUT %s/{templateId}", prefix), writeHandler.HandlePutTemplate)
	mux.HandleFunc(fmt.Sprintf("GET %s/projects/{projectId}/designs", prefix), readHandler.HandleGenerateDesignsFromTemplates)
}

func RegisterDesignHandlers(mux ServeMux, prefix string, readHandler DesignsReadHandler, writeHandler DesignsWriteHandler) {
	mux.HandleFunc(fmt.Sprintf("OPTIONS %s/projects/{projectId}/designs/", prefix), handleOptions)
	mux.HandleFunc(fmt.Sprintf("GET %s/projects/{projectId}/designs/{designId}", prefix), readHandler.HandleGetSpecifiedDesign)
	mux.HandleFunc(fmt.Sprintf("GET %s/projects/{projectId}/designs/", prefix), readHandler.HandleListDesignsForProject)
	mux.HandleFunc(fmt.Sprintf("GET %s/projects/{projectId}/designs", prefix), readHandler.HandleListDesignsForProject)
	mux.HandleFunc(fmt.Sprintf("POST %s/projects/{projectId}/designs/", prefix), writeHandler.HandlePost)
	mux.HandleFunc(fmt.Sprintf("POST %s/projects/{projectId}/designs", prefix), writeHandler.HandlePost)
	mux.HandleFunc(fmt.Sprintf("PUT %s/projects/{projectId}/designs/{designId}", prefix), writeHandler.HandlePut)
	mux.HandleFunc(fmt.Sprintf("PUT %s/projects/{projectId}/designs/", prefix), writeHandler.HandlePutAllDesignsForProject)
	mux.HandleFunc(fmt.Sprintf("PUT %s/projects/{projectId}/designs", prefix), writeHandler.HandlePutAllDesignsForProject)
	mux.HandleFunc(fmt.Sprintf("PATCH %s/projects/{projectId}/designs/{designId}", prefix), writeHandler.HandlePatch)
	mux.HandleFunc(fmt.Sprintf("PATCH %s/projects/{projectId}/designs/{designId}/prose", prefix), writeHandler.HandleRegenerateProse)
	mux.HandleFunc(fmt.Sprintf("DELETE %s/projects/{projectId}/designs/{designId}", prefix), writeHandler.HandleDelete)
	mux.HandleFunc(fmt.Sprintf("GET %s/projects/designs", prefix), readHandler.HandleGetAllDesignsForMultipleProjects)
	mux.HandleFunc(fmt.Sprintf("GET %s/lookup/{designId}", prefix), readHandler.HandleGetProjectIdForDesign)
	mux.HandleFunc(fmt.Sprintf("GET %s/projects/{projectId}/designs/ai", prefix), readHandler.HandleGenerateDesignViaAI)
	mux.HandleFunc(fmt.Sprintf("POST %s/projects/{projectId}/scans", prefix), writeHandler.EvolveProjectDesignsForNewLayout)
}

func RegisterRenditionHandlers(mux ServeMux, prefix string, readHandler *RenditionQueryHandler, writeHandler *RenditionWriteHandler) {
	mux.HandleFunc(fmt.Sprintf("GET %s/designs/{designId}/renditions/", prefix), readHandler.HandleGetAllRenditionsForDesign)
	mux.HandleFunc(fmt.Sprintf("GET %s/designs/{designId}/renditions", prefix), readHandler.HandleGetAllRenditionsForDesign)
	mux.HandleFunc(fmt.Sprintf("POST %s/designs/{designId}/renditions/", prefix), writeHandler.HandlePost)
	mux.HandleFunc(fmt.Sprintf("POST %s/designs/{designId}/renditions", prefix), writeHandler.HandlePost)
	mux.HandleFunc(fmt.Sprintf("PUT %s/designs/{designId}/renditions/{renditionId}", prefix), writeHandler.HandlePut)
	mux.HandleFunc(fmt.Sprintf("DELETE %s/designs/{designId}/renditions/{renditionId}", prefix), writeHandler.HandleDelete)
	mux.HandleFunc(fmt.Sprintf("PATCH %s/designs/{designId}/renditions/{renditionId}", prefix), writeHandler.HandlePatch)
	mux.HandleFunc(fmt.Sprintf("PATCH %s/renditions/{renditionId}", prefix), writeHandler.HandlePatch)
	mux.HandleFunc(fmt.Sprintf("DELETE %s/renditions/{renditionId}", prefix), writeHandler.HandleDelete)
	mux.HandleFunc(fmt.Sprintf("GET %s/renditions/", prefix), readHandler.HandleGetRenditionsById)
	mux.HandleFunc(fmt.Sprintf("GET %s/renditions", prefix), readHandler.HandleGetRenditionsById)
}

func RegisterProjectHandlers(mux ServeMux, handler *ProjectHandler) {
	mux.HandleFunc("GET "+path, handler.HandleGetRandomProject)
	mux.HandleFunc("GET /studio/v1/projects/previews/search", handler.HandleGetProject)
}
