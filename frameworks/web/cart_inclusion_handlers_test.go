package web_test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func setupCartInclusionHandlers(t *testing.T) (*web.CartInclusionsQueryHandler, *web.CartInclusionsWriteHandler, *gateways.FakeRelDb) {
	t.Helper()
	fakeDb := gateways.NewFakeRelDb()

	// Create use cases
	saver := usecases.NewCartInclusionReplacer(fakeDb, nil)
	deleter := usecases.NewCartInclusionDeleter(fakeDb, nil)
	merger := usecases.NewCartInclusionMerger(fakeDb, nil)
	retriever := usecases.NewCartInclusionRetriever(fakeDb, nil)

	// Create controllers
	writeController := controllers.NewCartInclusionWriteController(saver, deleter, merger, nil)
	queryController := controllers.NewCartInclusionRetrievalController(retriever)

	// Create handlers
	queryHandler := web.NewCartInclusionQueryHandler(nil, queryController)
	writeHandler := web.NewCartInclusionWriteHandler(nil, writeController)

	return queryHandler, writeHandler, fakeDb
}

func TestCartInclusionQueryHandler_HandleGetCartInclusions(t *testing.T) {
	queryHandler, _, repo := setupCartInclusionHandlers(t)

	// Create a test design first
	ctx := context.Background()
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	inclusion := usecases.CartInclusion{
		ProductID:    uuid.New(),
		Location:     usecases.LocationFloor,
		Include:      true,
		QuantityDiff: 2,
	}

	err = repo.UpsertCartInclusion(ctx, designId, inclusion)
	require.NoError(t, err)

	req := httptest.NewRequest("GET", "/designs/"+designId.String()+"/cart-inclusions", nil)
	req.SetPathValue("designId", designId.String())
	w := httptest.NewRecorder()

	queryHandler.HandleGetAllCartInclusionsForDesign(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
}

func TestCartInclusionWriteHandler_HandlePut(t *testing.T) {
	_, writeHandler, repo := setupCartInclusionHandlers(t)

	// Create a test design first
	ctx := context.Background()
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productId := uuid.New()

	// Create a JSON-serializable format (array of inclusions)
	inclusionsArray := []adapters.CartInclusion{
		{
			ProductID:    productId,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 2,
		},
	}

	body, err := json.Marshal(inclusionsArray)
	require.NoError(t, err)

	req := httptest.NewRequest("PUT", "/designs/"+designId.String()+"/cart-inclusions", bytes.NewReader(body))
	req.SetPathValue("designId", designId.String())
	w := httptest.NewRecorder()

	writeHandler.HandlePut(w, req)

	assert.Equal(t, http.StatusNoContent, w.Code)
}

func TestCartInclusionWriteHandler_HandlePut_MultipleInclusions(t *testing.T) {
	_, writeHandler, repo := setupCartInclusionHandlers(t)

	// Create a test design first
	ctx := context.Background()
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productId1 := uuid.New()
	productId2 := uuid.New()

	// Create a JSON-serializable format (array of inclusions)
	inclusionsArray := []adapters.CartInclusion{
		{
			ProductID:    productId1,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 2,
		},
		{
			ProductID:    productId2,
			Location:     usecases.LocationWall,
			Include:      false,
			QuantityDiff: -1,
		},
	}

	body, err := json.Marshal(inclusionsArray)
	require.NoError(t, err)

	req := httptest.NewRequest("PUT", "/designs/"+designId.String()+"/cart-inclusions", bytes.NewReader(body))
	req.SetPathValue("designId", designId.String())
	w := httptest.NewRecorder()

	writeHandler.HandlePut(w, req)

	assert.Equal(t, http.StatusNoContent, w.Code)
}

func TestCartInclusionWriteHandler_HandleDelete_ClearAll(t *testing.T) {
	_, writeHandler, repo := setupCartInclusionHandlers(t)

	// Create a test design first
	ctx := context.Background()
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	req := httptest.NewRequest("DELETE", "/designs/"+designId.String()+"/cart-inclusions", nil)
	req.SetPathValue("designId", designId.String())
	w := httptest.NewRecorder()

	writeHandler.HandleDelete(w, req)

	assert.Equal(t, http.StatusNoContent, w.Code)
}

func TestCartInclusionWriteHandler_HandleDelete_SingleProduct(t *testing.T) {
	_, writeHandler, repo := setupCartInclusionHandlers(t)

	// Create a test design first
	ctx := context.Background()
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productId := uuid.New()
	location := "Floor"

	req := httptest.NewRequest("DELETE", "/designs/"+designId.String()+"/cart-inclusions/"+productId.String()+"/"+location, nil)
	req.SetPathValue("designId", designId.String())
	req.SetPathValue("productId", productId.String())
	req.SetPathValue("location", location)
	w := httptest.NewRecorder()

	writeHandler.HandleDelete(w, req)

	// Expect error since product doesn't exist, but handler should handle it gracefully
	assert.True(t, w.Code >= 400 || w.Code == 200) // Either error or success is acceptable
}

func TestCartInclusionWriteHandler_HandlePatch(t *testing.T) {
	_, writeHandler, repo := setupCartInclusionHandlers(t)

	// Create a test design first
	ctx := context.Background()
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productId := uuid.New()

	// Create a JSON-serializable format (array of inclusions)
	inclusionsArray := []adapters.CartInclusion{
		{
			ProductID:    productId,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 2,
		},
	}

	body, err := json.Marshal(inclusionsArray)
	require.NoError(t, err)

	req := httptest.NewRequest("PATCH", "/designs/"+designId.String()+"/cart-inclusions", bytes.NewReader(body))
	req.SetPathValue("designId", designId.String())
	w := httptest.NewRecorder()

	writeHandler.HandlePatch(w, req)

	assert.Equal(t, http.StatusNoContent, w.Code)
}
