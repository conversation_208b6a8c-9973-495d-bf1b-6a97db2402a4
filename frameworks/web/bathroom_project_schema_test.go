package web_test

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/santhosh-tekuri/jsonschema/v6"
)

const projectSchema = `{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "title": " reno project summary",
  "type": "object",
  "properties": {
    "name": {
      "type": "string"
    },
    "cost_usd": {
      "type": "number"
    },
    "num_items": {
      "type": "number"
    },
    "area_sq_ft": {
      "type": "number"
    },
    "image_url": {
      "type": "string",
      "format": "url"
    },
    "updated_at": {
      "type": "string",
      "format": "date-time"
    }
  },
  "required": [
    "name",
    "cost_usd",
    "num_items",
    "area_sq_ft",
    "updated_at"
  ],
  "additionalProperties": false
}`

func TestProjectConformsToSchema(t *testing.T) {
	tmpFile := "project-schema.json"
	if err := os.WriteFile(tmpFile, []byte(projectSchema), 0644); err != nil {
		t.Fatalf("Failed to write schema file: %v", err)
	}
	defer func() {
		if err := os.Remove(tmpFile); err != nil {
			t.Fatalf("Failed to remove schema file: %v", err)
		}
	}()

	// Compile the JSON schema
	compiler := jsonschema.NewCompiler()
	schema, err := compiler.Compile(tmpFile)
	if err != nil {
		t.Fatalf("Failed to compile JSON schema: %v", err)
	}

	handler := setupProjectHandler()

	// Test multiple generated projects to ensure consistency
	for i := 0; i < 10; i++ {
		req := httptest.NewRequest("GET", previewsSearchPath+"?designId="+uuid.New().String(), nil)
		w := httptest.NewRecorder()

		handler.HandleGetRandomProject(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("Request %d failed with status %d", i, w.Code)
		}

		// Parse the response as generic interface for schema validation
		var project any
		if err := json.Unmarshal(w.Body.Bytes(), &project); err != nil {
			t.Fatalf("Request %d: Failed to parse JSON response: %v", i, err)
		}

		if err := schema.Validate(project); err != nil {
			t.Errorf("Request %d: Generated project does not conform to schema: %v", i, err)
			t.Errorf("Project data: %s", w.Body.String())
		}
	}
}

func TestProjectFieldValidation(t *testing.T) {
	handler := setupProjectHandler()

	req := httptest.NewRequest("GET", previewsSearchPath+"?designId="+uuid.New().String(), nil)
	w := httptest.NewRecorder()

	handler.HandleGetRandomProject(w, req)
	if w.Code != http.StatusOK {
		t.Fatalf("Request failed with status %d", w.Code)
	}

	var project map[string]any
	if err := json.Unmarshal(w.Body.Bytes(), &project); err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	// Test specific field constraints

	// name should be non-empty string
	if name, ok := project["name"].(string); !ok {
		t.Error("Field 'name' should be a string")
	} else if strings.TrimSpace(name) == "" {
		t.Error("Field 'name' should not be empty")
	}

	// cost_usd should be positive number
	if costUSD, ok := project["cost_usd"].(float64); !ok {
		t.Error("Field 'cost_usd' should be a number")
	} else if costUSD <= 0 {
		t.Error("Field 'cost_usd' should be positive")
	}

	// num_items should be positive integer
	if numItems, ok := project["num_items"].(float64); !ok {
		t.Error("Field 'num_items' should be a number")
	} else if numItems <= 0 || numItems != float64(int(numItems)) {
		t.Error("Field 'num_items' should be a positive integer")
	}

	// area_sq_ft should be positive number
	if areaSqFt, ok := project["area_sq_ft"].(float64); !ok {
		t.Error("Field 'area_sq_ft' should be a number")
	} else if areaSqFt <= 0 {
		t.Error("Field 'area_sq_ft' should be positive")
	}

	// updated_at should be valid ISO 8601 date-time
	if updatedAt, ok := project["updated_at"].(string); !ok {
		t.Error("Field 'updated_at' should be a string")
	} else if !isValidDateTime(updatedAt) {
		t.Errorf("Field 'updated_at' should be valid date-time format, got: %s", updatedAt)
	}

	// image_url should be valid URL if present
	if imageURL, exists := project["image_url"]; exists && imageURL != nil {
		if url, ok := imageURL.(string); !ok {
			t.Error("Field 'image_url' should be a string when present")
		} else if !isValidURL(url) {
			t.Errorf("Field 'image_url' should be valid URL, got: %s", url)
		}
	}
}

func isValidDateTime(dateTime string) bool {
	// Basic check for ISO 8601 format (RFC3339)
	return strings.Contains(dateTime, "T") &&
		(strings.Contains(dateTime, "Z") || strings.Contains(dateTime, "+") || strings.Contains(dateTime, "-"))
}

func isValidURL(url string) bool {
	// Basic URL validation
	return strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://")
}
