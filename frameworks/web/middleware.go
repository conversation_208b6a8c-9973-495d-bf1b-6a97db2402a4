package web

import (
	"log/slog"
	"net/http"
)

// MiddlewareChain represents a chain of HTTP middleware
type MiddlewareChain struct {
	middlewares []func(http.Handler) http.Handler
}

// NewMiddlewareChain creates a new middleware chain
func NewMiddlewareChain() *MiddlewareChain {
	return &MiddlewareChain{
		middlewares: make([]func(http.Handler) http.Handler, 0),
	}
}

// Use adds a middleware to the chain
func (mc *MiddlewareChain) Use(middleware func(http.Handler) http.Handler) *MiddlewareChain {
	mc.middlewares = append(mc.middlewares, middleware)
	return mc
}

// Then applies the middleware chain to a handler
func (mc *MiddlewareChain) Then(handler http.Handler) http.Handler {
	// Apply middlewares in reverse order so they execute in the order they were added
	for i := len(mc.middlewares) - 1; i >= 0; i-- {
		handler = mc.middlewares[i](handler)
	}
	return handler
}

// ThenFunc applies the middleware chain to a handler function
func (mc *MiddlewareChain) ThenFunc(handlerFunc http.HandlerFunc) http.Handler {
	return mc.Then(handlerFunc)
}

// StandardMiddleware creates a standard middleware chain with correlation and logging
func StandardMiddleware(logger *slog.Logger) *MiddlewareChain {
	return StandardMiddlewareWithExclusions(logger, nil)
}

// StandardMiddlewareWithExclusions creates a standard middleware chain with correlation and logging,
// excluding specified paths from logging
func StandardMiddlewareWithExclusions(logger *slog.Logger, excludePaths []string) *MiddlewareChain {
	return NewMiddlewareChain().
		Use(CorrelationMiddleware).
		Use(LoggingMiddlewareWithExclusions(logger, excludePaths)).
		Use(RecoveryMiddleware(logger))
}

// RecoveryMiddleware recovers from panics and logs them with correlation context
func RecoveryMiddleware(logger *slog.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			defer func() {
				if err := recover(); err != nil {
					corrCtx := GetCorrelationContext(r.Context())

					logArgs := []any{
						slog.Any("panic", err),
						slog.String("method", r.Method),
						slog.String("path", r.URL.Path),
						slog.String("request_id", corrCtx.RequestID),
						slog.String("correlation_id", corrCtx.CorrelationID),
					}

					// Add query parameters if present
					if r.URL.RawQuery != "" {
						logArgs = append(logArgs, slog.String("query_params", r.URL.RawQuery))
					}

					logger.ErrorContext(r.Context(), "Panic recovered", logArgs...)

					// Return 500 Internal Server Error
					http.Error(w, "Internal Server Error", http.StatusInternalServerError)
				}
			}()

			next.ServeHTTP(w, r)
		})
	}
}

// CORSMiddleware adds CORS headers
func CORSMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PATCH, DELETE, PUT, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, X-Request-ID, X-Correlation-ID, X-User-ID, X-Session-ID")
			w.Header().Set("Access-Control-Expose-Headers", "X-Request-ID, X-Correlation-ID")

			if r.Method == http.MethodOptions {
				w.WriteHeader(http.StatusNoContent)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// SecurityHeadersMiddleware adds security headers
func SecurityHeadersMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("X-Content-Type-Options", "nosniff")
			w.Header().Set("X-Frame-Options", "DENY")
			w.Header().Set("X-XSS-Protection", "1; mode=block")

			next.ServeHTTP(w, r)
		})
	}
}

// RequestSizeLimitMiddleware limits the size of request bodies
func RequestSizeLimitMiddleware(maxBytes int64) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			r.Body = http.MaxBytesReader(w, r.Body, maxBytes)
			next.ServeHTTP(w, r)
		})
	}
}

// WrapHandler wraps an http.HandlerFunc with standard middleware
func WrapHandler(logger *slog.Logger, handlerFunc http.HandlerFunc) http.Handler {
	return StandardMiddleware(logger).ThenFunc(handlerFunc)
}

// WrapHandlerWithMiddleware wraps a handler with custom middleware chain
func WrapHandlerWithMiddleware(chain *MiddlewareChain, handlerFunc http.HandlerFunc) http.Handler {
	return chain.ThenFunc(handlerFunc)
}

// Enhanced route registration functions that include middleware

// RegisterHandlerWithMiddleware registers a handler with middleware
func RegisterHandlerWithMiddleware(pattern string, logger *slog.Logger, handlerFunc http.HandlerFunc) {
	http.Handle(pattern, WrapHandler(logger, handlerFunc))
}

// RegisterHandlerWithCustomMiddleware registers a handler with custom middleware
func RegisterHandlerWithCustomMiddleware(pattern string, chain *MiddlewareChain, handlerFunc http.HandlerFunc) {
	http.Handle(pattern, WrapHandlerWithMiddleware(chain, handlerFunc))
}
