# Enhanced Logging with Request IDs and Correlation

This package provides enhanced logging capabilities with request ID and correlation ID tracking
throughout the application.

## Features

- **Request ID Generation**: Unique identifier for each HTTP request
- **Correlation ID Propagation**: Tracks requests across service boundaries
- **Structured Logging**: JSON-formatted logs with consistent correlation context
- **HTTP Middleware**: Automatic injection of correlation context
- **Testing Utilities**: Comprehensive testing support for correlation logging

## Quick Start

### 1. Basic Setup

```go
// In main.go
func main() {
    logger := setupLogger()
    
    // Create middleware chain with correlation and logging
    middleware := web.StandardMiddleware(logger).
        Use(web.CORSMiddleware()).
        Use(web.SecurityHeadersMiddleware())

    // Wrap the default mux with middleware
    handler := middleware.Then(http.DefaultServeMux)
    
    http.ListenAndServe(":8080", handler)
}
```

### 2. Using Correlation-Aware Logging

```go
// In your use cases or handlers
func (uc *MyUseCase) DoSomething(ctx context.Context) error {
    // Logger automatically includes correlation context
    uc.logger.InfoContext(ctx, "Starting operation",
        slog.String("operation", "do_something"),
        slog.String("user_id", "123"))
    
    // Use operation logger for structured operation tracking
    opLogger := web.NewOperationLogger(ctx, uc.logger, "do_something")
    opLogger.Start("processing request")
    
    // ... do work ...
    
    opLogger.Success("completed successfully",
        slog.Int("items_processed", 42))
    
    return nil
}
```

### 3. HTTP Client with Correlation Propagation

```go
// Create correlation-aware HTTP client
client := web.NewCorrelationHTTPClient(logger)

// Make requests - correlation headers are automatically added
resp, err := client.Get(ctx, "https://api.example.com/data")
```

## Correlation Context

### Headers

The system uses these HTTP headers for correlation:

- `X-Request-ID`: Unique identifier for the current request
- `X-Correlation-ID`: Identifier that spans multiple requests/services
- `X-User-ID`: User identifier (optional)
- `X-Session-ID`: Session identifier (optional)

### Context Values

Correlation information is stored in the request context:

```go
corrCtx := web.GetCorrelationContext(ctx)
fmt.Printf("Request ID: %s\n", corrCtx.RequestID)
fmt.Printf("Correlation ID: %s\n", corrCtx.CorrelationID)
```

## Middleware

### Standard Middleware Chain

```go
middleware := web.StandardMiddleware(logger)
// Includes:
// - CorrelationMiddleware (adds correlation context)
// - LoggingMiddleware (logs requests/responses)
// - RecoveryMiddleware (handles panics)
```

### Custom Middleware Chain

```go
middleware := web.NewMiddlewareChain().
    Use(web.CorrelationMiddleware).
    Use(web.LoggingMiddleware(logger)).
    Use(web.CORSMiddleware()).
    Use(web.SecurityHeadersMiddleware()).
    Use(web.RequestSizeLimitMiddleware(10 * 1024 * 1024))
```

## Logging Patterns

### Structured Operation Logging

```go
func (s *Service) ProcessOrder(ctx context.Context, orderID string) error {
    opLogger := web.NewOperationLogger(ctx, s.logger, "process_order")
    
    opLogger.Start("validating order", slog.String("order_id", orderID))
    
    if err := s.validateOrder(orderID); err != nil {
        opLogger.Error("validation failed", err, slog.String("order_id", orderID))
        return err
    }
    
    opLogger.Debug("order validated", slog.String("order_id", orderID))
    
    // ... process order ...
    
    opLogger.Success("order processed", 
        slog.String("order_id", orderID),
        slog.Duration("processing_time", time.Since(start)))
    
    return nil
}
```

### Enhanced Error Logging

```go
func (s *Service) HandleError(ctx context.Context, err error) {
    s.logger.ErrorContext(ctx, "Operation failed",
        slog.String("operation", "handle_request"),
        slog.String("error", err.Error()),
        slog.String("error_type", fmt.Sprintf("%T", err)),
        slog.Any("stack_trace", getStackTrace(err))) // if available
}
```

## Testing

### Basic Testing with Log Capture

```go
func TestMyFunction(t *testing.T) {
    // Create test helper
    helper := web.NewIntegrationTestHelper()
    
    // Use the context and logger from helper
    myService := NewMyService(helper.LogCapture.Logger())
    err := myService.DoSomething(helper.Context)
    
    // Assert operation was logged
    helper.AssertOperationLogged(t, "do_something")
    
    // Check specific log content
    helper.LogCapture.AssertLogContains(t, map[string]interface{}{
        "operation": "do_something",
        "status": "success",
    })
}
```

### Testing Middleware

```go
func TestCorrelationMiddleware(t *testing.T) {
    handler := web.NewMockHTTPHandler()
    
    // Test with correlation headers
    testCorr := web.NewTestCorrelationContext()
    req := web.CreateTestRequestWithCorrelation("GET", "/test", testCorr)
    
    w := web.TestMiddlewareWithRequest(t, web.CorrelationMiddleware, handler, req)
    
    // Assert correlation headers in response
    web.AssertCorrelationHeaders(t, w)
    
    // Assert correlation context was captured
    web.AssertCorrelationInContext(t, handler.CapturedCtx)
}
```

## Configuration

### Logger Configuration

```go
config := &web.LoggerConfig{
    Level:      slog.LevelInfo,
    Format:     "json", // or "text"
    AddSource:  false,
    TimeFormat: "",
}
logger := web.NewStructuredLogger(config)
```

### Environment Variables

- `LOG_LEVEL`: Set log level (debug, info, warn, error)
- `LOG_FORMAT`: Set log format (json, text)

## Best Practices

1. **Always use context**: Pass context through all function calls to maintain correlation
2. **Use operation loggers**: For complex operations, use `OperationLogger` for structured tracking
3. **Include relevant metadata**: Add operation-specific fields to log entries
4. **Test correlation**: Use testing utilities to verify correlation is working
5. **Propagate correlation**: Use correlation-aware HTTP clients for external calls

## Log Output Example

```json
{
  "time": "2025-01-20T10:30:00Z",
  "level": "INFO",
  "msg": "Operation started: processing order",
  "operation": "process_order",
  "request_id": "req_abc12345",
  "correlation_id": "corr_def67890",
  "user_id": "user_123",
  "order_id": "order_456"
}
```

This enhanced logging system provides comprehensive request tracking and correlation across your entire application, making debugging and monitoring much more effective.
