package web_test

import (
	"bytes"
	"context"
	"encoding/json"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
)

func TestCorrelationContext(t *testing.T) {
	t.Run("should create new correlation context with IDs", func(t *testing.T) {
		corrCtx := web.NewCorrelationContext()

		assert.NotEmpty(t, corrCtx.RequestID)
		assert.NotEmpty(t, corrCtx.CorrelationID)
		assert.True(t, strings.HasPrefix(corrCtx.RequestID, "req_"))
		assert.True(t, strings.HasPrefix(corrCtx.CorrelationID, "corr_"))
		assert.False(t, corrCtx.StartTime.IsZero())
	})

	t.Run("should add and retrieve correlation context from context", func(t *testing.T) {
		corrCtx := &web.CorrelationContext{
			RequestID:     "req_test123",
			CorrelationID: "corr_test456",
			UserID:        "user_789",
			SessionID:     "session_abc",
		}

		ctx := web.WithCorrelationContext(context.Background(), corrCtx)
		retrieved := web.GetCorrelationContext(ctx)

		assert.Equal(t, corrCtx.RequestID, retrieved.RequestID)
		assert.Equal(t, corrCtx.CorrelationID, retrieved.CorrelationID)
		assert.Equal(t, corrCtx.UserID, retrieved.UserID)
		assert.Equal(t, corrCtx.SessionID, retrieved.SessionID)
	})
}

func TestCorrelationMiddleware(t *testing.T) {
	t.Run("should add correlation context to request", func(t *testing.T) {
		var capturedContext context.Context

		handler := web.CorrelationMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			capturedContext = r.Context()
			w.WriteHeader(http.StatusOK)
		}))

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		corrCtx := web.GetCorrelationContext(capturedContext)
		assert.NotEmpty(t, corrCtx.RequestID)
		assert.NotEmpty(t, corrCtx.CorrelationID)

		// Check response headers
		assert.NotEmpty(t, w.Header().Get(web.HeaderRequestID))
		assert.NotEmpty(t, w.Header().Get(web.HeaderCorrelationID))
	})

	t.Run("should preserve existing correlation ID from header", func(t *testing.T) {
		existingCorrelationID := "existing_corr_123"
		var capturedContext context.Context

		handler := web.CorrelationMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			capturedContext = r.Context()
			w.WriteHeader(http.StatusOK)
		}))

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set(web.HeaderCorrelationID, existingCorrelationID)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		corrCtx := web.GetCorrelationContext(capturedContext)
		assert.Equal(t, existingCorrelationID, corrCtx.CorrelationID)
		assert.Equal(t, existingCorrelationID, w.Header().Get(web.HeaderCorrelationID))
	})

	t.Run("should extract user and session IDs from headers", func(t *testing.T) {
		userID := "user_123"
		sessionID := "session_456"
		var capturedContext context.Context

		handler := web.CorrelationMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			capturedContext = r.Context()
			w.WriteHeader(http.StatusOK)
		}))

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set(web.HeaderUserID, userID)
		req.Header.Set(web.HeaderSessionID, sessionID)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		corrCtx := web.GetCorrelationContext(capturedContext)
		assert.Equal(t, userID, corrCtx.UserID)
		assert.Equal(t, sessionID, corrCtx.SessionID)
	})
}

func TestLoggingMiddleware(t *testing.T) {
	t.Run("should log request start and completion", func(t *testing.T) {
		var buf bytes.Buffer
		logger := slog.New(slog.NewJSONHandler(&buf, &slog.HandlerOptions{Level: slog.LevelDebug}))

		handler := web.CorrelationMiddleware(
			web.LoggingMiddleware(logger)(
				http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.WriteHeader(http.StatusOK)
				}),
			),
		)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		logOutput := buf.String()
		lines := strings.Split(strings.TrimSpace(logOutput), "\n")

		// Should have at least 2 log lines (start and completion)
		assert.GreaterOrEqual(t, len(lines), 2)

		// Parse first log line (request start)
		var startLog map[string]interface{}
		err := json.Unmarshal([]byte(lines[0]), &startLog)
		require.NoError(t, err)

		assert.Equal(t, "Request started", startLog["msg"])
		assert.Equal(t, "GET", startLog["method"])
		assert.Equal(t, "/test", startLog["path"])
		assert.NotEmpty(t, startLog["request_id"])
		assert.NotEmpty(t, startLog["correlation_id"])

		// Parse last log line (request completion)
		var endLog map[string]interface{}
		err = json.Unmarshal([]byte(lines[len(lines)-1]), &endLog)
		require.NoError(t, err)

		assert.Equal(t, "Request completed", endLog["msg"])
		assert.Equal(t, "GET", endLog["method"])
		assert.Equal(t, "/test", endLog["path"])
		assert.Equal(t, float64(200), endLog["status_code"])
		assert.NotEmpty(t, endLog["duration"])
		assert.NotEmpty(t, endLog["request_id"])
		assert.NotEmpty(t, endLog["correlation_id"])
	})

	t.Run("should include query parameters in logs", func(t *testing.T) {
		var buf bytes.Buffer
		logger := slog.New(slog.NewJSONHandler(&buf, &slog.HandlerOptions{Level: slog.LevelDebug}))

		handler := web.CorrelationMiddleware(
			web.LoggingMiddleware(logger)(
				http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.WriteHeader(http.StatusOK)
				}),
			),
		)

		req := httptest.NewRequest("GET", "/test?param1=value1&param2=value2", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		logOutput := buf.String()
		lines := strings.Split(strings.TrimSpace(logOutput), "\n")

		// Should have at least 2 log lines (start and completion)
		assert.GreaterOrEqual(t, len(lines), 2)

		// Parse first log line (request start)
		var startLog map[string]interface{}
		err := json.Unmarshal([]byte(lines[0]), &startLog)
		require.NoError(t, err)

		assert.Equal(t, "Request started", startLog["msg"])
		assert.Equal(t, "GET", startLog["method"])
		assert.Equal(t, "/test", startLog["path"])
		assert.Equal(t, "param1=value1&param2=value2", startLog["query_params"])
		assert.NotEmpty(t, startLog["request_id"])
		assert.NotEmpty(t, startLog["correlation_id"])

		// Parse last log line (request completion)
		var endLog map[string]interface{}
		err = json.Unmarshal([]byte(lines[len(lines)-1]), &endLog)
		require.NoError(t, err)

		assert.Equal(t, "Request completed", endLog["msg"])
		assert.Equal(t, "GET", endLog["method"])
		assert.Equal(t, "/test", endLog["path"])
		assert.Equal(t, "param1=value1&param2=value2", endLog["query_params"])
		assert.Equal(t, float64(200), endLog["status_code"])
		assert.NotEmpty(t, endLog["duration"])
		assert.NotEmpty(t, endLog["request_id"])
		assert.NotEmpty(t, endLog["correlation_id"])
	})

	t.Run("should not include query_params field when no query parameters", func(t *testing.T) {
		var buf bytes.Buffer
		logger := slog.New(slog.NewJSONHandler(&buf, &slog.HandlerOptions{Level: slog.LevelDebug}))

		handler := web.CorrelationMiddleware(
			web.LoggingMiddleware(logger)(
				http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.WriteHeader(http.StatusOK)
				}),
			),
		)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		logOutput := buf.String()
		lines := strings.Split(strings.TrimSpace(logOutput), "\n")

		// Parse first log line (request start)
		var startLog map[string]interface{}
		err := json.Unmarshal([]byte(lines[0]), &startLog)
		require.NoError(t, err)

		// Should not have query_params field when no query parameters
		_, hasQueryParams := startLog["query_params"]
		assert.False(t, hasQueryParams)

		// Parse last log line (request completion)
		var endLog map[string]interface{}
		err = json.Unmarshal([]byte(lines[len(lines)-1]), &endLog)
		require.NoError(t, err)

		// Should not have query_params field when no query parameters
		_, hasQueryParams = endLog["query_params"]
		assert.False(t, hasQueryParams)
	})
}

func TestLoggerFromContext(t *testing.T) {
	t.Run("should create logger with correlation attributes", func(t *testing.T) {
		var buf bytes.Buffer
		baseLogger := slog.New(slog.NewJSONHandler(&buf, &slog.HandlerOptions{Level: slog.LevelDebug}))

		corrCtx := &web.CorrelationContext{
			RequestID:     "req_test123",
			CorrelationID: "corr_test456",
			UserID:        "user_789",
		}

		ctx := web.WithCorrelationContext(context.Background(), corrCtx)
		logger := web.LoggerFromContext(ctx, baseLogger)

		logger.InfoContext(ctx, "Test message")

		var logEntry map[string]interface{}
		err := json.Unmarshal(buf.Bytes(), &logEntry)
		require.NoError(t, err)

		assert.Equal(t, "Test message", logEntry["msg"])
		assert.Equal(t, "req_test123", logEntry["request_id"])
		assert.Equal(t, "corr_test456", logEntry["correlation_id"])
		assert.Equal(t, "user_789", logEntry["user_id"])
	})

	t.Run("should return base logger when no correlation context", func(t *testing.T) {
		baseLogger := slog.Default()
		ctx := context.Background()

		logger := web.LoggerFromContext(ctx, baseLogger)

		assert.Equal(t, baseLogger, logger)
	})
}

func TestOperationLogger(t *testing.T) {
	t.Run("should log operation lifecycle with correlation", func(t *testing.T) {
		var buf bytes.Buffer
		baseLogger := slog.New(slog.NewJSONHandler(&buf, &slog.HandlerOptions{Level: slog.LevelDebug}))

		corrCtx := &web.CorrelationContext{
			RequestID:     "req_test123",
			CorrelationID: "corr_test456",
		}

		ctx := web.WithCorrelationContext(context.Background(), corrCtx)
		opLogger := web.NewOperationLogger(ctx, baseLogger, "test_operation")

		opLogger.Start("starting test")
		opLogger.Success("completed successfully")

		logOutput := buf.String()
		lines := strings.Split(strings.TrimSpace(logOutput), "\n")

		assert.Len(t, lines, 2)

		// Check start log
		var startLog map[string]interface{}
		err := json.Unmarshal([]byte(lines[0]), &startLog)
		require.NoError(t, err)

		assert.Contains(t, startLog["msg"], "Operation started")
		assert.Equal(t, "test_operation", startLog["operation"])
		assert.Equal(t, "req_test123", startLog["request_id"])

		// Check success log
		var successLog map[string]interface{}
		err = json.Unmarshal([]byte(lines[1]), &successLog)
		require.NoError(t, err)

		assert.Contains(t, successLog["msg"], "Operation completed")
		assert.Equal(t, "test_operation", successLog["operation"])
		assert.Equal(t, "success", successLog["status"])
	})
}

func TestLoggingMiddlewareWithExclusions(t *testing.T) {
	t.Run("should exclude specified paths from logging", func(t *testing.T) {
		var buf bytes.Buffer
		logger := slog.New(slog.NewJSONHandler(&buf, &slog.HandlerOptions{Level: slog.LevelDebug}))

		// Create middleware that excludes /healthz
		handler := web.CorrelationMiddleware(
			web.LoggingMiddlewareWithExclusions(logger, []string{"/healthz"})(
				http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.WriteHeader(http.StatusOK)
				}),
			),
		)

		// Test excluded path - should not log
		req := httptest.NewRequest("GET", "/healthz", nil)
		w := httptest.NewRecorder()
		handler.ServeHTTP(w, req)

		// Should have no log output
		assert.Empty(t, buf.String())

		// Test non-excluded path - should log
		buf.Reset()
		req = httptest.NewRequest("GET", "/api/test", nil)
		w = httptest.NewRecorder()
		handler.ServeHTTP(w, req)

		// Should have log output
		assert.NotEmpty(t, buf.String())

		// Verify it contains the expected log entries
		lines := strings.Split(strings.TrimSpace(buf.String()), "\n")
		assert.GreaterOrEqual(t, len(lines), 2) // Should have start and completion logs

		// Verify the path is logged correctly
		var startLog map[string]interface{}
		err := json.Unmarshal([]byte(lines[0]), &startLog)
		require.NoError(t, err)
		assert.Equal(t, "/api/test", startLog["path"])
		assert.Equal(t, "Request started", startLog["msg"])
	})
}
