# Database Connection Pool Configuration
# Copy this file to .env and adjust values as needed

# Maximum number of connections in the pool
# Default: 30
# Recommended: 10-50 for most applications
DB_MAX_CONNS=30

# Minimum number of connections in the pool
# Default: 5
# Recommended: 2-10 for most applications
DB_MIN_CONNS=5

# Maximum lifetime of a connection
# Default: 1h
# Recommended: 30m-2h depending on your database configuration
DB_MAX_CONN_LIFETIME=1h

# Maximum idle time of a connection
# Default: 30m
# Recommended: 15m-1h depending on your application usage patterns
DB_MAX_CONN_IDLE_TIME=30m

# Period between health checks
# Default: 1m
# Recommended: 30s-5m depending on your monitoring needs
DB_HEALTH_CHECK_PERIOD=1m

# Example configurations for different environments:

# Development environment (lower resource usage)
# DB_MAX_CONNS=10
# DB_MIN_CONNS=2
# DB_MAX_CONN_LIFETIME=30m
# DB_MAX_CONN_IDLE_TIME=15m
# DB_HEALTH_CHECK_PERIOD=2m

# Production environment (higher throughput)
# DB_MAX_CONNS=50
# DB_MIN_CONNS=10
# DB_MAX_CONN_LIFETIME=2h
# DB_MAX_CONN_IDLE_TIME=1h
# DB_HEALTH_CHECK_PERIOD=30s

# High-load environment
# DB_MAX_CONNS=100
# DB_MIN_CONNS=20
# DB_MAX_CONN_LIFETIME=1h
# DB_MAX_CONN_IDLE_TIME=30m
# DB_HEALTH_CHECK_PERIOD=30s

# Notes:
# - Duration values can be specified in Go duration format (e.g., "30s", "5m", "1h", "2h30m")
# - Connection counts should be tuned based on your database server's max_connections setting
# - Consider your application's concurrency patterns when setting pool sizes
# - Monitor pool statistics using the LogPoolStats function to optimize settings
