package entities

import (
	"database/sql"
	"errors"
	"net/url"
	"time"

	"github.com/google/uuid"
)

var ErrRenditionIdMismatch = errors.New("rendition ID mismatch")

type RenditionStatus string

const (
	RenditionPending   RenditionStatus = "Pending"
	RenditionStarted   RenditionStatus = "Started"
	RenditionCompleted RenditionStatus = "Completed"
	RenditionOutdated  RenditionStatus = "Outdated"
	RenditionArchived  RenditionStatus = "Archived"
)

type Rendition struct {
	Id        uuid.UUID
	CreatedAt time.Time
	UpdatedAt time.Time

	// These are the only 2 fields that can be modified.
	Status RenditionStatus
	URL    *url.URL

	// These fields are automatically populated via the associated room design
	// when being retrieved only and cannot be modified.
	DesignId      *uuid.UUID
	Title         sql.NullString
	Description   sql.NullString
	SkuCount      sql.NullInt32
	TotalPrice    sql.NullInt32
	Designer      sql.NullString
	DesignerImage sql.NullString
}

type RenditionDiff struct {
	Id     uuid.UUID       `json:"id"`
	Status RenditionStatus `json:"status"`
	URL    *url.URL        `json:"url"`
}

func (existing *Rendition) MergeWith(incoming RenditionDiff) error {
	if incoming.Id != existing.Id {
		return ErrRenditionIdMismatch
	}
	if incoming.Status == RenditionCompleted && incoming.URL == nil {
		return errors.New("rendition URL cannot be empty when status is Completed")
	}
	existing.Status = incoming.Status
	if incoming.URL != nil {
		existing.URL = incoming.URL
	}
	return nil
}
