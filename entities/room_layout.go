package entities

import (
	"encoding/json"
	"log"
	"slices"
	"time"

	"github.com/google/uuid"
	"github.com/nsf/jsondiff"
)

type ShowerEnclosureType string

const (
	NoShowerEnclosure      ShowerEnclosureType = "None"
	SlidingShowerEnclosure ShowerEnclosureType = "Sliding"
	FixedShowerEnclosure   ShowerEnclosureType = "Fixed"
)

type AlcoveTub struct {
	LayoutId uuid.UUID           `json:"layoutId"`
	DoorType ShowerEnclosureType `json:"doorType"`
	ShowerId *uuid.UUID          `json:"showerSystem"`
}

type WetArea struct {
	LayoutId             uuid.UUID           `json:"layoutId"`
	GlassType            ShowerEnclosureType `json:"glassType"`
	ShowerIds            []uuid.UUID         `json:"showerSystems,omitempty"`
	AlcoveTubs           []AlcoveTub         `json:"alcoveTubs,omitempty"`
	FreestandingTubIds   []uuid.UUID         `json:"freestandingTubs,omitempty"`
	MaxShowerGlassLength *float64            `json:"maxShowerGlassLength,omitempty"`
	MaxTubLength         *float64            `json:"maxTubLength,omitempty"`
}

func (wa WetArea) Equals(other WetArea) bool {
	return wa.LayoutId == other.LayoutId &&
		wa.GlassType == other.GlassType &&
		slices.Equal(wa.ShowerIds, other.ShowerIds) &&
		slices.Equal(wa.AlcoveTubs, other.AlcoveTubs) &&
		slices.Equal(wa.FreestandingTubIds, other.FreestandingTubIds) &&
		wa.MaxShowerGlassLength == other.MaxShowerGlassLength &&
		wa.MaxTubLength == other.MaxTubLength
}

type Wall struct {
	LayoutId uuid.UUID   `json:"layoutId"`
	NicheIds []uuid.UUID `json:"nicheIds,omitempty"`
}

func (w Wall) Equals(other Wall) bool {
	return w.LayoutId == other.LayoutId && slices.Equal(w.NicheIds, other.NicheIds)
}

type Vanity struct {
	LayoutId  uuid.UUID `json:"layoutId"`
	MaxLength *float64  `json:"maxLength,omitempty"`
}

type RoomLayout struct {
	Id        uuid.UUID `json:"id,omitempty"`
	UpdatedAt time.Time `json:"updatedAt,omitempty"`
	RawData   []byte    `json:"-"`
	Hash      uint64    `json:"-"`
	AreaSqFt  float64   `json:"areaSqFt,omitempty"`

	FloorIds  []uuid.UUID `json:"floorIds"`
	Walls     []Wall      `json:"walls"`
	WetAreas  []WetArea   `json:"wetAreas"`
	ToiletIds []uuid.UUID `json:"toiletIds"`
	Vanities  []Vanity    `json:"vanities"`
}

// Equals checks if the essental parts of two RoomLayouts are equal.
func (rl RoomLayout) Compare(other RoomLayout) (bool, string) {
	if slices.Equal(rl.FloorIds, other.FloorIds) &&
		slices.Equal(rl.ToiletIds, other.ToiletIds) &&
		slices.Equal(rl.Vanities, other.Vanities) &&
		slices.EqualFunc(rl.Walls, other.Walls, func(a, b Wall) bool { return a.Equals(b) }) &&
		slices.EqualFunc(rl.WetAreas, other.WetAreas, func(a, b WetArea) bool { return a.Equals(b) }) {
		return true, ""
	}

	thisRoom, err := json.Marshal(rl)
	if err != nil {
		log.Println("Failed to marshal room layout:", err.Error())
		return false, "???"
	}
	defaultRoom, err := json.Marshal(other)
	if err != nil {
		log.Println("Failed to marshal room layout:", err.Error())
		return false, "???"
	}
	opts := jsondiff.DefaultJSONOptions()
	opts.SkipMatches = true
	diff, msg := jsondiff.Compare(thisRoom, defaultRoom, &opts)
	return diff == jsondiff.FullMatch, msg
}
