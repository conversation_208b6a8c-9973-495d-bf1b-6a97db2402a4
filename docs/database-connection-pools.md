# Database Connection Pool Configuration

This document describes how to configure database connection pools in the room-design service.

## Overview

The service uses PostgreSQL connection pools via the `pgxpool` library to efficiently manage database connections. Connection pools help:

- Reduce connection overhead by reusing existing connections
- Limit the number of concurrent connections to the database
- Provide connection health monitoring and automatic recovery
- Improve application performance and resource utilization

## Configuration

### Environment Variables

The connection pool can be configured using the following environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `DB_MAX_CONNS` | 30 | Maximum number of connections in the pool |
| `DB_MIN_CONNS` | 5 | Minimum number of connections in the pool |
| `DB_MAX_CONN_LIFETIME` | 1h | Maximum lifetime of a connection |
| `DB_MAX_CONN_IDLE_TIME` | 30m | Maximum idle time of a connection |
| `DB_HEALTH_CHECK_PERIOD` | 1m | Period between health checks |

### Duration Format

Duration values use Go's duration format:

- `s` for seconds (e.g., `30s`)
- `m` for minutes (e.g., `15m`)
- `h` for hours (e.g., `2h`)
- Combined formats (e.g., `1h30m`)

### Example Configurations

#### Development Environment

```bash
DB_MAX_CONNS=10
DB_MIN_CONNS=2
DB_MAX_CONN_LIFETIME=30m
DB_MAX_CONN_IDLE_TIME=15m
DB_HEALTH_CHECK_PERIOD=2m
```

#### Production Environment

```bash
DB_MAX_CONNS=50
DB_MIN_CONNS=10
DB_MAX_CONN_LIFETIME=2h
DB_MAX_CONN_IDLE_TIME=1h
DB_HEALTH_CHECK_PERIOD=30s
```

#### High-Load Environment

```bash
DB_MAX_CONNS=100
DB_MIN_CONNS=20
DB_MAX_CONN_LIFETIME=1h
DB_MAX_CONN_IDLE_TIME=30m
DB_HEALTH_CHECK_PERIOD=30s
```

## Usage

### Default Configuration

The service automatically loads configuration from environment variables:

```go
// Uses environment variables or defaults
relDb := db.NewRelationalDb(ctx, pgConnStr, logger)
```

### Custom Configuration

You can also provide custom configuration programmatically:

```go
poolConfig := &db.PoolConfig{
    MaxConns:          25,
    MinConns:          5,
    MaxConnLifetime:   time.Hour,
    MaxConnIdleTime:   30 * time.Minute,
    HealthCheckPeriod: time.Minute,
}

relDb := db.NewRelationalDbWithConfig(ctx, pgConnStr, poolConfig, logger)
```

## Monitoring

### Pool Statistics

Use the `LogPoolStats` function to monitor pool health:

```go
db.LogPoolStats(pool, logger)
```

This logs metrics including:

- Current acquired connections
- Idle connections
- Total connections
- Acquire count and duration
- Connection lifecycle statistics

### Key Metrics to Monitor

1. **Acquired Connections**: Should be well below max connections
2. **Idle Connections**: Should be above min connections during low load
3. **Acquire Duration**: Should be low (< 1ms typically)
4. **Total Connections**: Should stay within expected range

## Tuning Guidelines

### Connection Limits

- **Max Connections**: Set based on your database server's `max_connections` setting
- **Min Connections**: Set to handle baseline load without connection overhead
- **Rule of thumb**: Start with max_conns = (database_max_connections / number_of_services) * 0.8

### Timeouts

- **Connection Lifetime**: Balance between connection reuse and avoiding stale connections
- **Idle Time**: Should be longer than typical request intervals but shorter than database timeout
- **Health Check Period**: More frequent for critical applications, less frequent for development

### Environment-Specific Recommendations

| Environment | Max Conns | Min Conns | Lifetime | Idle Time | Health Check |
|-------------|-----------|-----------|----------|-----------|--------------|
| Development | 10 | 2 | 30m | 15m | 2m |
| Staging | 20 | 5 | 1h | 30m | 1m |
| Production | 30-50 | 10 | 1-2h | 30m-1h | 30s-1m |
| High-Load | 50-100 | 20 | 1h | 30m | 30s |

## Troubleshooting

### Common Issues

1. **Connection Pool Exhausted**
   - Increase `DB_MAX_CONNS`
   - Check for connection leaks in application code
   - Monitor acquire duration

2. **High Connection Churn**
   - Increase `DB_MIN_CONNS`
   - Increase `DB_MAX_CONN_IDLE_TIME`
   - Check application connection patterns

3. **Stale Connections**
   - Decrease `DB_MAX_CONN_LIFETIME`
   - Decrease `DB_HEALTH_CHECK_PERIOD`
   - Check database server timeout settings

### Debugging

Enable debug logging to see detailed pool behavior:

```bash
LOG_LEVEL=debug
```

Monitor pool statistics regularly in production:

```go
// Add to your monitoring/health check endpoints
db.LogPoolStats(pool, logger)
```

## Best Practices

1. **Start Conservative**: Begin with default settings and tune based on monitoring
2. **Monitor Continuously**: Track pool metrics in production
3. **Test Under Load**: Verify pool behavior under expected peak load
4. **Consider Database Limits**: Ensure total connections across all services don't exceed database limits
5. **Environment Parity**: Use similar settings across staging and production
6. **Graceful Degradation**: Handle pool exhaustion gracefully in application code
