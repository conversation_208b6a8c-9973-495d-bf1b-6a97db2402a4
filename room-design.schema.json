{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Legacy bathroom design", "description": "The selection of products for a bathroom as used for 1.0 demo", "properties": {"id": {"type": "string"}, "faucet": {"type": ["string", "null"], "format": "UUID"}, "floorTile": {"type": ["string", "null"], "format": "UUID"}, "floorTilePattern": {"enum": ["Vertical", "Horizontal", "HalfOffset", "ThirdOffset", "<PERSON><PERSON><PERSON>", null]}, "lighting": {"type": ["string", "null"], "format": "UUID"}, "mirror": {"type": ["string", "null"], "format": "UUID"}, "paint": {"type": ["string", "null"], "format": "UUID"}, "shelves": {"type": ["string", "null"], "format": "UUID"}, "showerFloorTile": {"type": ["string", "null"], "format": "UUID"}, "showerFloorTilePattern": {"enum": ["Vertical", "Horizontal", "HalfOffset", "ThirdOffset", "<PERSON><PERSON><PERSON>", null]}, "showerSystem": {"type": ["string", "null"], "format": "UUID"}, "showerWallTile": {"type": ["string", "null"], "format": "UUID"}, "showerWallTilePattern": {"enum": ["Vertical", "Horizontal", "HalfOffset", "ThirdOffset", "<PERSON><PERSON><PERSON>", null]}, "showerShortWallTile": {"type": ["string", "null"], "format": "UUID"}, "showerGlass": {"type": ["string", "null"], "format": "UUID"}, "toilet": {"type": ["string", "null"], "format": "UUID"}, "tub": {"type": ["string", "null"], "format": "UUID"}, "tubDoor": {"type": ["string", "null"], "format": "UUID"}, "tubFiller": {"type": ["string", "null"], "format": "UUID"}, "vanity": {"type": ["string", "null"], "format": "UUID"}, "wallpaper": {"type": ["string", "null"], "format": "UUID"}, "wallpaperPlacement": {"enum": ["None", "AllWalls", "VanityWall", null]}, "wallTile": {"type": ["string", "null"], "format": "UUID"}, "wallTilePlacement": {"enum": ["None", "FullWall", "HalfWall", "VanityFullWall", "VanityHalfWall", null]}, "wallTilePattern": {"enum": ["Vertical", "Horizontal", "HalfOffset", "ThirdOffset", "<PERSON><PERSON><PERSON>", null]}, "nicheTile": {"type": ["string", "null"], "format": "UUID"}, "status": {"enum": ["Preview", "Fave", "Archived", null]}, "colorScheme": {"enum": ["Neutral", "Bold", null]}, "style": {"enum": ["Traditional", "Transitional", "Mid-century", "Modern", null]}, "tags": {"type": "integer"}, "title": {"type": ["string", "null"]}, "description": {"type": ["string", "null"]}, "leadTimeDays": {"type": "integer"}, "skuCount": {"type": "integer"}, "totalPrice": {"type": "integer"}, "isShowerGlassVisible": {"type": "boolean"}, "isTubDoorVisible": {"type": "boolean"}, "isNichesVisible": {"type": "boolean"}, "lastUpdatedDateTime": {"type": "string", "format": "date-time"}, "renditions": {"type": "array", "items": {"type": "object", "properties": {"status": {"enum": ["pending", "started", "completed", "outdated", "archived"]}, "url": {"type": "string", "format": "uri"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["status"], "additionalProperties": true}}}, "required": [], "additionalProperties": false}