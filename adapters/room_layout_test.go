package adapters

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRoomLayoutJSONMarshaling(t *testing.T) {
	// Sample JSON data similar to what you provided
	jsonData := `{
		"areas": {
			"showers": [
				{
					"curbHeight": 0,
					"curbThickness": 0,
					"identifier": "7d959d9c-415b-489e-8011-2e364bd5309f",
					"parentIdentifier": "397EF72C-53A3-4F0A-BC8A-E641E3FF691D",
					"position": {
						"x": -0.0873088241,
						"y": -1.3753562,
						"z": -1.23969078
					},
					"rotation": {
						"x": 0,
						"y": 0,
						"z": 0
					},
					"shape": [
						{
							"x": -0.7412139,
							"y": -0.457155228
						},
						{
							"x": -0.7412139,
							"y": 0.457155228
						}
					],
					"shortName": null
				}
			]
		},
		"ceilings": [
			{
				"identifier": "6207f67d-9e0a-432a-a288-29ef8823ee94",
				"parentIdentifier": null,
				"position": {
					"x": -0.08730887,
					"y": 0.80064404,
					"z": 0.336536884
				},
				"rotation": {
					"x": 90,
					"y": 0,
					"z": 0
				},
				"shape": [
					{
						"x": -0.7412143,
						"y": -2.03338361
					}
				],
				"shortName": null
			}
		],
		"doors": [
			{
				"identifier": "C5050BF6-498E-4A71-A202-A893F72DC735",
				"parentIdentifier": "69858A83-A3C9-4510-B66F-E40C053FF720",
				"position": {
					"x": -0.8285227,
					"y": -0.6014211,
					"z": 1.84341884
				},
				"rotation": {
					"x": 0,
					"y": 90,
					"z": 0
				},
				"scale": {
					"x": 0.762518764,
					"y": 2.05586982,
					"z": 0
				},
				"shortName": "D1",
				"swing": "In",
				"type": "SingleSwingDoor"
			}
		],
		"floors": [],
		"lights": [],
		"linenCabinets": [],
		"mirrors": [],
		"niches": [],
		"openings": [],
		"robeHooks": [],
		"scannedDate": 774538310,
		"shelves": [],
		"showerSystems": [
			{
				"identifier": "a1902b9e-e418-43e6-b235-518c5cfbf43b",
				"parentIdentifier": "8087FDE7-4B16-459E-9CA8-3E480D8DAFA3",
				"position": {
					"x": 0,
					"y": 0,
					"z": 0
				},
				"rotation": {
					"x": 0,
					"y": 0,
					"z": 0
				},
				"scale": {
					"x": 0.164999992,
					"y": 2.43,
					"z": 0.2475
				},
				"shortName": null,
				"showerAreaIdentifier": "7d959d9c-415b-489e-8011-2e364bd5309f",
				"type": "TubAndShowerFaucet"
			}
		],
		"sinks": [],
		"toilets": [],
		"towelBars": [],
		"towelRings": [],
		"tpHolders": [],
		"tubFillers": [],
		"tubs": [
			{
				"identifier": "397EF72C-53A3-4F0A-BC8A-E641E3FF691D",
				"parentIdentifier": null,
				"position": {
					"x": -0.0873088241,
					"y": -1.3753562,
					"z": -1.23969078
				},
				"rotation": {
					"x": 0,
					"y": 0,
					"z": 0
				},
				"scale": {
					"x": 1.48242784,
					"y": 0.5079998,
					"z": 0.914310455
				},
				"shortName": null,
				"type": "Alcove"
			}
		],
		"vanities": [],
		"walls": [
			{
				"identifier": "8087FDE7-4B16-459E-9CA8-3E480D8DAFA3",
				"parentIdentifier": null,
				"position": {
					"x": 0.6539051,
					"y": -0.414356023,
					"z": 0.336536646
				},
				"rotation": {
					"x": 0,
					"y": -90,
					"z": 0
				},
				"shape": [
					{
						"x": 2.03338313,
						"y": 1.215
					}
				],
				"shortName": "A",
				"thickness": 0.152399927
			}
		],
		"windows": []
	}`

	// Test unmarshaling
	var roomLayout RoomLayout
	err := json.Unmarshal([]byte(jsonData), &roomLayout)
	require.NoError(t, err)

	// Verify the structure was populated correctly
	assert.Len(t, roomLayout.Areas.Showers, 1)
	assert.Len(t, roomLayout.Ceilings, 1)
	assert.Len(t, roomLayout.Doors, 1)
	assert.Len(t, roomLayout.ShowerSystems, 1)
	assert.Len(t, roomLayout.Tubs, 1)
	assert.Len(t, roomLayout.Walls, 1)

	// Verify specific fields
	shower := roomLayout.Areas.Showers[0]
	assert.Equal(t, "7d959d9c-415b-489e-8011-2e364bd5309f", shower.Identifier)
	assert.Equal(t, "397EF72C-53A3-4F0A-BC8A-E641E3FF691D", *shower.ParentIdentifier)
	assert.Equal(t, float64(0), shower.CurbHeight)
	assert.Equal(t, float64(0), shower.CurbThickness)
	assert.Len(t, shower.Shape, 2)

	door := roomLayout.Doors[0]
	assert.Equal(t, "C5050BF6-498E-4A71-A202-A893F72DC735", door.Identifier)
	assert.Equal(t, "In", door.Swing)
	assert.Equal(t, "SingleSwingDoor", door.Type)
	assert.Equal(t, "D1", *door.ShortName)

	showerSystem := roomLayout.ShowerSystems[0]
	assert.Equal(t, "a1902b9e-e418-43e6-b235-518c5cfbf43b", showerSystem.Identifier)
	assert.Equal(t, "7d959d9c-415b-489e-8011-2e364bd5309f", showerSystem.ShowerAreaIdentifier)
	assert.Equal(t, "TubAndShowerFaucet", showerSystem.Type)

	tub := roomLayout.Tubs[0]
	assert.Equal(t, "397EF72C-53A3-4F0A-BC8A-E641E3FF691D", tub.Identifier)
	assert.Equal(t, "Alcove", tub.Type)

	wall := roomLayout.Walls[0]
	assert.Equal(t, "8087FDE7-4B16-459E-9CA8-3E480D8DAFA3", wall.Identifier)
	assert.Equal(t, "A", *wall.ShortName)
	assert.Equal(t, float64(0.152399927), wall.Thickness)

	// Test marshaling back to JSON
	marshaledData, err := json.Marshal(roomLayout)
	require.NoError(t, err)

	// Verify we can unmarshal it again
	var roomLayout2 RoomLayout
	err = json.Unmarshal(marshaledData, &roomLayout2)
	require.NoError(t, err)

	// Basic verification that round-trip worked
	assert.Len(t, roomLayout2.Areas.Showers, 1)
	assert.Equal(t, roomLayout.Areas.Showers[0].Identifier, roomLayout2.Areas.Showers[0].Identifier)
}

func TestRoomLayoutToEntity(t *testing.T) {
	// Create a simple room layout
	roomLayout := RoomLayout{
		Floors: []Floor{
			{
				ShapedElement: ShapedElement{
					BaseElement: BaseElement{
						Identifier: "EEFDD188-4481-41B3-8B82-213A0D6BEC4B",
						Position:   Position{X: 1.0, Y: 2.0, Z: 3.0},
					},
				},
			},
		},
		Toilets: []Toilet{
			{
				ScalableElement: ScalableElement{
					BaseElement: BaseElement{
						Identifier: "0EE45E00-0FBC-4A19-A50A-579E32FF91BF",
					},
				},
				Type: "Freestanding",
			},
		},
	}

	// Convert to entity
	entity := roomLayout.ToEntity()

	// Verify conversion
	assert.NotZero(t, entity.Hash)
	assert.NotEmpty(t, entity.RawData)
	assert.Len(t, entity.FloorIds, 1)
	assert.Len(t, entity.ToiletIds, 1)
	assert.Equal(t, "eefdd188-4481-41b3-8b82-213a0d6bec4b", entity.FloorIds[0].String())
	assert.Equal(t, "0ee45e00-0fbc-4a19-a50a-579e32ff91bf", entity.ToiletIds[0].String())
}

func TestFromRoomLayoutEntity(t *testing.T) {
	// Create a room layout and convert to entity
	originalLayout := RoomLayout{
		Areas: Areas{
			Showers: []Shower{
				{
					BaseElement: BaseElement{
						Identifier: "7d959d9c-415b-489e-8011-2e364bd5309f",
					},
					CurbHeight: 0.5,
				},
			},
		},
	}

	entity := originalLayout.ToEntity()

	// Convert back from entity
	convertedLayout := FromRoomLayoutEntity(entity)

	// Verify the conversion preserved the data
	assert.Len(t, convertedLayout.Areas.Showers, 1)
	assert.Equal(t, originalLayout.Areas.Showers[0].Identifier, convertedLayout.Areas.Showers[0].Identifier)
	assert.Equal(t, originalLayout.Areas.Showers[0].CurbHeight, convertedLayout.Areas.Showers[0].CurbHeight)
}
