package adapters

import (
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"
	"unicode"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type RenditionStatus string

const (
	RenditionPending   RenditionStatus = "pending"
	RenditionStarted   RenditionStatus = "started"
	RenditionCompleted RenditionStatus = "completed"
	RenditionOutdated  RenditionStatus = "outdated"
	RenditionArchived  RenditionStatus = "archived"
)

type Rendition struct {
	Id        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	Status   RenditionStatus `json:"status"`
	DesignId *uuid.UUID      `json:"design_id"`
	URL      *string         `json:"url,omitempty"`

	// Optional fields used only in outgoing API responses.
	// Their values are populated from the associated room design.
	Title         *string `json:"title,omitempty"`
	Description   *string `json:"description,omitempty"`
	SkuCount      *int32  `json:"skuCount,omitempty"`
	TotalPrice    *int32  `json:"totalPrice,omitempty"`
	Designer      *string `json:"designer,omitempty"`
	DesignerImage *string `json:"designerImage,omitempty"`
}

func (r *Rendition) AlignId(id string) error {
	renditionId, err := uuid.Parse(id)
	if err != nil {
		return fmt.Errorf("invalid rendition UUID %s: %w", id, err)
	}
	zeroUUID := uuid.UUID{}
	if r.Id == zeroUUID {
		r.Id = renditionId
		return nil
	}
	if r.Id != renditionId {
		return errors.New("rendition ID mismatch")
	}
	return nil
}

func (r *Rendition) ToDomain() (result entities.Rendition, err error) {
	result = entities.Rendition{
		Id:        r.Id,
		CreatedAt: r.CreatedAt,
		UpdatedAt: r.UpdatedAt,
		Status:    entities.RenditionStatus(CapitalizeFirstLetter(string(r.Status))),
	}
	if r.URL == nil || *r.URL == "" {
		return
	}
	result.URL, err = url.Parse(*r.URL)
	return
}

func (r *Rendition) ToDiff() (diff entities.RenditionDiff, err error) {
	diff = entities.RenditionDiff{
		Id:     r.Id,
		Status: entities.RenditionStatus(CapitalizeFirstLetter(string(r.Status))),
	}
	if r.URL == nil || *r.URL == "" {
		return
	}
	diff.URL, err = url.Parse(*r.URL)
	return
}

func FromDomainRendition(r entities.Rendition) Rendition {
	result := Rendition{
		Id:        r.Id,
		CreatedAt: r.CreatedAt,
		UpdatedAt: r.UpdatedAt,
		Status:    RenditionStatus(strings.ToLower(string(r.Status))),
		DesignId:  r.DesignId,
	}
	if r.URL != nil {
		url := r.URL.String()
		result.URL = &url
	}
	if r.Title.Valid {
		result.Title = &r.Title.String
	}
	if r.Description.Valid {
		result.Description = &r.Description.String
	}
	if r.SkuCount.Valid {
		result.SkuCount = &r.SkuCount.Int32
	}
	if r.TotalPrice.Valid {
		result.TotalPrice = &r.TotalPrice.Int32
	}
	if r.Designer.Valid {
		result.Designer = &r.Designer.String
	} else {
		designerName := "Bond Studio"
		result.Designer = &designerName
	}
	if r.DesignerImage.Valid {
		result.DesignerImage = &r.DesignerImage.String
	}
	return result
}

func CapitalizeFirstLetter(s string) string {
	if len(s) == 0 {
		return s
	}
	runes := []rune(s)
	runes[0] = unicode.ToTitle(runes[0])
	return string(runes)
}
