package controllers

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type RenditionRetrievalController struct {
	retriever *usecases.RenditionRetriever
}

func NewRenditionRetrievalController(retriever *usecases.RenditionRetriever) *RenditionRetrievalController {
	return &RenditionRetrievalController{retriever: retriever}
}

func (r *RenditionRetrievalController) FetchRenditions(ctx context.Context,
	ids []uuid.UUID, presenter usecases.RenditionsPresenter) {

	r.retriever.Renditions(ctx, ids, presenter)
}

func (r *RenditionRetrievalController) FetchRenditionsForDesign(ctx context.Context,
	designId uuid.UUID, presenter usecases.RenditionsPresenter) {

	r.retriever.RenditionsForDesign(ctx, designId, presenter)
}
