package controllers_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FakeCartInclusionsPresenter for testing
type FakeCartInclusionsPresenter struct {
	PresentErrorCalls          []error
	PresentCartInclusionsCalls []PresentCartInclusionsCall
}

type PresentCartInclusionsCall struct {
	Ctx            context.Context
	CartInclusions usecases.CartInclusions
}

func NewFakeCartInclusionsPresenter() *FakeCartInclusionsPresenter {
	return &FakeCartInclusionsPresenter{
		PresentErrorCalls:          make([]error, 0),
		PresentCartInclusionsCalls: make([]PresentCartInclusionsCall, 0),
	}
}

func (p *FakeCartInclusionsPresenter) PresentError(err error) {
	p.PresentErrorCalls = append(p.PresentErrorCalls, err)
}

func (p *FakeCartInclusionsPresenter) PresentCartInclusions(ctx context.Context, inclusions usecases.CartInclusions) {
	p.PresentCartInclusionsCalls = append(p.PresentCartInclusionsCalls, PresentCartInclusionsCall{
		Ctx:            ctx,
		CartInclusions: inclusions,
	})
}

func TestCartInclusionRetrievalController_FetchCartInclusions(t *testing.T) {
	controller, repo := setupCartInclusionRetrievalController(t)
	presenter := NewFakeCartInclusionsPresenter()

	// Create a test design first
	ctx := context.Background()
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	inclusions := usecases.CartInclusion{
		ProductID:    uuid.New(),
		Include:      true,
		QuantityDiff: 2,
	}

	err = repo.UpsertCartInclusion(ctx, designId, inclusions)
	require.NoError(t, err)

	controller.FetchCartInclusions(ctx, designId, presenter)

	assert.Empty(t, presenter.PresentErrorCalls)
	assert.Len(t, presenter.PresentCartInclusionsCalls, 1)
	assert.Len(t, presenter.PresentCartInclusionsCalls[0].CartInclusions, 1)
}

// Helper functions

func setupCartInclusionRetrievalController(t *testing.T) (*controllers.CartInclusionRetrievalController, *gateways.FakeRelDb) {
	t.Helper()
	fakeDb := gateways.NewFakeRelDb()

	// Create usecase instance
	retriever := usecases.NewCartInclusionRetriever(fakeDb, nil)

	// Create controller
	controller := controllers.NewCartInclusionRetrievalController(retriever)

	return controller, fakeDb
}
