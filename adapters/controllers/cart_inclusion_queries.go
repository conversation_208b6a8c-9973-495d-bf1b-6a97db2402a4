package controllers

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type CartInclusionRetrievalController struct {
	retriever *usecases.CartInclusionRetriever
}

func NewCartInclusionRetrievalController(retriever *usecases.CartInclusionRetriever) *CartInclusionRetrievalController {
	return &CartInclusionRetrievalController{retriever: retriever}
}

func (c *CartInclusionRetrievalController) FetchCartInclusions(ctx context.Context,
	designId uuid.UUID, presenter usecases.CartInclusionsPresenter) {

	c.retriever.RetrieveCartInclusions(ctx, presenter, designId)
}
