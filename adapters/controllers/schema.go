package controllers

import (
	"log"
	"os"

	"github.com/santhosh-tekuri/jsonschema/v6"
)

func Schema(jsonSchemaFilename string) *jsonschema.Schema {
	_, err := os.ReadFile(jsonSchemaFilename)
	if err != nil {
		log.Fatalf("Error reading schema file: %v", err)
	}
	c := jsonschema.NewCompiler()
	schema, err := c.Compile(jsonSchemaFilename)
	if err != nil {
		log.Fatalf("Failed to compile schema: %v", err)
	}
	return schema
}
