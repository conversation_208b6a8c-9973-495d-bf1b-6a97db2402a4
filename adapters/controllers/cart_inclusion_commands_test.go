package controllers_test

import (
	"context"
	"log/slog"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FakeOutcomePresenter is a fake implementation of usecases.OutcomePresenter for testing
type FakeOutcomePresenter struct {
	ConveySuccessCalls int
	PresentErrorCalls  []error
}

func NewFakeOutcomePresenter() *FakeOutcomePresenter {
	return &FakeOutcomePresenter{
		ConveySuccessCalls: 0,
		PresentErrorCalls:  make([]error, 0),
	}
}

func (f *FakeOutcomePresenter) ConveySuccess() {
	f.ConveySuccessCalls++
}

func (f *FakeOutcomePresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

func TestCartInclusionWriteController_UpdateCartInclusions(t *testing.T) {
	controller, repo, _ := setupCartInclusionWriteController(t)
	presenter := NewFakeOutcomePresenter()

	// Create a test design first
	ctx := context.Background()
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productId := uuid.New()
	key := adapters.CartInclusionKey{ProductID: productId, Location: usecases.LocationFloor}
	inclusions := adapters.CartInclusions{
		key: {
			ProductID:    productId,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 2,
		},
	}

	controller.ReplaceCartInclusions(ctx, designId, inclusions, presenter)

	assert.Equal(t, 1, presenter.ConveySuccessCalls)
	assert.Empty(t, presenter.PresentErrorCalls)
}

func TestCartInclusionWriteController_ClearCartInclusions(t *testing.T) {
	controller, repo, _ := setupCartInclusionWriteController(t)
	presenter := NewFakeOutcomePresenter()

	// Create a test design first
	ctx := context.Background()
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	controller.ClearCartInclusions(ctx, designId, presenter)

	assert.Equal(t, 1, presenter.ConveySuccessCalls)
	assert.Empty(t, presenter.PresentErrorCalls)
}

// Helper functions

func setupCartInclusionWriteController(t *testing.T) (*controllers.CartInclusionWriteController, *gateways.FakeRelDb, *slog.Logger) {
	t.Helper()
	logger := slog.Default()
	fakeDb := gateways.NewFakeRelDb()

	// Create usecase instances
	saver := usecases.NewCartInclusionReplacer(fakeDb, logger)
	deleter := usecases.NewCartInclusionDeleter(fakeDb, logger)

	// Create controller
	merger := usecases.NewCartInclusionMerger(fakeDb, logger)
	controller := controllers.NewCartInclusionWriteController(saver, deleter, merger, logger)

	return controller, fakeDb, logger
}
