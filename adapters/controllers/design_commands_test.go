package controllers_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func setup2(t *testing.T) (*controllers.DesignWriteController, *gateways.FakeRelDb) {
	t.Helper()
	r := gateways.NewFakeRelDb()
	catalog := gateways.NewFakeCatalog()
	ai := gateways.NewFakeLLM()
	designCreator := usecases.NewDesignCreater(r, r, catalog, ai, nil)
	designUpdater := usecases.NewDesignUpdater(r)
	designSaver := usecases.NewDesignSaver(r, catalog, ai, nil)
	proseRegenerator := usecases.NewDesignProseRegenerator(r, catalog, ai, nil)
	monolith := gateways.NewMonolith("localhost:4000")
	bulkDesignReplacer := usecases.NewBulkDesignReplacer(r, monolith, nil)
	designDeleter := usecases.NewDesignDeleter(r)
	productSearch := gateways.NewFakeProductSearch()
	designEvolver := usecases.NewDesignEvolver(catalog, productSearch, r, nil)
	designRetriever := usecases.NewDesignRetriever(r)
	ctlr := controllers.NewDesignWriteController(
		designCreator, designUpdater, designSaver, proseRegenerator, bulkDesignReplacer, designDeleter, designEvolver, designRetriever, nil)
	return ctlr, r
}

func genDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	return adapters.Design{
		FloorTile: &floorTile,
		Toilet:    &toilet,
		Vanity:    &vanity,
		Faucet:    &faucet,
		Mirror:    &mirror,
	}
}

const (
	projId = "PRJ-FOOBAR"
)

func TestAddingNewDesign(t *testing.T) {
	ctlr, store := setup2(t)
	testDesign := genDesign()
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	ctlr.SaveDesign(t.Context(), projId, testDesign, presenter)
	require.EqualValues(t, recorder.Code, http.StatusCreated)
	locationHeader := recorder.Header().Get("Location")
	require.NotEmpty(t, locationHeader)
	expectedLocationPrefix := fmt.Sprintf("/projects/%s/designs/", projId)
	if !strings.HasPrefix(locationHeader, expectedLocationPrefix) {
		t.Errorf("Location header prefix mismatch: %s should begin with %s", locationHeader, expectedLocationPrefix)
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	require.Len(t, designs, 1)
	got := designs[0]
	testDesign.ID = got.ID.String()
	want, err := testDesign.ToUsecaseDesign(projId)
	require.NoError(t, err)
	want.Created = got.Created
	want.LastUpdated = got.LastUpdated
	want.Title = got.Title
	want.Description = got.Description
	assert.Equal(t, want, got)
}

func mkDesign(t *testing.T, ctlr *controllers.DesignWriteController) uuid.UUID {
	t.Helper()
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	ctlr.SaveDesign(t.Context(), projId, testDesign, presenter)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	return uuid.MustParse(testDesign.ID)
}

func TestReplacingExistingDesign(t *testing.T) {
	handler, store := setup2(t)
	designId := mkDesign(t, handler)
	testDesign := genDesign()
	testDesign.ID = designId.String()
	toiletUUID := uuid.NewString()
	testDesign.Toilet = &toiletUUID
	want, err := testDesign.ToUsecaseDesign(projId)
	require.NoError(t, err)
	want.LastUpdated = time.Now()
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.SaveDesign(t.Context(), projId, testDesign, presenter)
	require.EqualValues(t, recorder.Code, http.StatusOK)
	got, err := store.ReadDesign(context.Background(), designId)
	require.NoError(t, err)
	want.Created = got.Created
	assert.Greater(t, got.LastUpdated, want.LastUpdated)
	want.LastUpdated = got.LastUpdated
	want.Title = got.Title
	want.Description = got.Description
	assert.Equal(t, want, got)
}

func TestModifyingExistingDesign(t *testing.T) {
	handler, store := setup2(t)
	designId := mkDesign(t, handler)
	design, err := store.ReadDesign(context.Background(), designId)
	require.NoError(t, err)
	toiletUUID := uuid.New()
	design.Toilet = &toiletUUID
	toilet := toiletUUID.String()
	patch := adapters.Design{ID: design.ID.String(), Toilet: &toilet}
	diff, err := patch.ToUsecaseDesignDiff()
	require.NoError(t, err)
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.ModifyDesign(t.Context(), diff, presenter)
	require.EqualValues(t, recorder.Code, http.StatusOK)
	got, err := store.ReadDesign(context.Background(), designId)
	require.NoError(t, err)
	assert.Greater(t, got.LastUpdated, design.LastUpdated)
	design.LastUpdated = got.LastUpdated
	design.Title = got.Title
	design.Description = got.Description
	assert.Equal(t, design, got)
}

func TestDeletingExistingDesign(t *testing.T) {
	handler, store := setup2(t)
	designId := mkDesign(t, handler)
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.DeleteDesign(t.Context(), designId, presenter)
	require.EqualValues(t, recorder.Code, http.StatusNoContent)
	_, err := store.ReadDesign(context.Background(), designId)
	require.ErrorIs(t, err, usecases.ErrNotFound)
}

func TestEvolveProjectDesignsForLayout(t *testing.T) {
	handler, store := setup2(t)

	// Create test room layout
	roomLayout := adapters.RoomLayout{
		Floors: []adapters.Floor{
			{
				ShapedElement: adapters.ShapedElement{
					BaseElement: adapters.BaseElement{
						Identifier: uuid.NewString(),
					},
				},
			},
		},
	}

	// Create test measurements
	measurements := adapters.Measurements{
		FloorArea: func() *float64 { f := 100.0; return &f }(),
	}

	// Create test designs
	design1 := genDesign()
	design1.ID = uuid.NewString()
	ucDesign1, err := design1.ToUsecaseDesign(projId)
	require.NoError(t, err)
	design2 := genDesign()
	design2.ID = uuid.NewString()
	ucDesign2, err := design2.ToUsecaseDesign(projId)
	require.NoError(t, err)
	_, err = store.UpsertDesign(context.Background(), ucDesign1)
	require.NoError(t, err)
	_, err = store.UpsertDesign(context.Background(), ucDesign2)
	require.NoError(t, err)

	// Create a fake presenter
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)

	// This should not panic and should complete successfully
	// Since we're ignoring the output, we just verify it doesn't crash
	handler.EvolveProjectDesignsForLayout(context.Background(), projId, roomLayout, measurements, presenter)
}

func TestEvolveDesignsForLayoutPanicsWithNilPresenter(t *testing.T) {
	handler, _ := setup2(t)

	roomLayout := adapters.RoomLayout{}
	measurements := adapters.Measurements{}

	// Should panic when presenter is nil
	require.Panics(t, func() {
		handler.EvolveProjectDesignsForLayout(context.Background(), projId, roomLayout, measurements, nil)
	})
}
