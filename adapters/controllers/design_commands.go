package controllers

import (
	"context"
	"log/slog"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type DesignWriteController struct {
	maker          *usecases.DesignCreater
	changer        *usecases.DesignUpdater
	saver          *usecases.DesignSaver
	bulkReplacer   *usecases.BulkDesignReplacer
	proseGenerator *usecases.DesignProseRegenerator
	eraser         *usecases.DesignDeleter
	evolver        *usecases.DesignEvolver
	retriever      *usecases.DesignRetriever
	logger         *slog.Logger
}

func NewDesignWriteController(
	maker *usecases.DesignCreater, changer *usecases.DesignUpdater, saver *usecases.DesignSaver,
	proseRegenerator *usecases.DesignProseRegenerator, bulkReplacer *usecases.BulkDesignReplacer, eraser *usecases.DesignDeleter,
	evolver *usecases.DesignEvolver, retriever *usecases.DesignRetriever, logger *slog.Logger) *DesignWriteController {
	if maker == nil {
		panic("maker cannot be nil")
	}
	if changer == nil {
		panic("changer cannot be nil")
	}
	if saver == nil {
		panic("saver cannot be nil")
	}
	if bulkReplacer == nil {
		panic("bulkReplacer cannot be nil")
	}
	if eraser == nil {
		panic("eraser cannot be nil")
	}
	if evolver == nil {
		panic("evolver cannot be nil")
	}
	if retriever == nil {
		panic("retriever cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignWriteController{
		maker: maker, changer: changer, saver: saver,
		bulkReplacer: bulkReplacer, eraser: eraser,
		proseGenerator: proseRegenerator, evolver: evolver, retriever: retriever, logger: logger,
	}
}

func (h *DesignWriteController) SaveDesign(ctx context.Context,
	projectId entities.ProjectId, design adapters.Design, presenter usecases.DesignMutationOutcomePresenter) {

	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	h.logger.InfoContext(ctx, "Saving design...",
		slog.String("designId", design.ID), slog.String("projectId", projectId.String()))
	useCase := usecases.Created
	if len(design.ID) > 2 {
		useCase = usecases.Updated
	} else {
		design.ID = uuid.NewString()
		h.logger.InfoContext(ctx, "Generated new ID for design", slog.String("designId", design.ID))
	}
	d, err := design.ToUsecaseDesign(projectId)
	if err != nil {
		h.logger.ErrorContext(ctx, "Error converting design to usecase",
			slog.String("error", err.Error()), slog.String("designId", design.ID))
		presenter.PresentError(usecases.ErrInvalidPayload)
		return
	}
	d.LastUpdated = time.Now()
	if useCase == usecases.Updated {
		h.saver.SaveDesign(ctx, presenter, d)
		return
	}
	h.maker.CreateDesign(ctx, presenter, d)
}

func (h *DesignWriteController) ModifyDesign(ctx context.Context,
	diff usecases.DesignDiff, presenter usecases.DesignMutationOutcomePresenter) {

	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	h.changer.UpdateDesign(ctx, presenter, diff)
}

func (h *DesignWriteController) ReplaceAllDesignsForProject(ctx context.Context,
	projectId entities.ProjectId, designs []adapters.Design, presenter usecases.OutcomePresenter) {

	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	data := make([]usecases.Design, len(designs))
	for i, d := range designs {
		design, err := d.ToUsecaseDesign(projectId)
		if err != nil {
			h.logger.ErrorContext(ctx, "Error converting design to usecase",
				slog.String("error", err.Error()), slog.String("designId", d.ID))
			presenter.PresentError(usecases.ErrInvalidPayload)
			return
		}
		data[i] = design
	}
	h.bulkReplacer.ReplaceAllDesignsForProject(ctx, presenter, data, projectId)
}

func (h *DesignWriteController) RegenerateDesignProse(ctx context.Context,
	designId uuid.UUID, presenter usecases.DesignMutationOutcomePresenter) {

	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	var zeroUUID uuid.UUID
	if designId == zeroUUID || designId == uuid.Nil {
		presenter.PresentError(usecases.ErrInvalidPayload)
		return
	}
	h.proseGenerator.RegenerateDesignProse(ctx, presenter, designId)
}

func (h *DesignWriteController) DeleteDesign(ctx context.Context, designId uuid.UUID, presenter usecases.OutcomePresenter) {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	h.eraser.DeleteDesign(ctx, presenter, designId)
}

func (h *DesignWriteController) EvolveProjectDesignsForLayout(ctx context.Context,
	projectId entities.ProjectId, roomLayout adapters.RoomLayout, measurements adapters.Measurements,
	presenter usecases.DesignsPresenter) {

	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}

	roomLayoutWithMeasurements := roomLayout
	roomLayoutWithMeasurements.Measurements = &measurements
	layoutEntity := roomLayoutWithMeasurements.ToEntity()

	h.evolver.EvolveProjectDesignsForLayout(ctx, projectId, layoutEntity, presenter)
}
