package controllers

import (
	"context"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type TemplateRetrievalController struct {
	retriever *usecases.TemplateRetriever
	generator *usecases.DesignGenerator
}

func NewTemplateRetrievalController(retriever *usecases.TemplateRetriever, generator *usecases.DesignGenerator) *TemplateRetrievalController {
	if usecases.IsNil(retriever) {
		panic("retriever cannot be nil")
	}
	if usecases.IsNil(generator) {
		panic("generator cannot be nil")
	}
	return &TemplateRetrievalController{retriever: retriever, generator: generator}
}

func (c *TemplateRetrievalController) FetchTemplate(ctx context.Context, templateId uuid.UUID, presenter usecases.TemplatesPresenter) {
	c.retriever.RetrieveTemplate(ctx, presenter, templateId)
}

func (c *TemplateRetrievalController) FetchTemplateByLegacyId(ctx context.Context, legacyId string, presenter usecases.TemplatesPresenter) {
	c.retriever.RetrieveTemplateByLegacyId(ctx, presenter, legacyId)
}

func (c *TemplateRetrievalController) FetchAllTemplates(ctx context.Context, presenter usecases.TemplatesPresenter) {
	c.retriever.RetrieveAllTemplates(ctx, presenter)
}

func (c *TemplateRetrievalController) GenerateDesignsFromTemplates(ctx context.Context,
	projectId entities.ProjectId, templateIds []uuid.UUID, presenter usecases.DesignsPresenter) {
	c.generator.GenerateDesignsForProjectFromTemplates(ctx, projectId, templateIds, presenter)
}
