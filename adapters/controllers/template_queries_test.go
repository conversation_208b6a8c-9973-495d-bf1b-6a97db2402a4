package controllers_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FakeMonolith is a fake implementation of the monolith interface for testing
type FakeMonolith struct {
	LayoutToReturn           entities.RoomLayout
	ShouldFail               bool
	ErrorToReturn            error
	GetLayoutForProjectCalls []entities.ProjectId
}

func (f *FakeMonolith) UpdateCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID) error {
	return nil // Not needed for this test
}

func (f *FakeMonolith) GetLayoutForProject(ctx context.Context, projectId entities.ProjectId) (entities.RoomLayout, error) {
	f.GetLayoutForProjectCalls = append(f.GetLayoutForProjectCalls, projectId)
	if f.ShouldFail {
		return entities.RoomLayout{}, f.ErrorToReturn
	}
	return f.LayoutToReturn, nil
}

func NewFakeMonolith() *FakeMonolith {
	return &FakeMonolith{
		GetLayoutForProjectCalls: make([]entities.ProjectId, 0),
	}
}

func setupTemplateRetrieval(t *testing.T) (*controllers.TemplateRetrievalController, *gateways.FakeRelDb, *FakeMonolith) {
	t.Helper()
	const defaultRoomLayoutFilename = "default_room_layout.json"
	defaultRoomLayout, err := os.ReadFile(defaultRoomLayoutFilename)
	require.NoError(t, err)
	r := gateways.NewFakeRelDb()
	rooms := gateways.NewRooms(defaultRoomLayout)
	retriever := usecases.NewTemplateRetriever(r)
	monolith := NewFakeMonolith()
	ai := gateways.NewFakeLLM()
	productSearch := gateways.NewFakeProductSearch()
	generator := usecases.NewDesignGenerator(r, monolith, rooms, productSearch, ai, nil)
	ctlr := controllers.NewTemplateRetrievalController(retriever, generator)
	return ctlr, r, monolith
}

func TestNewTemplateRetrievalController(t *testing.T) {
	t.Run("should create controller with valid dependencies", func(t *testing.T) {
		controller, _, _ := setupTemplateRetrieval(t)
		assert.NotNil(t, controller)
	})

	t.Run("should panic with nil retriever", func(t *testing.T) {
		_, r, monolith := setupTemplateRetrieval(t)
		rooms := gateways.NewRooms([]byte(`{}`))
		ai := gateways.NewFakeLLM()
		productSearch := gateways.NewFakeProductSearch()
		generator := usecases.NewDesignGenerator(r, monolith, rooms, productSearch, ai, nil)
		assert.Panics(t, func() {
			controllers.NewTemplateRetrievalController(nil, generator)
		})
	})

	t.Run("should panic with nil generator", func(t *testing.T) {
		_, r, _ := setupTemplateRetrieval(t)
		retriever := usecases.NewTemplateRetriever(r)
		assert.Panics(t, func() {
			controllers.NewTemplateRetrievalController(retriever, nil)
		})
	})
}

func TestFetchingTemplate(t *testing.T) {
	ctlr, store, _ := setupTemplateRetrieval(t)

	// First create a template to fetch
	templateId := uuid.New()
	testTemplate := usecases.Template{
		ID:   templateId,
		Name: "Test Template",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Description: "A test template",
	}

	// Insert template directly into store
	_, err := store.InsertTemplate(context.Background(), testTemplate, "")
	require.NoError(t, err)

	// Now fetch it through the controller
	recorder := httptest.NewRecorder()
	presenter := presenters.NewTemplatesPresenter(nil, recorder, true)

	ctlr.FetchTemplate(context.Background(), templateId, presenter)

	require.Equal(t, http.StatusOK, recorder.Code)
	assert.Contains(t, recorder.Body.String(), "Test Template")
}

func TestFetchingAllTemplates(t *testing.T) {
	ctlr, store, _ := setupTemplateRetrieval(t)

	// Create multiple templates
	template1 := usecases.Template{
		ID:   uuid.New(),
		Name: "Template 1",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
	}
	template2 := usecases.Template{
		ID:   uuid.New(),
		Name: "Template 2",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Bold,
			Style:       usecases.Traditional,
		},
	}

	// Insert templates into store
	_, err := store.InsertTemplate(context.Background(), template1, "")
	require.NoError(t, err)
	_, err = store.InsertTemplate(context.Background(), template2, "")
	require.NoError(t, err)

	// Fetch all templates
	recorder := httptest.NewRecorder()
	presenter := presenters.NewTemplatesPresenter(nil, recorder, true)

	ctlr.FetchAllTemplates(context.Background(), presenter)

	require.Equal(t, http.StatusOK, recorder.Code)
	assert.Contains(t, recorder.Body.String(), "Template 1")
	assert.Contains(t, recorder.Body.String(), "Template 2")
}

func TestFetchingTemplateByLegacyId(t *testing.T) {
	ctlr, store, _ := setupTemplateRetrieval(t)

	// Create a template with a legacy ID
	templateId := uuid.New()
	legacyId := "42"
	testTemplate := usecases.Template{
		ID:       templateId,
		LegacyId: &legacyId,
		Name:     "Legacy Template",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Description: "A template with legacy ID",
	}

	// Insert template directly into store
	_, err := store.InsertTemplate(context.Background(), testTemplate, legacyId)
	require.NoError(t, err)

	// Now fetch it by legacy ID through the controller
	recorder := httptest.NewRecorder()
	presenter := presenters.NewTemplatesPresenter(nil, recorder, true)

	ctlr.FetchTemplateByLegacyId(context.Background(), legacyId, presenter)

	require.Equal(t, http.StatusOK, recorder.Code)
	assert.Contains(t, recorder.Body.String(), "Legacy Template")
}

func TestGenerateDesignsFromTemplates(t *testing.T) {
	ctlr, store, monolith := setupTemplateRetrieval(t)
	ctx := context.Background()
	projectId := entities.NewProjectId("PRJ-123")
	templateId := uuid.New()

	// Setup a template in the store
	template := usecases.Template{
		ID:   templateId,
		Name: "Test Template",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
	}
	_, err := store.InsertTemplate(ctx, template, "")
	require.NoError(t, err)

	// Setup fake monolith to return a layout
	monolith.LayoutToReturn = entities.RoomLayout{Id: uuid.New()}

	// Setup a presenter
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)

	// Call the method
	ctlr.GenerateDesignsFromTemplates(ctx, projectId, []uuid.UUID{templateId}, presenter)

	// Assertions
	require.Equal(t, http.StatusOK, recorder.Code)
	assert.Contains(t, recorder.Body.String(), `"title":"Test Template"`)
	assert.Len(t, monolith.GetLayoutForProjectCalls, 1, "expected GetLayoutForProject to be called")
	assert.Equal(t, projectId, monolith.GetLayoutForProjectCalls[0])
}
