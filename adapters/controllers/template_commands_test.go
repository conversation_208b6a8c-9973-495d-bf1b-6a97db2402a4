package controllers_test

import (
	"context"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func setupTemplate(t *testing.T) (*controllers.TemplateWriteController, *gateways.FakeRelDb) {
	t.Helper()
	r := gateways.NewFakeRelDb()
	logger := slog.Default()
	creator := usecases.NewTemplateCreater(r, logger)
	ctlr := controllers.NewTemplateWriteController(creator, logger)
	return ctlr, r
}

func genTemplate() adapters.Template {
	return adapters.Template{
		Name: "Test Template",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Description:     "A test template",
		Inspiration:     "Test inspiration",
		Atmosphere:      "cozy, modern",
		ColorPalette:    "#FFFFFF, #000000",
		MaterialPalette: "wood, metal",
		Materials: adapters.Materials{
			VanityDict: map[string]uuid.UUID{
				"24": uuid.New(),
				"36": uuid.New(),
			},
			FaucetDict: map[string]uuid.UUID{
				"24": uuid.New(),
				"36": uuid.New(),
			},
		},
	}
}

func TestNewTemplateWriteController(t *testing.T) {
	logger := slog.Default()

	t.Run("should create controller with valid creator", func(t *testing.T) {
		r := gateways.NewFakeRelDb()
		creator := usecases.NewTemplateCreater(r, logger)
		controller := controllers.NewTemplateWriteController(creator, logger)
		assert.NotNil(t, controller)
	})

	t.Run("should panic with nil creator", func(t *testing.T) {
		assert.Panics(t, func() {
			controllers.NewTemplateWriteController(nil, logger)
		})
	})
}

func TestSavingNewTemplate(t *testing.T) {
	ctlr, store := setupTemplate(t)
	templateId := uuid.New()
	testTemplate := genTemplate()
	testTemplate.ID = templateId.String()

	recorder := httptest.NewRecorder()
	presenter := presenters.NewTemplateCreationOutcomePresenter(nil, recorder)

	ctlr.SaveTemplate(context.Background(), templateId, testTemplate, presenter)

	require.Equal(t, http.StatusCreated, recorder.Code)

	// Verify template was saved to store
	savedTemplate, err := store.ReadTemplate(context.Background(), templateId)
	require.NoError(t, err)

	// Verify the saved template has the correct properties
	assert.Equal(t, templateId, savedTemplate.ID)
	assert.Equal(t, "Test Template", savedTemplate.Name)
	assert.Equal(t, usecases.Neutral, savedTemplate.ColorScheme)
	assert.Equal(t, usecases.Modern, savedTemplate.Style)
	assert.Equal(t, "A test template", savedTemplate.Description)
	assert.Equal(t, "Test inspiration", savedTemplate.Inspiration)
}

func TestTemplateIdAlignment(t *testing.T) {
	ctlr, store := setupTemplate(t)
	templateId := uuid.New()
	differentId := uuid.New()

	testTemplate := genTemplate()
	testTemplate.ID = differentId.String() // Template has different ID than URL parameter

	recorder := httptest.NewRecorder()
	presenter := presenters.NewTemplateCreationOutcomePresenter(nil, recorder)

	ctlr.SaveTemplate(context.Background(), templateId, testTemplate, presenter)

	require.Equal(t, http.StatusCreated, recorder.Code)

	// Verify template was saved with the URL templateId, not the template's original ID
	savedTemplate, err := store.ReadTemplate(context.Background(), templateId)
	require.NoError(t, err)
	assert.Equal(t, templateId, savedTemplate.ID)

	// Verify the original template ID is not found
	_, err = store.ReadTemplate(context.Background(), differentId)
	require.ErrorIs(t, err, usecases.ErrNotFound)
}

func TestTemplateConversionError(t *testing.T) {
	ctlr, store := setupTemplate(t)
	templateId := uuid.New()

	// Create invalid template (mismatched vanity/faucet dict lengths)
	invalidTemplate := genTemplate()
	invalidTemplate.Materials.VanityDict = map[string]uuid.UUID{"24": uuid.New()}
	invalidTemplate.Materials.FaucetDict = map[string]uuid.UUID{"24": uuid.New(), "36": uuid.New()}

	recorder := httptest.NewRecorder()
	presenter := presenters.NewTemplateCreationOutcomePresenter(nil, recorder)

	ctlr.SaveTemplate(context.Background(), templateId, invalidTemplate, presenter)

	require.Equal(t, http.StatusBadRequest, recorder.Code)

	// Verify template was not saved to store
	_, err := store.ReadTemplate(context.Background(), templateId)
	require.ErrorIs(t, err, usecases.ErrNotFound)
}

func TestNilPresenterPanic(t *testing.T) {
	ctlr, _ := setupTemplate(t)
	templateId := uuid.New()
	testTemplate := genTemplate()

	assert.Panics(t, func() {
		ctlr.SaveTemplate(context.Background(), templateId, testTemplate, nil)
	})
}
