package adapters

import (
	"encoding/json"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type Preset struct {
	Id string `json:"id"`

	Rendition    Rendition       `json:"rendition"`
	Design       Design          `json:"design"`
	Measurements json.RawMessage `json:"measurements"`
	RoomLayout   RoomLayout      `json:"roomLayout"`
}

func FromUsecasePreset(preset usecases.Preset) Preset {
	return Preset{
		Id:           preset.Id,
		RoomLayout:   FromRoomLayoutEntity(preset.RoomLayout),
		Measurements: preset.Measurements,
		Design:       FromUsecaseDesign(preset.Design),
		Rendition:    FromDomainRendition(preset.Rendition),
	}
}
