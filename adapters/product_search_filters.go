package adapters

import (
	"log"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FilterParams is used for Product Search.
// It is the Go equivalent of its Pydantic namesake.
type FilterParams struct {
	// Category is a required field.
	Category usecases.Category `json:"category"`

	// The min price in cents.
	PriceGTE *uint `json:"price_gte,omitempty"`
	// The max price in cents. Must be > 1.
	PriceLTE *uint `json:"price_lte,omitempty"`

	// Lead time max. Must be > 2.
	LeadTimeLTE *uint `json:"lead_time_lte,omitempty"`

	// Optional lists of colors & styles that the product should have.
	Colors []usecases.ColorGroup `json:"colors,omitempty"`
	Styles []usecases.Style      `json:"styles,omitempty"`

	Collection *string `json:"collection,omitempty"`

	FaucetHoleSpacingCompatibility *FaucetHoleSpacing `json:"faucet_hole_spacing_compatibility,omitempty"`

	LightingTypes []usecases.LightingType `json:"lighting_types,omitempty"`
	// Max lighting length. Must be > 0.
	LightingLengthLTE *float64 `json:"lighting_length_lte,omitempty"`
	// Min number of lights for decorative lighting. Must be > 0.
	NumberOfLightsGTE *uint `json:"number_of_lights_gte,omitempty"`
	// Max number of light bulbs for decorative lighting. Must be > 1.
	NumberOfLightsLTE *uint `json:"number_of_lights_lte,omitempty"`

	MirrorType           *MirrorType            `json:"mirror_type,omitempty"`
	MirrorSurfaceMounted *bool                  `json:"mirror_surface_mounted,omitempty"`
	MirrorRecessed       *bool                  `json:"mirror_recessed,omitempty"`
	MirrorHasLED         *bool                  `json:"mirror_has_led,omitempty"`
	MirrorShapes         []usecases.MirrorShape `json:"mirror_shapes,omitempty"`

	// Min mirror height. Must be > 0.
	MirrorHeightGTE *float64 `json:"mirror_height_gte,omitempty"`
	// Max mirror height. Must be > 0.
	MirrorHeightLTE *float64 `json:"mirror_height_lte,omitempty"`
	// Min mirror width. Must be > 0.
	MirrorWidthGTE *float64 `json:"mirror_width_gte,omitempty"`
	// Max mirror width. Must be > 0.
	MirrorWidthLTE *float64 `json:"mirror_width_lte,omitempty"`

	// Min shelf count for shelving units. Must be > 0.
	ShelfCountGTE *uint `json:"shelf_count_gte,omitempty"`
	// Max shelf count for shelving units. Must be > 1.
	ShelfCountLTE *uint `json:"shelf_count_lte,omitempty"`
	// Min length for shelving units. Must be > 0.
	ShelfLengthGTE *float64 `json:"shelf_length_gte,omitempty"`
	// Max length for shelving units. Must be > 0.
	ShelfLengthLTE *float64 `json:"shelf_length_lte,omitempty"`

	ShowerHasTubSpout     *bool                         `json:"shower_has_tub_spout,omitempty"`
	HandshowerKitIncluded *bool                         `json:"handshower_kit_included,omitempty"`
	ShowerEnclosureType   *usecases.ShowerEnclosureType `json:"shower_enclosure_type,omitempty"`

	// Min square foot coverage for tiling. Must be > 0.
	SqftCoverageGTE *float64 `json:"sqft_coverage_gte,omitempty"`
	// Max square foot coverage for tiling. Must be > 0.
	SqftCoverageLTE *float64 `json:"sqft_coverage_lte,omitempty"`

	TileTypes    []usecases.TileType    `json:"tile_types,omitempty"`
	TileShapes   []usecases.TileShape   `json:"tile_shapes,omitempty"`
	TileFinishes []usecases.TileFinish  `json:"tile_finishes,omitempty"`
	TileLocation *usecases.TileLocation `json:"tile_location,omitempty"`

	TubType *usecases.TubType `json:"tub_type,omitempty"`
	// Min tub length. Must be > 0.
	TubLengthGTE *float64 `json:"tub_length_gte,omitempty"`
	// Max tub length. Must be > 0.
	TubLengthLTE *float64 `json:"tub_length_lte,omitempty"`

	TubFillerMountingPosition *usecases.TubFillerMountingPosition `json:"tub_filler_mounting_position,omitempty"`

	ToiletType *ToiletType `json:"toilet_mounting,omitempty"`
	HasBidet   *bool       `json:"has_bidet,omitempty"`

	VanityType *usecases.VanityType `json:"vanity_type,omitempty"`
	// Min vanity length. Must be > 0.
	VanityLengthGTE *float64 `json:"vanity_length_gte,omitempty"`
	// Max vanity length. Must be > 0.
	VanityLengthLTE *float64 `json:"vanity_length_lte,omitempty"`
	// Min number of sinks min for vanities. Must be >= 1.
	NumberOfSinksGTE *uint `json:"number_of_sinks_gte,omitempty"`
	// Max number of sinks for vanities. Must be >= 1.
	NumberOfSinksLTE *uint `json:"number_of_sinks_lte,omitempty"`

	WallpaperApplications []usecases.WallpaperApplication `json:"wallpaper_applications,omitempty"`
	WallpaperPatterns     []usecases.WallpaperPattern     `json:"wallpaper_patterns,omitempty"`
}

func (fp *FilterParams) ToUsecase() usecases.ProductSearchFilters {
	result := usecases.ProductSearchFilters{
		Category: fp.Category,
		Colors:   fp.Colors,
		Styles:   fp.Styles,
	}

	// Price range conversion
	if fp.PriceGTE != nil {
		result.PriceRange.Min = *fp.PriceGTE
	}
	result.PriceRange.Max = fp.PriceLTE

	// Lead time conversion
	if fp.LeadTimeLTE != nil {
		result.MaxLeadTime = fp.LeadTimeLTE
	}

	// Collections conversion
	if fp.Collection != nil {
		result.Collections = []string{*fp.Collection}
	}

	// Faucet hole spacing conversion
	if fp.FaucetHoleSpacingCompatibility != nil {
		if usecaseFHS, err := fp.FaucetHoleSpacingCompatibility.ToUsecase(); err == nil {
			result.FaucetHoleSpacingCompatibility = &usecaseFHS
		} else {
			log.Printf("Failed to convert faucet hole spacing (%s) to usecase: %v",
				*fp.FaucetHoleSpacingCompatibility, err)
		}
	}

	// Lighting filters
	result.LightingTypes = fp.LightingTypes
	if fp.NumberOfLightsGTE != nil {
		result.LightingNumBulbs.Min = *fp.NumberOfLightsGTE
	}
	result.LightingNumBulbs.Max = fp.NumberOfLightsLTE

	switch fp.Category {
	case usecases.CategoryLighting:
		if fp.LightingLengthLTE != nil {
			result.Length.Max = fp.LightingLengthLTE
		}
	case usecases.CategoryMirror:
		// Length range (from mirror width)
		if fp.MirrorWidthGTE != nil {
			result.Length.Min = *fp.MirrorWidthGTE
		}
		result.Length.Max = fp.MirrorWidthLTE
	case usecases.CategoryShelving:
		if fp.ShelfLengthGTE != nil {
			result.Length.Min = *fp.ShelfLengthGTE
		}
		result.Length.Max = fp.ShelfLengthLTE
	case usecases.CategoryTub:
		if fp.TubLengthGTE != nil {
			result.Length.Min = *fp.TubLengthGTE
		}
		result.Length.Max = fp.TubLengthLTE
	case usecases.CategoryVanity:
		if fp.VanityLengthGTE != nil {
			result.Length.Min = *fp.VanityLengthGTE
		}
		result.Length.Max = fp.VanityLengthLTE
	}

	// Mirror filters
	if fp.MirrorType != nil {
		isMedCab := *fp.MirrorType == MirrorTypeMedicineCabinet
		result.MirrorIsMedicineCabinet = &isMedCab
	}
	result.MirrorShapes = fp.MirrorShapes
	if fp.MirrorHasLED != nil {
		result.MirrorIsLighted = fp.MirrorHasLED
	}

	// Height range (from mirror dimensions)
	if fp.MirrorHeightGTE != nil {
		result.Height.Min = *fp.MirrorHeightGTE
	}
	result.Height.Max = fp.MirrorHeightLTE

	// Shelf filters
	if fp.ShelfCountGTE != nil {
		result.NumShelves.Min = *fp.ShelfCountGTE
	}
	result.NumShelves.Max = fp.ShelfCountLTE

	// Shower filters
	result.ShowerHasTubSpout = fp.ShowerHasTubSpout
	result.ShowerHasHandheldShowerhead = fp.HandshowerKitIncluded
	if fp.ShowerEnclosureType != nil {
		result.ShowerEnclosureTypes = []usecases.ShowerEnclosureType{*fp.ShowerEnclosureType}
	}

	// Tile filters
	result.TileLocation = fp.TileLocation
	result.TileTypes = fp.TileTypes
	result.TileShapes = fp.TileShapes
	result.TileFinishes = fp.TileFinishes
	if fp.SqftCoverageGTE != nil {
		result.TileAreaCoverageSqFt.Min = *fp.SqftCoverageGTE
	}
	result.TileAreaCoverageSqFt.Max = fp.SqftCoverageLTE

	// Toilet filters
	if fp.ToiletType != nil {
		switch *fp.ToiletType {
		case ToiletTypeFreestanding:
			pos := usecases.ToiletMountingPositionFloor
			result.ToiletMountingPosition = &pos
		case ToiletTypeWallHung:
			pos := usecases.ToiletMountingPositionWall
			result.ToiletMountingPosition = &pos
		}
	}
	result.ToiletMustHaveBidet = fp.HasBidet

	// Tub filters
	result.TubType = fp.TubType
	result.TubFillerMountingPosition = fp.TubFillerMountingPosition

	// Vanity filters
	if fp.VanityType != nil {
		result.VanityTypes = []usecases.VanityType{*fp.VanityType}
	}
	if fp.NumberOfSinksGTE != nil {
		result.VanityNumSinks.Min = *fp.NumberOfSinksGTE
	}
	result.VanityNumSinks.Max = fp.NumberOfSinksLTE

	// Wallpaper filters
	result.WallpaperApplications = fp.WallpaperApplications
	result.WallpaperPatterns = fp.WallpaperPatterns

	return result
}

func FiltersFromUsecase(usecaseFilters usecases.ProductSearchFilters) FilterParams {
	result := FilterParams{
		Category: usecaseFilters.Category,
		Colors:   usecaseFilters.Colors,
		Styles:   usecaseFilters.Styles,
	}

	// Price range conversion
	if usecaseFilters.PriceRange.Min > 0 {
		result.PriceGTE = &usecaseFilters.PriceRange.Min
	}
	result.PriceLTE = usecaseFilters.PriceRange.Max

	// Lead time conversion
	if usecaseFilters.MaxLeadTime != nil {
		result.LeadTimeLTE = usecaseFilters.MaxLeadTime
	}

	// Collections conversion
	if len(usecaseFilters.Collections) > 0 {
		result.Collection = &usecaseFilters.Collections[0]
	}

	// Faucet hole spacing conversion
	if usecaseFilters.FaucetHoleSpacingCompatibility != nil {
		adapterFHS := FaucetHoleSpacingFromUsecase(*usecaseFilters.FaucetHoleSpacingCompatibility)
		result.FaucetHoleSpacingCompatibility = &adapterFHS
	}

	// Lighting filters
	result.LightingTypes = usecaseFilters.LightingTypes
	if usecaseFilters.LightingNumBulbs.Min > 0 {
		result.NumberOfLightsGTE = &usecaseFilters.LightingNumBulbs.Min
	}
	result.NumberOfLightsLTE = usecaseFilters.LightingNumBulbs.Max

	// Length range (to lighting length)
	result.LightingLengthLTE = usecaseFilters.Length.Max

	// Mirror filters
	if usecaseFilters.MirrorIsMedicineCabinet != nil {
		if *usecaseFilters.MirrorIsMedicineCabinet {
			mirrorType := MirrorTypeMedicineCabinet
			result.MirrorType = &mirrorType
		} else {
			mirrorType := MirrorTypeMirror
			result.MirrorType = &mirrorType
		}
	}
	result.MirrorHasLED = usecaseFilters.MirrorIsLighted
	result.MirrorShapes = usecaseFilters.MirrorShapes

	// Height range (to mirror dimensions)
	if usecaseFilters.Height.Min > 0 {
		result.MirrorHeightGTE = &usecaseFilters.Height.Min
	}
	result.MirrorHeightLTE = usecaseFilters.Height.Max

	// Length range (to mirror width)
	if usecaseFilters.Length.Min > 0 {
		result.MirrorWidthGTE = &usecaseFilters.Length.Min
	}
	result.MirrorWidthLTE = usecaseFilters.Length.Max

	// Shelf filters
	if usecaseFilters.NumShelves.Min > 0 {
		result.ShelfCountGTE = &usecaseFilters.NumShelves.Min
	}
	result.ShelfCountLTE = usecaseFilters.NumShelves.Max

	// Length range (to shelf length)
	if usecaseFilters.Length.Min > 0 {
		result.ShelfLengthGTE = &usecaseFilters.Length.Min
	}
	result.ShelfLengthLTE = usecaseFilters.Length.Max

	// Shower filters
	result.ShowerHasTubSpout = usecaseFilters.ShowerHasTubSpout
	result.HandshowerKitIncluded = usecaseFilters.ShowerHasHandheldShowerhead
	if len(usecaseFilters.ShowerEnclosureTypes) > 0 {
		result.ShowerEnclosureType = &usecaseFilters.ShowerEnclosureTypes[0]
	}

	// Tile filters
	result.TileLocation = usecaseFilters.TileLocation
	result.TileTypes = usecaseFilters.TileTypes
	result.TileShapes = usecaseFilters.TileShapes
	result.TileFinishes = usecaseFilters.TileFinishes
	if usecaseFilters.TileAreaCoverageSqFt.Min > 0 {
		result.SqftCoverageGTE = &usecaseFilters.TileAreaCoverageSqFt.Min
	}
	result.SqftCoverageLTE = usecaseFilters.TileAreaCoverageSqFt.Max

	// Toilet filters
	if usecaseFilters.ToiletMountingPosition != nil {
		switch *usecaseFilters.ToiletMountingPosition {
		case usecases.ToiletMountingPositionFloor:
			toiletType := ToiletTypeFreestanding
			result.ToiletType = &toiletType
		case usecases.ToiletMountingPositionWall:
			toiletType := ToiletTypeWallHung
			result.ToiletType = &toiletType
		}
	}
	result.HasBidet = usecaseFilters.ToiletMustHaveBidet

	// Tub filters
	result.TubType = usecaseFilters.TubType
	result.TubFillerMountingPosition = usecaseFilters.TubFillerMountingPosition

	// Length range (to tub length)
	if usecaseFilters.Length.Min > 0 {
		result.TubLengthGTE = &usecaseFilters.Length.Min
	}
	result.TubLengthLTE = usecaseFilters.Length.Max

	// Vanity filters
	if len(usecaseFilters.VanityTypes) > 0 {
		result.VanityType = &usecaseFilters.VanityTypes[0]
	}
	if usecaseFilters.VanityNumSinks.Min > 0 {
		result.NumberOfSinksGTE = &usecaseFilters.VanityNumSinks.Min
	}
	result.NumberOfSinksLTE = usecaseFilters.VanityNumSinks.Max

	// Length range (to vanity length)
	if usecaseFilters.Length.Min > 0 {
		result.VanityLengthGTE = &usecaseFilters.Length.Min
	}
	result.VanityLengthLTE = usecaseFilters.Length.Max

	// Wallpaper filters
	result.WallpaperApplications = usecaseFilters.WallpaperApplications
	result.WallpaperPatterns = usecaseFilters.WallpaperPatterns

	return result
}
