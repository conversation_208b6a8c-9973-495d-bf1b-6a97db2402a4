package gateways

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestNewProductSearch(t *testing.T) {
	t.Run("valid hostname", func(t *testing.T) {
		productSearch := NewProductSearch("example.com")
		assert.NotNil(t, productSearch)
		assert.Equal(t, "example.com", productSearch.hostname)
	})

	t.Run("empty hostname panics", func(t *testing.T) {
		assert.Panics(t, func() {
			NewProductSearch("")
		})
	})
}

func TestProductSearch_FindProducts(t *testing.T) {
	productId1 := uuid.New()
	productId2 := uuid.New()

	t.Run("successful request", func(t *testing.T) {
		// Create a TLS mock server
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Verify the request
			assert.Equal(t, "/search/products", r.URL.Path)
			assert.Equal(t, "category=Vanity&colors=White", r.URL.RawQuery)
			assert.Equal(t, "GET", r.Method)

			// Return mock response
			response := struct {
				Data []struct {
					Id string `json:"id"`
				} `json:"data"`
			}{
				Data: []struct {
					Id string `json:"id"`
				}{
					{Id: productId1.String()},
					{Id: productId2.String()},
				},
			}

			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		// Extract hostname from server URL (remove https://)
		hostname := server.URL[8:] // Remove "https://"
		productSearch := NewProductSearch(hostname)

		// Use the test server's client which accepts self-signed certificates
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		result, err := productSearch.FindProducts(context.Background(), "category=Vanity&colors=White")
		require.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Contains(t, result, productId1)
		assert.Contains(t, result, productId2)
	})

	t.Run("input validation error", func(t *testing.T) {
		productSearch := NewProductSearch("example.com")

		_, err := productSearch.FindProducts(context.Background(), "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "urlQueryParams cannot be empty")
	})

	t.Run("HTTP error", func(t *testing.T) {
		// Create a server that returns an error
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		productSearch := NewProductSearch(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		_, err := productSearch.FindProducts(context.Background(), "category=Vanity")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "error response from Product Search for query 'category=Vanity'")
	})

	t.Run("empty response data", func(t *testing.T) {
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			response := struct {
				Data []struct {
					Id string `json:"id"`
				} `json:"data"`
			}{
				Data: []struct {
					Id string `json:"id"`
				}{},
			}
			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		productSearch := NewProductSearch(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		result, err := productSearch.FindProducts(context.Background(), "category=Vanity")
		require.NoError(t, err)
		assert.Len(t, result, 0)
	})

	t.Run("invalid JSON response", func(t *testing.T) {
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			_, err := w.Write([]byte("invalid json"))
			assert.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		productSearch := NewProductSearch(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		_, err := productSearch.FindProducts(context.Background(), "category=Vanity")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to unmarshal response from Product Search for query 'category=Vanity'")
	})

	t.Run("invalid UUID in response", func(t *testing.T) {
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Return invalid UUID that will cause parsing to fail
			response := struct {
				Data []struct {
					Id string `json:"id"`
				} `json:"data"`
			}{
				Data: []struct {
					Id string `json:"id"`
				}{
					{Id: productId1.String()},
					{Id: "invalid-uuid"}, // This will cause parsing error
					{Id: productId2.String()},
				},
			}
			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		productSearch := NewProductSearch(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		_, err := productSearch.FindProducts(context.Background(), "category=Vanity")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to parse product ID 'invalid-uuid' at index 1 for query 'category=Vanity'")
	})
}

func TestProductSearch_FindProductsViaAI(t *testing.T) {
	productId1 := uuid.New()
	productId2 := uuid.New()

	t.Run("successful AI search request", func(t *testing.T) {
		// Create a TLS mock server
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Verify the request
			assert.Equal(t, "/search/ai/v3/search", r.URL.Path)
			assert.Equal(t, "POST", r.Method)
			assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

			// Read and verify request body
			body, err := io.ReadAll(r.Body)
			require.NoError(t, err)

			var requestBody struct {
				Filters adapters.FilterParams `json:"filters"`
			}
			err = json.Unmarshal(body, &requestBody)
			require.NoError(t, err)
			assert.Equal(t, usecases.CategoryVanity, requestBody.Filters.Category)

			// Return mock response - AI Search format with productIds array
			response := struct {
				ProductIds []uuid.UUID `json:"productIds"`
			}{
				ProductIds: []uuid.UUID{productId1, productId2},
			}

			w.Header().Set("Content-Type", "application/json")
			err = json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		// Extract hostname from server URL (remove https://)
		hostname := server.URL[8:]
		productSearch := NewProductSearch(hostname)

		// Use the test server's client which accepts self-signed certificates
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		// Create test filters
		filters := usecases.ProductSearchFilters{
			Category: usecases.CategoryVanity,
			Colors:   []usecases.ColorGroup{usecases.White},
		}

		result, err := productSearch.FindProductsViaAI(context.Background(), filters)
		require.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Contains(t, result, productId1)
		assert.Contains(t, result, productId2)
	})

	t.Run("AI search HTTP error", func(t *testing.T) {
		// Create a server that returns an error
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		productSearch := NewProductSearch(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		filters := usecases.ProductSearchFilters{
			Category: usecases.CategoryVanity,
		}

		_, err := productSearch.FindProductsViaAI(context.Background(), filters)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "error response from AI Search for filters")
	})

	t.Run("AI search empty response", func(t *testing.T) {
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			response := struct {
				ProductIds []uuid.UUID `json:"productIds"`
			}{
				ProductIds: []uuid.UUID{},
			}
			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		productSearch := NewProductSearch(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		filters := usecases.ProductSearchFilters{
			Category: usecases.CategoryVanity,
		}

		result, err := productSearch.FindProductsViaAI(context.Background(), filters)
		require.NoError(t, err)
		assert.Len(t, result, 0)
	})
}
