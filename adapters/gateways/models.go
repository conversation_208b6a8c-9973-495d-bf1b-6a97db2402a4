package gateways

import (
	"database/sql"
	"encoding/json"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type RoomLayoutModel struct {
	ID        uuid.UUID       `db:"id"`
	UpdatedAt time.Time       `db:"updated_at"`
	RawData   json.RawMessage `db:"raw_data"`
	Hash      []byte          `db:"xxhash"`
}

// RoomDesignModel corresponds to the 'room_designs' table.
type RoomDesignModel struct {
	ID          uuid.UUID
	ProjectID   string
	CreatedAt   time.Time
	UpdatedAt   time.Time
	Status      sql.NullString
	Style       sql.NullString
	ColorScheme sql.NullString
	Title       sql.NullString
	Description sql.NullString
}

// DefaultProductsModel corresponds to the 'default_products' table.
type DefaultProductsModel struct {
	RoomDesignID           uuid.UUID
	FloorTile              *uuid.UUID
	FloorTilePattern       *usecases.TilePattern
	Toilet                 *uuid.UUID
	Vanity                 *uuid.UUID
	Faucet                 *uuid.UUID
	Mirror                 *uuid.UUID
	Lighting               *uuid.UUID
	Paint                  *uuid.UUID
	Shelving               *uuid.UUID
	WallTilePlacement      usecases.WallTilePlacement
	WallTile               *uuid.UUID
	WallTilePattern        *usecases.TilePattern
	WallpaperPlacement     usecases.WallpaperPlacement
	Wallpaper              *uuid.UUID
	ShowerSystem           *uuid.UUID
	ShowerFloorTile        *uuid.UUID
	ShowerFloorTilePattern *usecases.TilePattern
	ShowerWallTile         *uuid.UUID
	ShowerWallTilePattern  *usecases.TilePattern
	ShowerShortWallTile    *uuid.UUID
	ShowerGlass            *uuid.UUID
	NicheTile              *uuid.UUID
	Tub                    *uuid.UUID
	TubFiller              *uuid.UUID
	TubDoor                *uuid.UUID
}

// RenderPrefsModel corresponds to the 'render_prefs' table.
type RenderPrefsModel struct {
	RoomDesignID       uuid.UUID
	ShowerGlassVisible bool
	TubDoorVisible     bool
	NichesVisible      bool
}

type RetailInfoModel struct {
	RoomDesignID    uuid.UUID
	TotalPriceCents sql.NullInt32
	LeadTimeDays    sql.NullInt32
	SkuCount        sql.NullInt32
}

// TemplateModel corresponds to the 'templates' table.
type TemplateModel struct {
	ID        uuid.UUID
	CreatedAt time.Time
	UpdatedAt time.Time

	ColorScheme usecases.ColorScheme
	Style       usecases.Style
	Name        string

	Description          sql.NullString
	Inspiration          sql.NullString
	Atmosphere           []string
	ColorPalette         []string
	MaterialPalette      []string
	HighlightedBrandUrls []string
}

// RenditionModel corresponds to the 'renditions' table.
type RenditionModel struct {
	ID        uuid.UUID
	CreatedAt time.Time
	UpdatedAt time.Time
	Status    string
	URL       sql.NullString
}

// TemplateProductSelectionsModel corresponds to the 'template_product_selections' table.
type TemplateProductSelectionsModel struct {
	TemplateID         uuid.UUID
	FloorTile          uuid.UUID
	Lighting           uuid.UUID
	Mirror             uuid.UUID
	Paint              uuid.UUID
	Shelving           uuid.UUID
	Toilet             uuid.UUID
	ShowerFloorTile    uuid.UUID
	ShowerWallTile     uuid.UUID
	TubFiller          uuid.UUID
	WallTilePlacement  string
	WallTile           uuid.UUID
	WallpaperPlacement string
	Wallpaper          *uuid.UUID
}

// TemplateOptionsModel corresponds to the 'template_options' table.
type TemplateOptionsModel struct {
	TemplateID         uuid.UUID
	AlcoveTub          uuid.UUID
	FreestandingTub    uuid.UUID
	ShowerGlassFixed   uuid.UUID
	ShowerGlassSliding uuid.UUID
	ShowerSystemFull   uuid.UUID
	ShowerSystemShower uuid.UUID
	TubDoorFixed       uuid.UUID
	TubDoorSliding     uuid.UUID
}

// VanityScalingOptionsModel corresponds to the 'vanity_scaling_options' table.
type VanityScalingOptionsModel struct {
	TemplateID            uuid.UUID
	MinVanityLengthInches int
	VanityProductID       uuid.UUID
	FaucetProductID       uuid.UUID
}

// TemplateProvenanceModel corresponds to the 'template_provenance' table.
type TemplateProvenanceModel struct {
	TemplateID    uuid.UUID
	LightingBrand string
	PlumbingBrand string
	ToiletBrand   string
	VanityBrand   string
	VanityStorage string
}
