package gateways

import (
	"context"

	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
)

func NewFakeLLM() *OpenAI {
	return &OpenAI{chatCompleter: NewFakeChatCompletionSvc()}
}

type FakeChatCompletionSvc struct{}

func NewFakeChatCompletionSvc() *FakeChatCompletionSvc {
	return &FakeChatCompletionSvc{}
}

func (f *FakeChatCompletionSvc) New(ctx context.Context, body openai.ChatCompletionNewParams,
	opts ...option.RequestOption) (res *openai.ChatCompletion, err error) {
	result := openai.ChatCompletion{
		Choices: []openai.ChatCompletionChoice{
			{
				Message: openai.ChatCompletionMessage{
					Content: `{"title": "Fake Title", "description": "Fake Description"}`,
				},
			},
		},
	}
	return &result, nil
}
