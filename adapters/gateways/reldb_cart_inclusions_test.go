package gateways_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestCartInclusionsRepository_SaveAndGet(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()

	// Create a test design first
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Create test cart inclusions
	productID1 := uuid.New()
	productID2 := uuid.New()

	inclusion1 := usecases.CartInclusion{
		ProductID:    productID1,
		Location:     usecases.LocationFloor,
		Include:      true,
		QuantityDiff: 2,
	}

	inclusion2 := usecases.CartInclusion{
		ProductID:    productID2,
		Location:     usecases.LocationWall,
		Include:      false,
		QuantityDiff: -1,
	}

	// Save cart inclusions
	err = repo.UpsertCartInclusion(ctx, designId, inclusion1)
	require.NoError(t, err)

	err = repo.UpsertCartInclusion(ctx, designId, inclusion2)
	require.NoError(t, err)

	// Retrieve cart inclusions
	inclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, inclusions, 2)

	// Verify inclusions (now a map with composite keys)
	key1 := usecases.CartInclusionKey{ProductID: productID1, Location: usecases.LocationFloor}
	result1, exists := inclusions[key1]
	require.True(t, exists, "Expected inclusion for productID1")
	assert.Equal(t, productID1, result1.ProductID)
	assert.Equal(t, usecases.LocationFloor, result1.Location)
	assert.True(t, result1.Include)
	assert.Equal(t, 2, result1.QuantityDiff)

	key2 := usecases.CartInclusionKey{ProductID: productID2, Location: usecases.LocationWall}
	result2, exists := inclusions[key2]
	require.True(t, exists, "Expected inclusion for productID2")
	assert.Equal(t, productID2, result2.ProductID)
	assert.Equal(t, usecases.LocationWall, result2.Location)
	assert.False(t, result2.Include)
	assert.Equal(t, -1, result2.QuantityDiff)
}

func TestCartInclusionsRepository_UpdateExisting(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()

	// Create a test design
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productID := uuid.New()

	// Save initial inclusion
	initial := usecases.CartInclusion{
		ProductID:    productID,
		Location:     usecases.LocationFloor,
		Include:      true,
		QuantityDiff: 1,
	}
	err = repo.UpsertCartInclusion(ctx, designId, initial)
	require.NoError(t, err)

	// Update the inclusion
	updated := usecases.CartInclusion{
		ProductID:    productID,
		Location:     usecases.LocationFloor,
		Include:      false,
		QuantityDiff: 3,
	}
	err = repo.UpsertCartInclusion(ctx, designId, updated)
	require.NoError(t, err)

	// Verify update
	inclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, inclusions, 1)

	key := usecases.CartInclusionKey{ProductID: productID, Location: usecases.LocationFloor}
	result, exists := inclusions[key]
	require.True(t, exists, "Expected inclusion for productID")
	assert.Equal(t, productID, result.ProductID)
	assert.Equal(t, usecases.LocationFloor, result.Location)
	assert.False(t, result.Include)
	assert.Equal(t, 3, result.QuantityDiff)
}

func TestCartInclusionsRepository_DeleteSpecific(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()

	// Create a test design
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productID1 := uuid.New()
	productID2 := uuid.New()

	// Save two inclusions
	result1 := usecases.CartInclusion{ProductID: productID1, Location: usecases.LocationFloor, Include: true, QuantityDiff: 1}
	result2 := usecases.CartInclusion{ProductID: productID2, Location: usecases.LocationWall, Include: false, QuantityDiff: -1}

	err = repo.UpsertCartInclusion(ctx, designId, result1)
	require.NoError(t, err)
	err = repo.UpsertCartInclusion(ctx, designId, result2)
	require.NoError(t, err)

	// Delete one inclusion
	err = repo.DeleteCartInclusion(ctx, designId, productID1, usecases.LocationFloor)
	require.NoError(t, err)

	// Verify only one remains
	inclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, inclusions, 1)

	key2 := usecases.CartInclusionKey{ProductID: productID2, Location: usecases.LocationWall}
	adj, exists := inclusions[key2]
	require.True(t, exists, "Expected inclusion for productID2")
	assert.Equal(t, productID2, adj.ProductID)
	assert.Equal(t, usecases.LocationWall, adj.Location)
}

func TestCartInclusionsRepository_DeleteAll(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()

	// Create a test design
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Save multiple inclusions
	for i := 0; i < 3; i++ {
		adj := usecases.CartInclusion{
			ProductID:    uuid.New(),
			Include:      i%2 == 0,
			QuantityDiff: i,
		}
		err = repo.UpsertCartInclusion(ctx, designId, adj)
		require.NoError(t, err)
	}

	// Verify inclusions exist
	inclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, inclusions, 3)

	// Delete all inclusions
	err = repo.DeleteCartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)

	// Verify all are gone
	inclusions, err = repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	assert.Len(t, inclusions, 0)
}

func TestCartInclusionsRepository_UpsertMultiple(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()

	// Create a test design first
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Create multiple cart inclusions
	productID1 := uuid.New()
	productID2 := uuid.New()
	productID3 := uuid.New()

	key1 := usecases.CartInclusionKey{ProductID: productID1, Location: usecases.LocationFloor}
	key2 := usecases.CartInclusionKey{ProductID: productID2, Location: usecases.LocationWall}
	key3 := usecases.CartInclusionKey{ProductID: productID3, Location: usecases.LocationShowerFloor}

	inclusions := usecases.CartInclusions{
		key1: {
			ProductID:    productID1,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 2,
		},
		key2: {
			ProductID:    productID2,
			Location:     usecases.LocationWall,
			Include:      false,
			QuantityDiff: -1,
		},
		key3: {
			ProductID:    productID3,
			Location:     usecases.LocationShowerFloor,
			Include:      true,
			QuantityDiff: 5,
		},
	}

	// Bulk upsert
	err = repo.UpsertCartInclusionsForDesign(ctx, designId, inclusions)
	require.NoError(t, err)

	// Verify all inclusions were saved
	retrievedInclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, retrievedInclusions, 3)

	// Verify each inclusion
	for key, expectedInclusion := range inclusions {
		actualInclusion, exists := retrievedInclusions[key]
		require.True(t, exists, "Expected inclusion for product %s at location %s", key.ProductID, key.Location)
		assert.Equal(t, expectedInclusion.ProductID, actualInclusion.ProductID)
		assert.Equal(t, expectedInclusion.Location, actualInclusion.Location)
		assert.Equal(t, expectedInclusion.Include, actualInclusion.Include)
		assert.Equal(t, expectedInclusion.QuantityDiff, actualInclusion.QuantityDiff)
	}
}

func TestCartInclusionsRepository_UpsertMultiple_Empty(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()

	// Create a test design first
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Upsert empty map
	err = repo.UpsertCartInclusionsForDesign(ctx, designId, usecases.CartInclusions{})
	require.NoError(t, err)

	// Verify no inclusions were created
	inclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	assert.Len(t, inclusions, 0)
}

func TestCartInclusionsRepository_GetEmptyDesign(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()

	// Create a test design with no inclusions
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Get inclusions for empty design
	inclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	assert.Len(t, inclusions, 0)
}

func TestCartInclusionsRepository_DeleteNonExistent(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()

	// Create a test design
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Try to delete non-existent inclusion
	err = repo.DeleteCartInclusion(ctx, designId, uuid.New(), usecases.LocationFloor)
	assert.Error(t, err)
	assert.Equal(t, usecases.ErrNotFound, err)
}
