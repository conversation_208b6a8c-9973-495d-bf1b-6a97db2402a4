package gateways

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type Monolith struct {
	hostname string
}

func NewMonolith(hostname string) *Monolith {
	return &Monolith{hostname: hostname}
}

func (m *Monolith) UpdateCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID) error {
	payload := map[string]any{
		"products": map[string]any{
			"currentDesignId": designId.String(),
		},
	}
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal request payload: %w", err)
	}
	url := fmt.Sprintf("https://%s/v2/projects/%s", m.hostname, projectId.String())
	req, err := http.NewRequestWithContext(ctx, "PUT", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to execute request: %w", err)
	}
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("monolith refused to update current design Id for project %s to %s: %s",
			projectId.String(), designId.String(), resp.Status)
	}
	return resp.Body.Close()
}

func (m *Monolith) UpdateScanAndMeasurementsForProject(ctx context.Context,
	projectId entities.ProjectId, scan json.RawMessage, measurements json.RawMessage) error {

	payload := map[string]any{
		"scan":         scan,
		"measurements": measurements,
	}
	jsonData, err := json.Marshal(payload)
	if err != nil {
		fmt.Printf("Failed to marshal request payload containing raw bytes for scan & measurements: %v", err)
		return usecases.ErrInvalidPayload
	}
	url := fmt.Sprintf("https://%s/v2/projects/%s", m.hostname, projectId.String())
	req, err := http.NewRequestWithContext(ctx, "PUT", url, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("Failed to create request to update scan and measurements: %v", err)
		return fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		fmt.Printf("Failed to execute request to update scan and measurements: %v", err)
		return fmt.Errorf("failed to execute request: %w", err)
	}
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			fmt.Printf("Could not read response body for PUT request to %s that returns status %s: %v",
				url, resp.Status, err)
			return fmt.Errorf("failed to read response body: %w", err)
		} else {
			fmt.Printf("Unable to update scan and measurements for project %s via %s: %v\n",
				projectId.String(), url, string(body))
			return fmt.Errorf("monolith refused to update scan and measurements for project %s: %s",
				projectId.String(), string(body))
		}
	}
	return resp.Body.Close()
}

type project struct {
	Data []struct {
		Scan         json.RawMessage `json:"scan"`
		Measurements json.RawMessage `json:"measurements"`
	}
}

// GetLayoutForProject fetches the room layout/scan data for a specific project
func (m *Monolith) GetLayoutForProject(ctx context.Context, projectId entities.ProjectId) (entities.RoomLayout, error) {
	roomLayout := entities.RoomLayout{}
	url := fmt.Sprintf("https://%s/v2/projects/%s", m.hostname, projectId.String())
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return roomLayout, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return roomLayout, fmt.Errorf("failed to execute request: %w", err)
	}
	defer closeBody(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return roomLayout,
			fmt.Errorf("failed to read response body for GET request to %s: %w", url, err)
	}
	if resp.StatusCode != http.StatusOK {
		return roomLayout,
			fmt.Errorf("monolith refused to return data for project %s: %d (%s)",
				projectId.String(), resp.StatusCode, string(body))
	}

	var p project
	if err := json.Unmarshal(body, &p); err != nil {
		return roomLayout, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	if len(p.Data) == 0 {
		return roomLayout, fmt.Errorf("no data in response")
	}
	if p.Data[0].Scan == nil {
		return roomLayout, fmt.Errorf("no scan data in response")
	}

	var scan adapters.RoomLayout
	if err := json.Unmarshal(p.Data[0].Scan, &scan); err != nil {
		return roomLayout, fmt.Errorf("failed to unmarshal scan data: %w", err)
	}
	if p.Data[0].Measurements != nil {
		if err := json.Unmarshal(p.Data[0].Measurements, &scan.Measurements); err != nil {
			return roomLayout, fmt.Errorf("failed to unmarshal measurements data: %w", err)
		}
	}

	return scan.ToEntity(), nil
}
