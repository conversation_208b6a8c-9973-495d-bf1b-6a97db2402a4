package gateways

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log/slog"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

// Provides access to the Postgres DB used by the monolith.
// TODO: delete this file once all relevant data has been extracted from the monolith's DB.
type Postgres struct {
	dbpool *pgxpool.Pool
	logger *slog.Logger
}

func NewPostgres(dbpool *pgxpool.Pool, logger *slog.Logger) *Postgres {
	if dbpool == nil {
		panic("dbpool cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &Postgres{dbpool: dbpool, logger: logger}
}

func (p *Postgres) Close() {
	p.dbpool.Close()
}

func (p *Postgres) UpdateCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID) error {
	_, err := p.dbpool.Exec(ctx, `UPDATE projects
		SET products = jsonb_set(products::jsonb, '{currentDesignId}', to_jsonb($1::text))::json
		WHERE id = $2`, designId, projectId)
	if err != nil {
		return fmt.Errorf("updating current design for project %s failed: %w", projectId, err)
	}
	return nil
}

func (p *Postgres) GetCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId) (string, error) {
	const query = `SELECT products->>'currentDesignId' FROM projects WHERE id = $1`
	row := p.dbpool.QueryRow(ctx, query, projectId)
	var designID sql.NullString
	err := row.Scan(&designID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return "", fmt.Errorf("project with id '%s' not found", projectId)
		}
		return "", fmt.Errorf("scan failed for current design ID of project %s: %w", projectId, err)
	}
	return designID.String, nil
}
