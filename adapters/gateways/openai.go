package gateways

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/invopop/jsonschema"
	"github.com/openai/openai-go" // imported as openai
	"github.com/openai/openai-go/option"
	"github.com/openai/openai-go/responses"
)

const SYSTEM_INSTRUCTIONS_DESIGN = `You are a senior virtual interior design assistant with many years of experience. 
You are picking a set of products from within a catalog and will provide them a design style summary at the end
based on the overall style of the selected products.`
const PROMPT_PREFIX_DESIGN = `
Step 1 - Review the description for each product and create a summary of the style of the products, 
including plumbing finish material, vanity type, vanity mounting type, mood, colors, color palette and atmosphere.
Step 2 - Based on the style summary create a 1-3 word Design Style title describing their bathroom design and 
a 200-250 character Design Style description that references elements in the bathroom 
like colors present, finishes and element mounting types.

For context, you are an experienced bathroom designer. 
You are helping a novice homeowner select materials for their bathroom remodel. 
This homeowner does not have much design or construction knowledge so 
it's important to speak in language they understand and mention features the general public would care about. 
This content is going to go in a software application to help people visualize & buy materials for their remodeling project! 
You want all the content here to be as personalized as possible, so it gives them confidence in their ability to transact.
`

type chatCompletionSvc interface {
	New(ctx context.Context, body openai.ChatCompletionNewParams,
		opts ...option.RequestOption) (res *openai.ChatCompletion, err error)
}

type responder interface {
	New(ctx context.Context, params responses.ResponseNewParams,
		opts ...option.RequestOption) (*responses.Response, error)
}

type OpenAI struct {
	schema        *jsonschema.Schema
	chatCompleter chatCompletionSvc
	responder     responder
}

func NewOpenAI(client openai.Client) *OpenAI {
	return &OpenAI{
		schema:        GenerateSchema[TitleAndDescription](),
		chatCompleter: &client.Chat.Completions,
		responder:     &client.Responses,
	}
}

type TitleAndDescription struct {
	Title       string `json:"title"`
	Description string `json:"description"`
}

func GenerateSchema[T any]() *jsonschema.Schema {
	reflector := jsonschema.Reflector{
		AllowAdditionalProperties: false,
		DoNotReference:            true,
	}
	var v T
	schema := reflector.Reflect(v)
	return schema
}

func (o *OpenAI) GenerateDesignTitleAndDescription(ctx context.Context,
	productDescriptions map[string]string) (title string, description string, err error) {

	start := time.Now()

	schemaParam := openai.ResponseFormatJSONSchemaJSONSchemaParam{
		Name:        "title_and_description",
		Description: openai.String("The title & description of the design"),
		Schema:      o.schema,
		Strict:      openai.Bool(true),
	}
	chatCompletion, err := o.chatCompleter.New(ctx, openai.ChatCompletionNewParams{
		Messages: []openai.ChatCompletionMessageParamUnion{
			openai.SystemMessage(SYSTEM_INSTRUCTIONS_DESIGN),
			openai.UserMessage(buildDesignPrompt(PROMPT_PREFIX_DESIGN, productDescriptions)),
		},
		Model: openai.ChatModelO3Mini,
		ResponseFormat: openai.ChatCompletionNewParamsResponseFormatUnion{
			OfJSONSchema: &openai.ResponseFormatJSONSchemaParam{
				JSONSchema: schemaParam,
			},
		},
	})
	if err != nil {
		return
	}
	if len(chatCompletion.Choices) == 0 {
		err = fmt.Errorf("no choices returned by OpenAI")
		return
	}
	titleAndDescription := chatCompletion.Choices[0].Message.Content
	var result struct {
		Title       string `json:"title"`
		Description string `json:"description"`
	}
	if err = json.Unmarshal([]byte(titleAndDescription), &result); err != nil {
		log.Printf("Failed to unmarshal AI response for design : %v", err)
		err = fmt.Errorf("failed to unmarshal result: %w", err)
		return
	}

	duration := time.Since(start)
	log.Printf("Successfully generated title (%s) and description for design in %v", result.Title, duration)
	return result.Title, result.Description, nil
}

// buildDesignPrompt constructs the prompt for the design based on product descriptions.
func buildDesignPrompt(promptPrefix string, productDescriptions map[string]string) string {
	var descriptions []string
	for product, description := range productDescriptions {
		descriptions = append(descriptions, fmt.Sprintf("%s description: %s", product, description))
	}
	return fmt.Sprintf("%s\n%s", promptPrefix, strings.Join(descriptions, "\n"))
}
