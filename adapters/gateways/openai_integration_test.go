//go:build integration

// This file contains integration tests for the OpenAI gateway implementation using a fake LLM.
// These tests verify the actual functionality of the GenerateDesignTitleAndDescription method
// without making real API calls.
// Run with: go test -v --tags=integration -count=1 ./adapters/gateways
package gateways_test

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Test successful generation with a design containing products
func TestGenerateDesignTitleAndDescription_Success(t *testing.T) {
	design := createTestDesign()

	openAI := gateways.NewFakeLLM()

	ctx := t.Context()

	catalog := gateways.NewFakeCatalog()
	productDescriptions, err := catalog.ProductDescriptionsForDesign(ctx, design)
	title, description, err := openAI.GenerateDesignTitleAndDescription(ctx, productDescriptions)

	require.NoError(t, err)
	assert.Equal(t, "Fake Title", title)
	assert.Equal(t, "Fake Description", description)
	assert.NotEmpty(t, title)
	assert.NotEmpty(t, description)
}

// Test that the function works with a basic design and follows expected flow
func TestGenerateDesignTitleAndDescription_BasicFlow(t *testing.T) {
	design := createTestDesign()
	openAI := gateways.NewFakeLLM()

	ctx := t.Context()

	// Test that the function successfully processes a design with some products
	catalog := gateways.NewFakeCatalog()
	productDescriptions, err := catalog.ProductDescriptionsForDesign(ctx, design)
	title, description, err := openAI.GenerateDesignTitleAndDescription(ctx, productDescriptions)

	require.NoError(t, err)
	assert.NotEmpty(t, title)
	assert.NotEmpty(t, description)

	// Verify the fake LLM returns expected values
	assert.Equal(t, "Fake Title", title)
	assert.Equal(t, "Fake Description", description)
}

// Test with design containing all product types
func TestGenerateDesignTitleAndDescription_AllProducts(t *testing.T) {
	design := createTestDesignWithAllProducts()
	openAI := gateways.NewFakeLLM()

	ctx := t.Context()

	// This tests that all product types are handled correctly in buildProductDescriptions
	catalog := gateways.NewFakeCatalog()
	productDescriptions, err := catalog.ProductDescriptionsForDesign(ctx, design)
	title, description, err := openAI.GenerateDesignTitleAndDescription(ctx, productDescriptions)

	require.NoError(t, err)
	assert.Equal(t, "Fake Title", title)
	assert.Equal(t, "Fake Description", description)
	assert.NotEmpty(t, title)
	assert.NotEmpty(t, description)
}

// Test with design containing only some products
func TestGenerateDesignTitleAndDescription_PartialProducts(t *testing.T) {
	design := createTestDesign()
	// Remove some products to test partial coverage
	design.Mirror = nil
	design.Paint = nil

	openAI := gateways.NewFakeLLM()

	ctx := t.Context()

	// Should still work with partial product selection
	catalog := gateways.NewFakeCatalog()
	productDescriptions, err := catalog.ProductDescriptionsForDesign(ctx, design)
	title, description, err := openAI.GenerateDesignTitleAndDescription(ctx, productDescriptions)

	require.NoError(t, err)
	assert.Equal(t, "Fake Title", title)
	assert.Equal(t, "Fake Description", description)
	assert.NotEmpty(t, title)
	assert.NotEmpty(t, description)
}

// Test edge cases for UUID handling
func TestDesignWithEdgeCaseUUIDs(t *testing.T) {
	// Test with zero UUID
	zeroUUID := uuid.UUID{}

	design := usecases.Design{
		ID:                 uuid.New(),
		ProjectID:          entities.ProjectId("PRJ-ZERO-UUID"),
		Created:            time.Now(),
		LastUpdated:        time.Now(),
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.NoWallTile,
		DesignOptions: usecases.DesignOptions{
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: &zeroUUID,
			},
		},
	}

	openAI := gateways.NewFakeLLM()

	ctx := t.Context()

	// Should handle zero UUID gracefully in buildProductDescriptions
	catalog := gateways.NewFakeCatalog()
	productDescriptions, err := catalog.ProductDescriptionsForDesign(ctx, design)
	title, description, err := openAI.GenerateDesignTitleAndDescription(ctx, productDescriptions)

	require.NoError(t, err)
	assert.Equal(t, "Fake Title", title)
	assert.Equal(t, "Fake Description", description)
	assert.NotEmpty(t, title)
	assert.NotEmpty(t, description)
}
