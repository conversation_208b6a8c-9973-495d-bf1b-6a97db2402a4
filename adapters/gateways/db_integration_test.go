//go:build integration

package gateways_test

import (
	"context"
	"database/sql"
	"log"
	"net/url"
	"os"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/db"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const TEST_DATABASE_URL = "postgres://postgres@localhost:5432/room-design-test"

var testPool *pgxpool.Pool // Global pool for all tests in this package

// TestMain is the entry point for tests in this package. It sets up the
// database connection pool and closes it after all tests have run.
func TestMain(m *testing.M) {
	databaseUrl := os.Getenv("TEST_DATABASE_URL")
	if databaseUrl == "" {
		log.Println("TEST_DATABASE_URL env var not set; using default value: ", TEST_DATABASE_URL)
		databaseUrl = TEST_DATABASE_URL
	}

	// Create a test-specific pool configuration with smaller limits
	testPoolConfig := &db.PoolConfig{
		MaxConns:          10,               // Smaller pool for tests
		MinConns:          2,                // Minimum connections for tests
		MaxConnLifetime:   30 * time.Minute, // Shorter lifetime for tests
		MaxConnIdleTime:   15 * time.Minute, // Shorter idle time for tests
		HealthCheckPeriod: 2 * time.Minute,  // Less frequent health checks for tests
	}

	// Parse the connection string and create pool config
	config, err := pgxpool.ParseConfig(databaseUrl)
	if err != nil {
		log.Fatalf("Failed to parse test database URL: %v", err)
	}

	// Apply test pool configuration
	config.MaxConns = testPoolConfig.MaxConns
	config.MinConns = testPoolConfig.MinConns
	config.MaxConnLifetime = testPoolConfig.MaxConnLifetime
	config.MaxConnIdleTime = testPoolConfig.MaxConnIdleTime
	config.HealthCheckPeriod = testPoolConfig.HealthCheckPeriod

	// Create the test pool with configuration
	testPool, err = pgxpool.NewWithConfig(context.Background(), config)
	if err != nil {
		log.Fatalf("Failed to connect to test database: %v", err)
	}
	defer testPool.Close()

	log.Printf("Test database pool created with max_conns=%d, min_conns=%d",
		testPoolConfig.MaxConns, testPoolConfig.MinConns)

	// Run all tests in the package
	exitCode := m.Run()
	os.Exit(exitCode)
}

// TestPoolConfiguration verifies that the connection pool is configured correctly
func TestPoolConfiguration(t *testing.T) {
	require.NotNil(t, testPool, "Test pool should be initialized")

	stats := testPool.Stat()

	// Verify pool configuration is applied
	assert.Equal(t, int32(10), stats.MaxConns(), "Max connections should be 10")
	assert.LessOrEqual(t, stats.TotalConns(), stats.MaxConns(), "Total connections should not exceed max")
	assert.GreaterOrEqual(t, stats.IdleConns(), int32(0), "Idle connections should be non-negative")

	// Test that we can acquire a connection
	conn, err := testPool.Acquire(context.Background())
	require.NoError(t, err, "Should be able to acquire a connection")
	defer conn.Release()

	// Verify connection is working
	var result int
	err = conn.QueryRow(context.Background(), "SELECT 1").Scan(&result)
	require.NoError(t, err, "Should be able to execute a simple query")
	assert.Equal(t, 1, result, "Query should return 1")
}

// TestRelationalDb_Integration_Designs_CRUD performs a full create, read, update, and delete
// cycle against a live database to ensure the entire lifecycle works as expected.
func TestRelationalDb_Integration_Designs_CRUD(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	// CASCADE is crucial to also truncate tables with foreign keys.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	testDesign := usecases.Design{
		ProjectID:          "PRJ-TEST123",
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Integration Test", Valid: true},
			Description:      sql.NullString{Valid: false},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Modern),
			FloorTilePattern: ptr(usecases.Herringbone),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile:      ptr(uuid.New()),
				Lighting:       ptr(uuid.New()),
				Mirror:         ptr(uuid.New()),
				Paint:          ptr(uuid.New()),
				ShowerWallTile: &uuid.Nil,
				Toilet:         ptr(uuid.New()),
			},
			Faucet:            ptr(uuid.New()),
			ShowerGlass:       &uuid.Nil,
			TubDoor:           &uuid.Nil,
			Vanity:            ptr(uuid.New()),
			NumSKUs:           sql.NullInt32{Int32: 10, Valid: true},
			TotalPriceInCents: sql.NullInt32{Int32: 1000, Valid: true},
			LeadTimeDays:      sql.NullInt32{Int32: 30, Valid: true},
		},
	}
	*testDesign.ShowerWallTile = uuid.New()
	*testDesign.ShowerGlass = uuid.New()
	*testDesign.TubDoor = uuid.New()

	// --- 1. Test Create ---
	createdID, err := db.UpsertDesign(ctx, testDesign)
	require.NoError(t, err, "Create failed")
	require.NotEqual(t, uuid.Nil, createdID, "Create should return a valid UUID")

	// --- 2. Test Read ---
	readDesign, err := db.ReadDesign(ctx, createdID)
	require.NoError(t, err, "Read failed after create")
	require.NotNil(t, readDesign)
	require.NotNil(t, readDesign.Created)
	require.NotEmpty(t, readDesign.ID)

	assert.Equal(t, createdID, readDesign.ID)
	assert.Equal(t, testDesign.ProjectID, readDesign.ProjectID)
	assert.Equal(t, testDesign.Title.String, readDesign.Title.String)
	assert.Equal(t, usecases.Herringbone, *readDesign.FloorTilePattern)
	assert.Equal(t, usecases.Modern, *readDesign.Style)
	assert.Equal(t, usecases.Neutral, *readDesign.ColorScheme)
	assert.Equal(t, usecases.Preview, readDesign.Status)
	assert.Equal(t, readDesign.Created, readDesign.LastUpdated)
	assert.Equal(t, testDesign.NumSKUs.Int32, readDesign.NumSKUs.Int32)
	assert.Equal(t, testDesign.TotalPriceInCents.Int32, readDesign.TotalPriceInCents.Int32)
	assert.Equal(t, testDesign.LeadTimeDays.Int32, readDesign.LeadTimeDays.Int32)

	// --- 3. Test Update ---
	readDesign.Title = sql.NullString{String: "Updated Title", Valid: true}
	readDesign.FloorTilePattern = ptr(usecases.VerticalStacked)
	readDesign.Style = ptr(usecases.Traditional)
	readDesign.ColorScheme = ptr(usecases.ColorScheme(""))
	readDesign.Status = usecases.Fave
	readDesign.TubDoorVisible = true
	readDesign.NichesVisible = true

	_, err = db.UpsertDesign(ctx, readDesign)
	require.NoError(t, err, "Update failed")

	// --- 4. Read again to verify update ---
	updatedDesign, err := db.ReadDesign(ctx, createdID)
	require.NoError(t, err, "Read failed after update")
	require.NotNil(t, updatedDesign)

	assert.Equal(t, "Updated Title", updatedDesign.Title.String)
	assert.Equal(t, usecases.VerticalStacked, *updatedDesign.FloorTilePattern)
	assert.Equal(t, usecases.Traditional, *updatedDesign.Style)
	assert.Equal(t, *readDesign.ColorScheme, *updatedDesign.ColorScheme)
	assert.Equal(t, usecases.Fave, updatedDesign.Status)
	assert.True(t, updatedDesign.TubDoorVisible)
	assert.True(t, updatedDesign.NichesVisible)
	assert.Greater(t, updatedDesign.LastUpdated, updatedDesign.Created)
	assert.Greater(t, updatedDesign.LastUpdated, readDesign.LastUpdated)

	// --- 5. Test DesignsForProject ---
	// Create another design for the same project
	_, err = db.UpsertDesign(ctx, usecases.Design{
		ProjectID:          "PRJ-TEST123",
		Status:             usecases.Fave,
		WallpaperPlacement: usecases.AllWalls,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			FloorTilePattern: ptr(usecases.ThirdOffset),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
				Mirror:    ptr(uuid.New()),
				Lighting:  ptr(uuid.New()),
				Paint:     ptr(uuid.New()),
			},
			Vanity: ptr(uuid.New()),
			Faucet: ptr(uuid.New()),
		},
	})
	require.NoError(t, err, "Create failed for second design")

	// Fetch both designs for the original project
	designs, err := db.DesignsForProject(ctx, "PRJ-TEST123")
	require.NoError(t, err, "Fetch designs failed")
	assert.Len(t, designs, 2, "Expected 2 designs for the project")

	// --- 6. Test DesignsByProject ---
	// Create another designs for a different project
	_, err = db.UpsertDesign(ctx, usecases.Design{
		ProjectID:          "PRJ-TEST456",
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.FullWall,
		DesignOptions: usecases.DesignOptions{
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
				Mirror:    ptr(uuid.New()),
				Lighting:  ptr(uuid.New()),
				Paint:     ptr(uuid.New()),
			},
			FloorTilePattern: ptr(usecases.VerticalStacked),
			Vanity:           ptr(uuid.New()),
			Faucet:           ptr(uuid.New()),
		},
	})
	require.NoError(t, err, "Create failed for third design")

	// Fetch all designs for both projects
	projectDesigns, errors, err := db.DesignsByProject(ctx, []entities.ProjectId{"PRJ-TEST123", "PRJ-TEST456"})
	require.NoError(t, err, "Fetch designs by project failed")
	assert.Len(t, errors, 0, "Expected no errors")
	assert.Len(t, projectDesigns, 2, "Expected designs for 2 projects")

	// --- 7. Test Delete ---
	err = db.DeleteDesign(ctx, createdID)
	require.NoError(t, err, "Delete failed")
}

// TestRelationalDb_Integration_InsertPreset tests the InsertPreset method
// against a live database to ensure it correctly creates all related records.
func TestRelationalDb_Integration_InsertPreset(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)
	legacyId := "42"

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE template.templates CASCADE;")
	require.NoError(t, err, "Failed to truncate templates table before test")
	_, err = testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate room_designs table before test")

	// First create a template that the preset can reference
	testTemplate := usecases.Template{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Name:            "Test Template for Preset",
		Description:     "A test template for preset testing",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "test.jpg"},
		Inspiration:     "Modern design",
		Atmosphere:      []string{"calm"},
		ColorPalette:    []string{"white"},
		MaterialPalette: []string{"marble"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "highlight1.jpg"},
			{Scheme: "https", Host: "example.com", Path: "highlight2.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.NoWallpaper,
	}

	templateID, err := db.InsertTemplate(ctx, testTemplate, legacyId)
	require.NoError(t, err, "Failed to create test template")

	testPreset := usecases.Preset{
		Id:         legacyId,
		TemplateId: templateID,
		Design: usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "PRJ-PRESET-TEST",
			Status:             usecases.Preview,
			WallpaperPlacement: usecases.NoWallpaper,
			WallTilePlacement:  usecases.HalfWall,
			DesignOptions: usecases.DesignOptions{
				Title:            sql.NullString{String: "Test Preset Design", Valid: true},
				Description:      sql.NullString{String: "A test preset design", Valid: true},
				ColorScheme:      ptr(usecases.Neutral),
				Style:            ptr(usecases.Modern),
				FloorTilePattern: ptr(usecases.Herringbone),
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: ptr(uuid.New()),
					Lighting:  ptr(uuid.New()),
					Mirror:    ptr(uuid.New()),
					Paint:     ptr(uuid.New()),
					Toilet:    ptr(uuid.New()),
				},
				Faucet:            ptr(uuid.New()),
				Vanity:            ptr(uuid.New()),
				NumSKUs:           sql.NullInt32{Int32: 10, Valid: true},
				TotalPriceInCents: sql.NullInt32{Int32: 1000, Valid: true},
				LeadTimeDays:      sql.NullInt32{Int32: 30, Valid: true},
			},
			ShowerGlassVisible: true,
			TubDoorVisible:     false,
			NichesVisible:      true,
		},
		Rendition: entities.Rendition{
			Id:     uuid.New(),
			Status: entities.RenditionPending,
			URL:    &url.URL{Scheme: "https", Host: "example.com", Path: "/image.webp"},
		},
	}

	// Act - Insert the preset
	err = db.InsertPreset(ctx, testPreset)
	if err != nil {
		log.Printf("Failed to insert preset with rendition image URL %v: %v",
			testPreset.Rendition.URL.String(), err)
	}
	require.NoError(t, err, "InsertPreset failed")

	// Assert - Read the preset back to verify it was created correctly
	readPreset, err := db.FindPresetByLegacyId(ctx, testPreset.Id)
	require.NoError(t, err, "ReadPreset failed after insert")
	require.NotNil(t, readPreset)

	// Verify preset fields
	assert.Equal(t, testPreset.Id, readPreset.Id)

	// Verify design fields
	assert.NotEqual(t, uuid.Nil, readPreset.Design.ID)
	testPreset.Design.Created = readPreset.Design.Created
	testPreset.Design.LastUpdated = readPreset.Design.LastUpdated
	assert.Equal(t, testPreset.Design, readPreset.Design)

	// Verify rendition fields
	assert.NotEqual(t, uuid.Nil, readPreset.Rendition.Id)
	testPreset.Rendition.CreatedAt = readPreset.Rendition.CreatedAt
	testPreset.Rendition.UpdatedAt = readPreset.Rendition.UpdatedAt
	assert.Equal(t, testPreset.Rendition, readPreset.Rendition)
}

// TestRelationalDb_Integration_InsertTemplate tests the InsertTemplate method
// against a live database to ensure it correctly creates all related records.
func TestRelationalDb_Integration_InsertTemplate(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE template.templates CASCADE;")
	require.NoError(t, err, "Failed to truncate templates table before test")

	legacyId := "00"
	testTemplate := usecases.Template{
		LegacyId: &legacyId,
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Name:            "Test Template",
		Description:     "A test template for integration testing",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/test.jpg"},
		Inspiration:     "Modern minimalist design",
		Atmosphere:      []string{"calm", "serene", "modern"},
		ColorPalette:    []string{"white", "gray", "black"},
		MaterialPalette: []string{"marble", "wood", "metal"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/highlight1.jpg"},
			{Scheme: "https", Host: "example.com", Path: "/highlight2.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
			Wallpaper:       ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		TemplateProvenance: usecases.TemplateProvenance{
			LightingBrand: ptr("Test Lighting Brand"),
			PlumbingBrand: ptr("Test Plumbing Brand"),
			ToiletBrand:   ptr("Test Toilet Brand"),
			VanityBrand:   ptr("Test Vanity Brand"),
			VanityStorage: ptr("Test Storage"),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.VanityWall,
		VanityScalingOptions: map[int]usecases.VanityScalingOption{
			36: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
			48: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		},
	}

	// Act - Insert the template
	templateID, err := db.InsertTemplate(ctx, testTemplate, "00")
	require.NoError(t, err, "InsertTemplate failed")
	require.NotEqual(t, uuid.Nil, templateID, "InsertTemplate should return a valid UUID")

	// Assert - Read the template back to verify it was created correctly
	readTemplate, err := db.ReadTemplate(ctx, templateID)
	require.NoError(t, err, "ReadTemplate failed after insert")

	// Verify template fields
	assert.Equal(t, templateID, readTemplate.ID)
	testTemplate.ID = readTemplate.ID
	assert.Equal(t, legacyId, *readTemplate.LegacyId)
	testTemplate.LegacyId = readTemplate.LegacyId
	assert.NotZero(t, readTemplate.UpdatedAt)
	testTemplate.UpdatedAt = readTemplate.UpdatedAt
	assert.Equal(t, testTemplate, readTemplate)

	// --- Additional Test: ReadAllTemplates consistency ---
	// Verify that ReadAllTemplates returns the same template data as ReadTemplate
	allTemplates, err := db.ReadAllTemplates(ctx)
	require.NoError(t, err, "ReadAllTemplates failed")
	require.NotEmpty(t, allTemplates, "ReadAllTemplates should return at least one template")

	// Find our template in the results
	var foundTemplate *usecases.Template
	for i := range allTemplates {
		if allTemplates[i].ID == templateID {
			foundTemplate = &allTemplates[i]
			break
		}
	}
	require.NotNil(t, foundTemplate, "Template should be found in ReadAllTemplates results")

	// Verify that the template from ReadAllTemplates matches the one from ReadTemplate
	assert.Equal(t, readTemplate, *foundTemplate)
}

// TestRelationalDb_Integration_TemplateVanityScalingOptions tests that vanity scaling options
// are correctly stored and retrieved from the database across different template operations.
func TestRelationalDb_Integration_TemplateVanityScalingOptions(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE template.templates CASCADE;")
	require.NoError(t, err, "Failed to truncate templates table before test")

	// Create test templates with vanity scaling options
	template1 := usecases.Template{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Name:            "Template 1",
		Description:     "Template with multiple vanity scaling options",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/template1.jpg"},
		Inspiration:     "Modern design with multiple vanity options",
		Atmosphere:      []string{"calm", "modern"},
		ColorPalette:    []string{"white", "gray"},
		MaterialPalette: []string{"marble", "wood"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/highlight1.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		TemplateProvenance: usecases.TemplateProvenance{
			LightingBrand: ptr("Test Lighting Brand 1"),
			PlumbingBrand: ptr("Test Plumbing Brand 1"),
			ToiletBrand:   ptr("Test Toilet Brand 1"),
			VanityBrand:   ptr("Test Vanity Brand 1"),
			VanityStorage: ptr("Test Storage 1"),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.NoWallpaper,
		VanityScalingOptions: map[int]usecases.VanityScalingOption{
			36: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
			48: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		},
	}

	template2 := usecases.Template{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Bold,
			Style:       usecases.Traditional,
		},
		Name:            "Template 2",
		Description:     "Template with single vanity scaling option",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/template2.jpg"},
		Inspiration:     "Traditional design with single vanity option",
		Atmosphere:      []string{"warm", "traditional"},
		ColorPalette:    []string{"beige", "brown"},
		MaterialPalette: []string{"wood", "ceramic"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/highlight2.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		TemplateProvenance: usecases.TemplateProvenance{
			LightingBrand: ptr("Test Lighting Brand 2"),
			PlumbingBrand: ptr("Test Plumbing Brand 2"),
			ToiletBrand:   ptr("Test Toilet Brand 2"),
			VanityBrand:   ptr("Test Vanity Brand 2"),
			VanityStorage: ptr("Test Storage 2"),
		},
		WallTilePlacement:  usecases.FullWall,
		WallpaperPlacement: usecases.VanityWall,
		VanityScalingOptions: map[int]usecases.VanityScalingOption{
			60: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		},
	}

	template3 := usecases.Template{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Transitional,
		},
		Name:            "Template 3",
		Description:     "Template with no vanity scaling options",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/template3.jpg"},
		Inspiration:     "Transitional design with no vanity options",
		Atmosphere:      []string{"balanced", "transitional"},
		ColorPalette:    []string{"cream", "taupe"},
		MaterialPalette: []string{"stone", "metal"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/highlight3.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		TemplateProvenance: usecases.TemplateProvenance{
			LightingBrand: ptr("Test Lighting Brand 3"),
			PlumbingBrand: ptr("Test Plumbing Brand 3"),
			ToiletBrand:   ptr("Test Toilet Brand 3"),
			VanityBrand:   ptr("Test Vanity Brand 3"),
			VanityStorage: ptr("Test Storage 3"),
		},
		WallTilePlacement:    usecases.HalfWall,
		WallpaperPlacement:   usecases.AllWalls,
		VanityScalingOptions: map[int]usecases.VanityScalingOption{}, // Empty
	}

	// Insert templates
	id1, err := db.InsertTemplate(ctx, template1, "")
	require.NoError(t, err, "Failed to insert template1")
	require.NotEqual(t, uuid.Nil, id1)

	id2, err := db.InsertTemplate(ctx, template2, "")
	require.NoError(t, err, "Failed to insert template2")
	require.NotEqual(t, uuid.Nil, id2)

	id3, err := db.InsertTemplate(ctx, template3, "")
	require.NoError(t, err, "Failed to insert template3")
	require.NotEqual(t, uuid.Nil, id3)

	// Test ReadAllTemplates - should populate vanity scaling options correctly
	allTemplates, err := db.ReadAllTemplates(ctx)
	require.NoError(t, err, "ReadAllTemplates failed")
	require.Len(t, allTemplates, 3, "Should return all 3 templates")

	// Verify vanity scaling options are preserved
	templateMap := make(map[uuid.UUID]usecases.Template)
	for _, tmpl := range allTemplates {
		templateMap[tmpl.ID] = tmpl
	}

	// Check template1 vanity options
	tmpl1 := templateMap[id1]
	require.Len(t, tmpl1.VanityScalingOptions, 2, "Template1 should have 2 vanity scaling options")
	require.Contains(t, tmpl1.VanityScalingOptions, 36, "Template1 should have 36-inch option")
	require.Contains(t, tmpl1.VanityScalingOptions, 48, "Template1 should have 48-inch option")
	require.Equal(t, template1.VanityScalingOptions[36], tmpl1.VanityScalingOptions[36], "36-inch option should match")
	require.Equal(t, template1.VanityScalingOptions[48], tmpl1.VanityScalingOptions[48], "48-inch option should match")

	// Check template2 vanity options
	tmpl2 := templateMap[id2]
	require.Len(t, tmpl2.VanityScalingOptions, 1, "Template2 should have 1 vanity scaling option")
	require.Contains(t, tmpl2.VanityScalingOptions, 60, "Template2 should have 60-inch option")
	require.Equal(t, template2.VanityScalingOptions[60], tmpl2.VanityScalingOptions[60], "60-inch option should match")

	// Check template3 vanity options (empty)
	tmpl3 := templateMap[id3]
	require.Len(t, tmpl3.VanityScalingOptions, 0, "Template3 should have no vanity scaling options")

	// Test TemplatesById - should also populate vanity scaling options correctly
	templateIds := []uuid.UUID{id1, id2}
	selectedTemplates, err := db.TemplatesById(ctx, templateIds)
	require.NoError(t, err, "TemplatesById failed")
	require.Len(t, selectedTemplates, 2, "Should return 2 selected templates")

	// Verify vanity scaling options are preserved in TemplatesById
	selectedMap := make(map[uuid.UUID]usecases.Template)
	for _, tmpl := range selectedTemplates {
		selectedMap[tmpl.ID] = tmpl
	}

	// Check template1 vanity options from TemplatesById
	tmpl1Selected := selectedMap[id1]
	require.Len(t, tmpl1Selected.VanityScalingOptions, 2, "Selected template1 should have 2 vanity scaling options")
	require.Equal(t, template1.VanityScalingOptions[36], tmpl1Selected.VanityScalingOptions[36], "Selected template1 36-inch option should match")
	require.Equal(t, template1.VanityScalingOptions[48], tmpl1Selected.VanityScalingOptions[48], "Selected template1 48-inch option should match")

	// Check template2 vanity options from TemplatesById
	tmpl2Selected := selectedMap[id2]
	require.Len(t, tmpl2Selected.VanityScalingOptions, 1, "Selected template2 should have 1 vanity scaling option")
	require.Equal(t, template2.VanityScalingOptions[60], tmpl2Selected.VanityScalingOptions[60], "Selected template2 60-inch option should match")

	// Test ReadTemplate individually to ensure consistency
	readTemplate1, err := db.ReadTemplate(ctx, id1)
	require.NoError(t, err, "ReadTemplate for template1 failed")
	require.Equal(t, tmpl1.VanityScalingOptions, readTemplate1.VanityScalingOptions, "ReadTemplate should return same vanity options as ReadAllTemplates")

	readTemplate2, err := db.ReadTemplate(ctx, id2)
	require.NoError(t, err, "ReadTemplate for template2 failed")
	require.Equal(t, tmpl2.VanityScalingOptions, readTemplate2.VanityScalingOptions, "ReadTemplate should return same vanity options as ReadAllTemplates")

	readTemplate3, err := db.ReadTemplate(ctx, id3)
	require.NoError(t, err, "ReadTemplate for template3 failed")
	require.Equal(t, tmpl3.VanityScalingOptions, readTemplate3.VanityScalingOptions, "ReadTemplate should return same vanity options as ReadAllTemplates")
}

// TestRelationalDb_Integration_Renditions_CRUD performs a full create, read, update, and delete
// cycle for renditions against a live database to ensure the entire lifecycle works as expected.
func TestRelationalDb_Integration_Renditions_CRUD(t *testing.T) {
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// First create a design that the rendition can reference
	testDesign := usecases.Design{
		ProjectID:          "PRJ-RENDITION-TEST",
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Rendition Test Design", Valid: true},
			Description:      sql.NullString{String: "A test design for rendition testing", Valid: true},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Modern),
			FloorTilePattern: ptr(usecases.Herringbone),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Lighting:  ptr(uuid.New()),
				Mirror:    ptr(uuid.New()),
				Paint:     ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
			Faucet: ptr(uuid.New()),
			Vanity: ptr(uuid.New()),
		},
	}

	designID, err := db.UpsertDesign(ctx, testDesign)
	require.NoError(t, err, "Failed to create test design")
	require.NotEqual(t, uuid.Nil, designID, "Design creation should return a valid UUID")

	testRendition := entities.Rendition{
		Id:     uuid.New(),
		Status: entities.RenditionPending,
	}

	// --- 1. Test Create ---
	createdID, err := db.InsertRendition(ctx, designID, testRendition)
	require.NoError(t, err, "Create rendition failed")
	require.NotEqual(t, uuid.Nil, createdID, "Create should return a valid UUID")
	assert.Equal(t, testRendition.Id, createdID, "Returned ID should match the provided ID")

	// --- 2. Test Read by ID ---
	readRenditions, err := db.Renditions(ctx, []uuid.UUID{createdID})
	require.NoError(t, err, "Read renditions by ID failed")
	require.Len(t, readRenditions, 1, "Should return exactly one rendition")

	readRendition := readRenditions[0]
	assert.Equal(t, createdID, readRendition.Id)
	assert.Equal(t, testRendition.Status, readRendition.Status)
	assert.NotZero(t, readRendition.CreatedAt)
	assert.NotZero(t, readRendition.UpdatedAt)
	assert.Equal(t, readRendition.CreatedAt, readRendition.UpdatedAt)
	assert.EqualValues(t, testDesign.Title, readRendition.Title)
	assert.EqualValues(t, testDesign.Description, readRendition.Description)

	// --- 3. Test Read by Design ID ---
	designRenditions, err := db.RenditionsForDesign(ctx, designID)
	require.NoError(t, err, "Read renditions by design ID failed")
	require.Len(t, designRenditions, 1, "Should return exactly one rendition for the design")
	assert.Equal(t, readRendition, designRenditions[0])

	// --- 4. Test Update ---
	newURL := url.URL{Scheme: "https", Host: "example.com", Path: "/updated-image.webp"}
	updateDiff := entities.RenditionDiff{
		Id:     createdID,
		Status: entities.RenditionCompleted,
		URL:    &newURL,
	}

	err = db.UpdateRendition(ctx, updateDiff)
	require.NoError(t, err, "Update rendition failed")

	// --- 5. Read again to verify update ---
	updatedRenditions, err := db.Renditions(ctx, []uuid.UUID{createdID})
	require.NoError(t, err, "Read renditions after update failed")
	require.Len(t, updatedRenditions, 1, "Should return exactly one rendition")

	updatedRendition := updatedRenditions[0]
	assert.Equal(t, createdID, updatedRendition.Id)
	assert.Equal(t, entities.RenditionCompleted, updatedRendition.Status)
	assert.Equal(t, newURL.String(), updatedRendition.URL.String())
	assert.Equal(t, readRendition.CreatedAt, updatedRendition.CreatedAt)
	assert.Greater(t, updatedRendition.UpdatedAt, updatedRendition.CreatedAt)

	// --- 6. Test Multiple Renditions for Same Design ---
	secondRendition := entities.Rendition{
		Id:     uuid.New(),
		Status: entities.RenditionStarted,
		URL:    &url.URL{Scheme: "https", Host: "example.com", Path: "/second-image.webp"},
	}

	secondID, err := db.InsertRendition(ctx, designID, secondRendition)
	require.NoError(t, err, "Create second rendition failed")

	// Verify both renditions exist for the design
	allDesignRenditions, err := db.RenditionsForDesign(ctx, designID)
	require.NoError(t, err, "Read all renditions for design failed")
	assert.Len(t, allDesignRenditions, 2, "Should return two renditions for the design")

	// Verify we can read both by IDs
	bothRenditions, err := db.Renditions(ctx, []uuid.UUID{createdID, secondID})
	require.NoError(t, err, "Read multiple renditions by ID failed")
	assert.Len(t, bothRenditions, 2, "Should return both renditions")

	// --- 7. Test Delete ---
	err = db.DeleteRendition(ctx, createdID)
	require.NoError(t, err, "Delete rendition failed")

	// --- 8. Read again to verify delete ---
	deletedRenditions, err := db.Renditions(ctx, []uuid.UUID{createdID})
	require.NoError(t, err, "Read after delete should not error")
	assert.Len(t, deletedRenditions, 0, "Should return no renditions after delete")

	// Verify the second rendition still exists
	remainingRenditions, err := db.RenditionsForDesign(ctx, designID)
	require.NoError(t, err, "Read remaining renditions failed")
	assert.Len(t, remainingRenditions, 1, "Should have one remaining rendition")
	assert.Equal(t, secondID, remainingRenditions[0].Id)

	// --- 9. Test Delete Non-existent Rendition ---
	err = db.DeleteRendition(ctx, uuid.New())
	require.Error(t, err, "Delete non-existent rendition should fail")
	assert.Contains(t, err.Error(), "target not found in storage")
}

// TestRelationalDb_Integration_Renditions_ValidationAndEdgeCases tests validation rules
// and edge cases for rendition operations.
func TestRelationalDb_Integration_Renditions_ValidationAndEdgeCases(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	testDesign := usecases.Design{
		ProjectID:          "PRJ-VALIDATION-TEST",
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title: sql.NullString{String: "Validation Test Design", Valid: true},
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}
	designID, err := db.UpsertDesign(ctx, testDesign)
	require.NoError(t, err, "Failed to create test design")

	// --- Test 1: Completed rendition without URL should fail ---
	invalidRendition := entities.Rendition{
		Id:     uuid.New(),
		Status: entities.RenditionCompleted,
		URL:    &url.URL{}, // Empty URL
	}

	_, err = db.InsertRendition(ctx, designID, invalidRendition)
	require.Error(t, err, "Insert completed rendition without URL should fail")
	assert.Contains(t, err.Error(), "invalid payload in request")

	// --- Test 2: Pending rendition with valid URL should succeed ---
	validPendingRendition := entities.Rendition{
		Id:     uuid.New(),
		Status: entities.RenditionPending,
		URL:    &url.URL{Scheme: "https", Host: "example.com", Path: "/pending-image.webp"},
	}

	pendingID, err := db.InsertRendition(ctx, designID, validPendingRendition)
	require.NoError(t, err, "Insert pending rendition should succeed")
	require.NotEqual(t, uuid.Nil, pendingID)

	// --- Test 3: Update to completed status without URL should fail ---
	invalidUpdate := entities.RenditionDiff{
		Id:     pendingID,
		Status: entities.RenditionCompleted,
		URL:    nil, // No URL provided
	}

	err = db.UpdateRendition(ctx, invalidUpdate)
	require.Error(t, err, "Update to completed without URL should fail")
	assert.Contains(t, err.Error(), "invalid payload in request")

	// --- Test 3b: Update to empty status should fail ---
	invalidUpdate.Status = ""
	err = db.UpdateRendition(ctx, invalidUpdate)
	require.Error(t, err, "Update to with empty status should fail")
	assert.Contains(t, err.Error(), "invalid payload in request")

	// --- Test 4: Update non-existent rendition should fail ---
	nonExistentUpdate := entities.RenditionDiff{
		Id:     uuid.New(),
		Status: entities.RenditionArchived,
	}

	err = db.UpdateRendition(ctx, nonExistentUpdate)
	require.Error(t, err, "Update non-existent rendition should fail")
	assert.Contains(t, err.Error(), "target not found in storage")

	// --- Test 5: Insert rendition with nil UUID should generate new UUID ---
	renditionWithNilID := entities.Rendition{
		Id:     uuid.Nil,
		Status: entities.RenditionStarted,
		URL:    &url.URL{Scheme: "https", Host: "example.com", Path: "/generated-id.webp"},
	}

	generatedID, err := db.InsertRendition(ctx, designID, renditionWithNilID)
	require.NoError(t, err, "Insert with nil UUID should succeed")
	require.NotEqual(t, uuid.Nil, generatedID, "Should generate a new UUID")

	// --- Test 6: Read empty list of rendition IDs ---
	emptyRenditions, err := db.Renditions(ctx, []uuid.UUID{})
	require.NoError(t, err, "Read empty list should not error")
	assert.Len(t, emptyRenditions, 0, "Should return empty list")

	// --- Test 7: Read renditions for non-existent design ---
	nonExistentDesignRenditions, err := db.RenditionsForDesign(ctx, uuid.New())
	require.NoError(t, err, "Read renditions for non-existent design should not error")
	assert.Len(t, nonExistentDesignRenditions, 0, "Should return empty list")

	// --- Test 8: Test all rendition statuses ---
	statuses := []entities.RenditionStatus{
		entities.RenditionPending,
		entities.RenditionStarted,
		entities.RenditionCompleted,
		entities.RenditionOutdated,
		entities.RenditionArchived,
	}

	var statusRenditionIDs []uuid.UUID
	for _, status := range statuses {
		rendition := entities.Rendition{
			Id:     uuid.New(),
			Status: status,
			URL: &url.URL{
				Scheme: "https",
				Host:   "example.com",
				Path:   "/image-" + string(status) + ".webp",
			},
		}

		statusID, err := db.InsertRendition(ctx, designID, rendition)
		require.NoError(t, err, "Insert rendition with status %s should succeed", status)
		statusRenditionIDs = append(statusRenditionIDs, statusID)
	}

	// Verify all status renditions were created
	allStatusRenditions, err := db.Renditions(ctx, statusRenditionIDs)
	require.NoError(t, err, "Read all status renditions should succeed")
	assert.Len(t, allStatusRenditions, len(statuses), "Should return all status renditions")

	// Verify each status was preserved
	statusMap := make(map[uuid.UUID]entities.RenditionStatus)
	for _, rendition := range allStatusRenditions {
		statusMap[rendition.Id] = rendition.Status
	}

	for i, expectedStatus := range statuses {
		actualStatus, exists := statusMap[statusRenditionIDs[i]]
		require.True(t, exists, "Rendition with status %s should exist", expectedStatus)
		assert.Equal(t, expectedStatus, actualStatus, "Status should be preserved")
	}
}

// TestRelationalDb_Integration_DeleteDesignsForProjectExceptSpecified tests the
// DeleteDesignsForProjectExceptSpecified method against a live database to ensure
// it correctly deletes designs while preserving specified ones.
func TestRelationalDb_Integration_DeleteDesignsForProjectExceptSpecified(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	projectID := entities.NewProjectId("PRJ-DELETE-TEST")

	// Create multiple designs for the same project
	design1 := usecases.Design{
		ProjectID:          projectID,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Design 1", Valid: true},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Modern),
			FloorTilePattern: ptr(usecases.Herringbone),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	design2 := usecases.Design{
		ProjectID:          projectID,
		Status:             usecases.Fave,
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.FullWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Design 2", Valid: true},
			ColorScheme:      ptr(usecases.Bold),
			Style:            ptr(usecases.Traditional),
			FloorTilePattern: ptr(usecases.VerticalStacked),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	design3 := usecases.Design{
		ProjectID:          projectID,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.AllWalls,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Design 3", Valid: true},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Transitional),
			FloorTilePattern: ptr(usecases.ThirdOffset),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	// Create a design for a different project to ensure it's not affected
	designOtherProject := usecases.Design{
		ProjectID:          entities.NewProjectId("PRJ-OTHER"),
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Other Project Design", Valid: true},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Modern),
			FloorTilePattern: ptr(usecases.Herringbone),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	// Create all designs
	id1, err := db.UpsertDesign(ctx, design1)
	require.NoError(t, err, "Failed to create design 1")
	require.NotEqual(t, uuid.Nil, id1)

	id2, err := db.UpsertDesign(ctx, design2)
	require.NoError(t, err, "Failed to create design 2")
	require.NotEqual(t, uuid.Nil, id2)

	id3, err := db.UpsertDesign(ctx, design3)
	require.NoError(t, err, "Failed to create design 3")
	require.NotEqual(t, uuid.Nil, id3)

	idOther, err := db.UpsertDesign(ctx, designOtherProject)
	require.NoError(t, err, "Failed to create other project design")
	require.NotEqual(t, uuid.Nil, idOther)

	// Verify all designs were created
	allDesigns, err := db.DesignsForProject(ctx, projectID)
	require.NoError(t, err, "Failed to fetch designs for project")
	assert.Len(t, allDesigns, 3, "Should have 3 designs for the project")

	otherDesigns, err := db.DesignsForProject(ctx, entities.NewProjectId("PRJ-OTHER"))
	require.NoError(t, err, "Failed to fetch designs for other project")
	assert.Len(t, otherDesigns, 1, "Should have 1 design for the other project")

	// --- Test 1: Delete all except specified designs ---
	designsToKeep := []uuid.UUID{id1, id3} // Keep design 1 and 3, delete design 2

	err = db.DeleteDesignsForProjectExceptSpecified(ctx, projectID, designsToKeep)
	require.NoError(t, err, "DeleteDesignsForProjectExceptSpecified should succeed")

	// Verify only the specified designs remain
	remainingDesigns, err := db.DesignsForProject(ctx, projectID)
	require.NoError(t, err, "Failed to fetch remaining designs")
	assert.Len(t, remainingDesigns, 2, "Should have 2 remaining designs")

	remainingIDs := make([]uuid.UUID, len(remainingDesigns))
	for i, design := range remainingDesigns {
		remainingIDs[i] = design.ID
	}
	assert.Contains(t, remainingIDs, id1, "Design 1 should remain")
	assert.Contains(t, remainingIDs, id3, "Design 3 should remain")
	assert.NotContains(t, remainingIDs, id2, "Design 2 should be deleted")

	// Verify the other project's design is unaffected
	otherDesignsAfter, err := db.DesignsForProject(ctx, entities.NewProjectId("PRJ-OTHER"))
	require.NoError(t, err, "Failed to fetch designs for other project after deletion")
	assert.Len(t, otherDesignsAfter, 1, "Other project should still have 1 design")
	assert.Equal(t, idOther, otherDesignsAfter[0].ID, "Other project design should be unchanged")

	// --- Test 2: Delete all designs (empty keep list) ---
	err = db.DeleteDesignsForProjectExceptSpecified(ctx, projectID, []uuid.UUID{})
	require.NoError(t, err, "DeleteDesignsForProjectExceptSpecified with empty list should succeed")

	// Verify all designs are deleted
	finalDesigns, err := db.DesignsForProject(ctx, projectID)
	require.NoError(t, err, "Failed to fetch final designs")
	assert.Len(t, finalDesigns, 0, "Should have no remaining designs")

	// --- Test 3: Delete from non-existent project (should not error) ---
	err = db.DeleteDesignsForProjectExceptSpecified(ctx, entities.NewProjectId("PRJ-NONEXISTENT"), []uuid.UUID{uuid.New()})
	require.NoError(t, err, "DeleteDesignsForProjectExceptSpecified for non-existent project should not error")

	// --- Test 4: Keep non-existent design IDs (should not error) ---
	// Create one more design to test with
	finalDesign := usecases.Design{
		ProjectID:          projectID,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title: sql.NullString{String: "Final Design", Valid: true},
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	finalID, err := db.UpsertDesign(ctx, finalDesign)
	require.NoError(t, err, "Failed to create final design")

	// Try to keep both the real design and a non-existent one
	nonExistentID := uuid.New()
	err = db.DeleteDesignsForProjectExceptSpecified(ctx, projectID, []uuid.UUID{finalID, nonExistentID})
	require.NoError(t, err, "DeleteDesignsForProjectExceptSpecified with non-existent ID should not error")

	// Verify the real design remains
	finalCheck, err := db.DesignsForProject(ctx, projectID)
	require.NoError(t, err, "Failed to fetch designs for final check")
	assert.Len(t, finalCheck, 1, "Should have 1 remaining design")
	assert.Equal(t, finalID, finalCheck[0].ID, "Final design should remain")
}

// Helper function to get a pointer to a value
func ptr[T any](v T) *T {
	return &v
}
