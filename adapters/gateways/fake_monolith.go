package gateways

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

// FakeMonolith is a fake implementation of the Monolith for integration testing.
// It validates that JSON arguments sent to UpdateScanAndMeasurementsForProject are valid
// and captures all method calls for verification in tests.
//
// Usage example:
//
//	fake := gateways.NewFakeMonolith()
//	err := fake.UpdateScanAndMeasurementsForProject(ctx, projectId, scanJSON, measurementsJSON)
//	calls := fake.GetUpdateScanAndMeasurementsCalls()
//	assert.Len(t, calls, 1)
type FakeMonolith struct {
	mu                             sync.Mutex
	updateCurrentDesignCalls       []UpdateCurrentDesignCall
	updateScanAndMeasurementsCalls []UpdateScanAndMeasurementsCall
	shouldFailCurrentDesign        bool
	shouldFailScanAndMeasurements  bool
	currentDesignError             error
	scanAndMeasurementsError       error
}

type UpdateCurrentDesignCall struct {
	ProjectId entities.ProjectId
	DesignId  uuid.UUID
}

type UpdateScanAndMeasurementsCall struct {
	ProjectId    entities.ProjectId
	Scan         json.RawMessage
	Measurements json.RawMessage
}

func NewFakeMonolith() *FakeMonolith {
	return &FakeMonolith{
		updateCurrentDesignCalls:       make([]UpdateCurrentDesignCall, 0),
		updateScanAndMeasurementsCalls: make([]UpdateScanAndMeasurementsCall, 0),
	}
}

func (f *FakeMonolith) UpdateCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID) error {
	f.mu.Lock()
	defer f.mu.Unlock()

	f.updateCurrentDesignCalls = append(f.updateCurrentDesignCalls, UpdateCurrentDesignCall{
		ProjectId: projectId,
		DesignId:  designId,
	})

	if f.shouldFailCurrentDesign {
		return f.currentDesignError
	}
	return nil
}

func (f *FakeMonolith) UpdateScanAndMeasurementsForProject(ctx context.Context, projectId entities.ProjectId, scan json.RawMessage, measurements json.RawMessage) error {
	f.mu.Lock()
	defer f.mu.Unlock()

	// Validate that the JSON arguments are valid by attempting to unmarshal them
	var scanData interface{}
	if err := json.Unmarshal(scan, &scanData); err != nil {
		return fmt.Errorf("invalid scan JSON: %w", err)
	}

	var measurementsData interface{}
	if err := json.Unmarshal(measurements, &measurementsData); err != nil {
		return fmt.Errorf("invalid measurements JSON: %w", err)
	}

	f.updateScanAndMeasurementsCalls = append(f.updateScanAndMeasurementsCalls, UpdateScanAndMeasurementsCall{
		ProjectId:    projectId,
		Scan:         scan,
		Measurements: measurements,
	})

	if f.shouldFailScanAndMeasurements {
		return f.scanAndMeasurementsError
	}
	return nil
}

// GetLayoutForProject returns a fake room layout for testing
func (f *FakeMonolith) GetLayoutForProject(ctx context.Context, projectId entities.ProjectId) (entities.RoomLayout, error) {
	f.mu.Lock()
	defer f.mu.Unlock()

	return entities.RoomLayout{
		Id:        uuid.New(),
		Hash:      12345,
		RawData:   []byte(`{"fake": "layout"}`),
		FloorIds:  []uuid.UUID{uuid.New()},
		Walls:     []entities.Wall{},
		WetAreas:  []entities.WetArea{},
		ToiletIds: []uuid.UUID{},
		Vanities:  []entities.Vanity{},
	}, nil
}

// Helper methods for testing

func (f *FakeMonolith) GetUpdateCurrentDesignCalls() []UpdateCurrentDesignCall {
	f.mu.Lock()
	defer f.mu.Unlock()
	return append([]UpdateCurrentDesignCall(nil), f.updateCurrentDesignCalls...)
}

func (f *FakeMonolith) GetUpdateScanAndMeasurementsCalls() []UpdateScanAndMeasurementsCall {
	f.mu.Lock()
	defer f.mu.Unlock()
	return append([]UpdateScanAndMeasurementsCall(nil), f.updateScanAndMeasurementsCalls...)
}

func (f *FakeMonolith) SetShouldFailCurrentDesign(shouldFail bool, err error) {
	f.mu.Lock()
	defer f.mu.Unlock()
	f.shouldFailCurrentDesign = shouldFail
	f.currentDesignError = err
}

func (f *FakeMonolith) SetShouldFailScanAndMeasurements(shouldFail bool, err error) {
	f.mu.Lock()
	defer f.mu.Unlock()
	f.shouldFailScanAndMeasurements = shouldFail
	f.scanAndMeasurementsError = err
}

func (f *FakeMonolith) Reset() {
	f.mu.Lock()
	defer f.mu.Unlock()
	f.updateCurrentDesignCalls = make([]UpdateCurrentDesignCall, 0)
	f.updateScanAndMeasurementsCalls = make([]UpdateScanAndMeasurementsCall, 0)
	f.shouldFailCurrentDesign = false
	f.shouldFailScanAndMeasurements = false
	f.currentDesignError = nil
	f.scanAndMeasurementsError = nil
}
