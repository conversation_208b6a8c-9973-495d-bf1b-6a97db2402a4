package gateways

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type Catalog struct {
	hostname string
}

func NewCatalog(hostname string) *Catalog {
	if hostname == "" {
		panic("hostname cannot be empty")
	}
	return &Catalog{hostname: hostname}
}

func (c *Catalog) ProductInfo(
	ctx context.Context, category string, productId uuid.UUID,
) (usecases.RenderableProduct, error) {

	var result usecases.RenderableProduct
	if err := validateInputs(category, productId); err != nil {
		return result, err
	}

	url := fmt.Sprintf("https://%s/catalog/v2/products/renderable-products/%s/%s",
		c.hostname, category, productId.String())
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return result, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return result, fmt.Errorf("failed to execute request: %w", err)
	}
	defer closeBody(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return result, fmt.Errorf("failed to read response body: %w", err)
	}
	if resp.StatusCode != http.StatusOK {
		return result, fmt.Errorf("error response from Catalog: %d (%s)", resp.StatusCode, string(body))
	}

	var searchResponse struct {
		Data []adapters.ProductInfo `json:"data"`
	}
	if err := json.Unmarshal(body, &searchResponse); err != nil {
		log.Println(string(body))
		return result, fmt.Errorf("failed to unmarshal response from Catalog: %w", err)
	}

	if len(searchResponse.Data) == 0 {
		return result, fmt.Errorf("no products returned from Catalog for category '%s' and productId '%s'",
			category, productId.String())
	}
	convertedResult, err := searchResponse.Data[0].ToUsecase()
	if err != nil {
		return result, fmt.Errorf("failed to convert product info to usecase format: %w", err)
	}

	return convertedResult, nil
}

func (c *Catalog) ProductDescriptionsForDesign(ctx context.Context, design usecases.Design) (map[string]string, error) {
	idToProduct := design.CategoriesByProductUUID()
	var productIds []uuid.UUID

	if len(idToProduct) == 0 {
		log.Println("No product IDs found in design!")
		return make(map[string]string), usecases.ErrNotFound
	}
	for id := range idToProduct {
		productIds = append(productIds, id)
	}

	productDescriptions, err := c.ProductDescriptions(ctx, productIds)
	if err != nil {
		return nil, err
	}

	result := make(map[string]string)
	for id, description := range productDescriptions {
		category, ok := idToProduct[id]
		if !ok {
			log.Printf("Failed to find category for product ID %s", id.String())
			continue
		}
		result[category] = description
	}
	return result, nil
}

func (c *Catalog) ProductDescriptions(ctx context.Context, productUUIDs []uuid.UUID) (map[uuid.UUID]string, error) {
	results := make(map[uuid.UUID]string)
	if len(productUUIDs) == 0 {
		return results, nil
	}

	productIds := make([]string, 0, len(productUUIDs))
	for _, id := range productUUIDs {
		productIds = append(productIds, id.String())
	}
	url := fmt.Sprintf("https://%s/catalog/v2/products/renderable-products/%s?include[]=details",
		c.hostname, strings.Join(productIds, ";"))
	resp, err := http.Get(url)
	if err != nil {
		log.Printf("Failed to get product data from catalog via URL: %v", url)
		return nil, fmt.Errorf("failed to get product data from catalog: %w", err)
	}
	defer closeBody(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Failed to get product data from catalog via URL: %v", url)
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	if resp.StatusCode != http.StatusOK {
		log.Printf("Failed to get product data from Catalog via URL: %v", url)
		return nil, fmt.Errorf("error response from Catalog: %d (%s)", resp.StatusCode, string(body))
	}

	var productData struct {
		Data []struct {
			ID      uuid.UUID `json:"id"`
			Details struct {
				Description string `json:"description"`
			} `json:"details"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &productData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal product data: %w", err)
	}

	for _, product := range productData.Data {
		results[product.ID] = product.Details.Description
	}

	return results, nil
}

func validateInputs(category string, productId uuid.UUID) error {
	category = strings.TrimSpace(category)
	if category == "" {
		return fmt.Errorf("category cannot be empty or whitespace only")
	}
	if productId == uuid.Nil {
		return fmt.Errorf("productId cannot be nil/empty")
	}
	return nil
}

func closeBody(body io.ReadCloser) {
	if err := body.Close(); err != nil {
		log.Printf("failed to close response body: %s", err.Error())
	}
}
