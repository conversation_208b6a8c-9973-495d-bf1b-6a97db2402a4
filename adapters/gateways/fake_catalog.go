package gateways

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FakeCatalog is a fake implementation of the catalog interface for testing
type FakeCatalog struct {
	products map[uuid.UUID]usecases.RenderableProduct
	errors   map[string]map[uuid.UUID]error
}

func NewFakeCatalog() *FakeCatalog {
	return &FakeCatalog{
		products: make(map[uuid.UUID]usecases.RenderableProduct),
		errors:   make(map[string]map[uuid.UUID]error),
	}
}

func (f *FakeCatalog) ProductInfo(ctx context.Context, category string, productId uuid.UUID) (usecases.RenderableProduct, error) {
	if categoryErrors, exists := f.errors[category]; exists {
		if err, hasError := categoryErrors[productId]; hasError {
			return usecases.ProductInfo{}, err
		}
	}

	if product, found := f.products[productId]; found {
		return product, nil
	}

	return usecases.ProductInfo{}, usecases.ErrNotFound
}
func (f *FakeCatalog) ProductDescriptionsForDesign(ctx context.Context, design usecases.Design) (map[string]string, error) {
	idToProduct := design.CategoriesByProductUUID()
	if len(idToProduct) == 0 {
		return make(map[string]string), usecases.ErrNotFound
	}

	var productIds []uuid.UUID
	for id := range idToProduct {
		productIds = append(productIds, id)
	}

	productDescriptions, err := f.ProductDescriptions(ctx, productIds)
	if err != nil {
		return nil, err
	}

	result := make(map[string]string)
	for id, description := range productDescriptions {
		category, ok := idToProduct[id]
		if !ok {
			continue
		}
		result[category] = description
	}
	return result, nil
}
func (f *FakeCatalog) ProductDescriptions(ctx context.Context, productUUIDs []uuid.UUID) (map[uuid.UUID]string, error) {
	results := make(map[uuid.UUID]string)
	if len(productUUIDs) == 0 {
		return results, nil
	}

	for _, id := range productUUIDs {
		if product, exists := f.products[id]; exists {
			results[id] = product.GetDescription()
		}
	}

	return results, nil
}

func (f *FakeCatalog) AddProduct(category string, productId uuid.UUID, product usecases.RenderableProduct) {
	f.products[productId] = product
}

func (f *FakeCatalog) AddError(category string, productId uuid.UUID, err error) {
	if f.errors[category] == nil {
		f.errors[category] = make(map[uuid.UUID]error)
	}
	f.errors[category][productId] = err
}

// FakeProductSearch is a fake implementation of the productSearch interface for testing
type FakeProductSearch struct {
	results map[string][]uuid.UUID
	errors  map[string]error
}

func NewFakeProductSearch() *FakeProductSearch {
	return &FakeProductSearch{
		results: make(map[string][]uuid.UUID),
		errors:  make(map[string]error),
	}
}

func (f *FakeProductSearch) FindProductsViaAI(ctx context.Context, filters usecases.ProductSearchFilters) ([]uuid.UUID, error) {
	// Use category as key for AI search results, similar to regular search
	key := string(filters.Category)
	if err, exists := f.errors[key]; exists {
		return nil, err
	}
	if results, exists := f.results[key]; exists {
		return results, nil
	}
	return []uuid.UUID{}, nil
}

func (f *FakeProductSearch) FindProducts(ctx context.Context, urlQueryParams string) ([]uuid.UUID, error) {
	if err, hasError := f.errors[urlQueryParams]; hasError {
		return nil, err
	}

	if results, found := f.results[urlQueryParams]; found {
		return results, nil
	}

	return []uuid.UUID{}, nil
}

func (f *FakeProductSearch) AddResults(query string, results []uuid.UUID) {
	f.results[query] = results
}

func (f *FakeProductSearch) AddError(query string, err error) {
	f.errors[query] = err
}
