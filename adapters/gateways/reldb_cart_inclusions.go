package gateways

import (
	"context"
	"fmt"
	"log/slog"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// UpsertCartInclusion saves or updates a cart inclusion for a design.
// Uses UPSERT (INSERT ... ON CONFLICT) to handle both create and update cases.
func (r *RelationalDb) UpsertCartInclusion(ctx context.Context, designId uuid.UUID, inclusion usecases.CartInclusion) error {
	query := `
		INSERT INTO design.cart_inclusions (
			room_design_id,
			product_id,
			location,
			include,
			quantity_diff
		) VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (room_design_id, product_id, location)
		DO UPDATE SET
			include = EXCLUDED.include,
			quantity_diff = EXCLUDED.quantity_diff`

	_, err := r.db.Exec(ctx, query,
		designId,
		inclusion.ProductID,
		string(inclusion.Location),
		inclusion.Include,
		inclusion.QuantityDiff,
	)

	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to save cart inclusion",
			slog.String("error", err.Error()),
			slog.String("designId", designId.String()),
			slog.String("productId", inclusion.ProductID.String()),
			slog.String("location", string(inclusion.Location)))
		return fmt.Errorf("failed to save cart inclusion: %w", err)
	}

	r.logger.DebugContext(ctx, "Cart inclusion saved successfully",
		slog.String("designId", designId.String()),
		slog.String("productId", inclusion.ProductID.String()),
		slog.String("location", string(inclusion.Location)),
		slog.Bool("include", inclusion.Include),
		slog.Int("quantityDiff", inclusion.QuantityDiff))

	return nil
}

// UpsertCartInclusionsForDesign saves or updates multiple cart inclusions for a design in a single transaction.
func (r *RelationalDb) UpsertCartInclusionsForDesign(ctx context.Context, designId uuid.UUID, inclusions usecases.CartInclusions) error {
	if len(inclusions) == 0 {
		r.logger.DebugContext(ctx, "No cart inclusions to upsert", slog.String("designId", designId.String()))
		return nil
	}

	// Start a transaction for bulk operations
	tx, err := r.db.Begin(ctx)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to begin transaction for bulk cart inclusion upsert",
			slog.String("error", err.Error()),
			slog.String("designId", designId.String()))
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	query := `INSERT INTO design.cart_inclusions (room_design_id, product_id, location, include, quantity_diff)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (room_design_id, product_id, location)
		DO UPDATE SET
			include = EXCLUDED.include,
			quantity_diff = EXCLUDED.quantity_diff`

	for key, inclusion := range inclusions {
		_, err := tx.Exec(ctx, query, designId, inclusion.ProductID, string(inclusion.Location), inclusion.Include, inclusion.QuantityDiff)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to upsert cart inclusion in bulk operation",
				slog.String("error", err.Error()),
				slog.String("designId", designId.String()),
				slog.String("productId", key.ProductID.String()),
				slog.String("location", string(key.Location)))
			return fmt.Errorf("failed to upsert cart inclusion for product %s at location %s: %w", key.ProductID, key.Location, err)
		}
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.ErrorContext(ctx, "Failed to commit bulk cart inclusion transaction",
			slog.String("error", err.Error()),
			slog.String("designId", designId.String()))
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	r.logger.DebugContext(ctx, "Successfully upserted multiple cart inclusions",
		slog.String("designId", designId.String()),
		slog.Int("count", len(inclusions)))

	return nil
}

// ReplaceCartInclusionsForDesign completely replaces all cart inclusions for a design.
// This clears all existing inclusions and sets the new ones in a single transaction.
func (r *RelationalDb) ReplaceCartInclusionsForDesign(ctx context.Context, designId uuid.UUID, inclusions usecases.CartInclusions) error {
	// Start a transaction for atomic replacement
	tx, err := r.db.Begin(ctx)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to begin transaction for cart inclusion replacement",
			slog.String("error", err.Error()),
			slog.String("designId", designId.String()))
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// First, delete all existing cart inclusions for this design
	deleteQuery := `DELETE FROM design.cart_inclusions WHERE room_design_id = $1`
	_, err = tx.Exec(ctx, deleteQuery, designId)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to clear existing cart inclusions",
			slog.String("error", err.Error()),
			slog.String("designId", designId.String()))
		return fmt.Errorf("failed to clear existing cart inclusions: %w", err)
	}

	// If there are new inclusions to insert, insert them
	if len(inclusions) > 0 {
		insertQuery := `INSERT INTO design.cart_inclusions (room_design_id, product_id, location, include, quantity_diff)
			VALUES ($1, $2, $3, $4, $5)`

		for _, inclusion := range inclusions {
			_, err = tx.Exec(ctx, insertQuery,
				designId,
				inclusion.ProductID,
				string(inclusion.Location),
				inclusion.Include,
				inclusion.QuantityDiff,
			)
			if err != nil {
				r.logger.ErrorContext(ctx, "Failed to insert cart inclusion during replacement",
					slog.String("error", err.Error()),
					slog.String("designId", designId.String()),
					slog.String("productId", inclusion.ProductID.String()),
					slog.String("location", string(inclusion.Location)))
				return fmt.Errorf("failed to insert cart inclusion: %w", err)
			}
		}
	}

	// Commit the transaction
	if err = tx.Commit(ctx); err != nil {
		r.logger.ErrorContext(ctx, "Failed to commit cart inclusion replacement transaction",
			slog.String("error", err.Error()),
			slog.String("designId", designId.String()))
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	r.logger.DebugContext(ctx, "Cart inclusions replaced successfully",
		slog.String("designId", designId.String()),
		slog.Int("inclusionCount", len(inclusions)))

	return nil
}

// CartInclusionsForDesign retrieves all cart inclusions for a design as a map.
func (r *RelationalDb) CartInclusionsForDesign(ctx context.Context, designId uuid.UUID) (usecases.CartInclusions, error) {
	query := `
		SELECT
			product_id,
			location,
			include,
			quantity_diff
		FROM design.cart_inclusions
		WHERE room_design_id = $1
		ORDER BY product_id, location`

	rows, err := r.db.Query(ctx, query, designId)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query cart inclusions",
			slog.String("error", err.Error()),
			slog.String("designId", designId.String()))
		return nil, fmt.Errorf("failed to query cart inclusions: %w", err)
	}
	defer rows.Close()

	inclusions := make(usecases.CartInclusions)
	for rows.Next() {
		var inclusion usecases.CartInclusion
		var locationStr string
		err := rows.Scan(
			&inclusion.ProductID,
			&locationStr,
			&inclusion.Include,
			&inclusion.QuantityDiff,
		)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to scan cart inclusion row",
				slog.String("error", err.Error()),
				slog.String("designId", designId.String()))
			return nil, fmt.Errorf("failed to scan cart inclusion: %w", err)
		}
		inclusion.Location = usecases.Location(locationStr)
		key := usecases.CartInclusionKey{
			ProductID: inclusion.ProductID,
			Location:  inclusion.Location,
		}
		inclusions[key] = inclusion
	}

	if err = rows.Err(); err != nil {
		r.logger.ErrorContext(ctx, "Error during cart inclusions rows iteration",
			slog.String("error", err.Error()),
			slog.String("designId", designId.String()))
		return nil, fmt.Errorf("error during rows iteration: %w", err)
	}

	r.logger.DebugContext(ctx, "Retrieved cart inclusions",
		slog.String("designId", designId.String()),
		slog.Int("count", len(inclusions)))

	return inclusions, nil
}

// DeleteCartInclusion removes a specific cart inclusion for a design, product, and location.
func (r *RelationalDb) DeleteCartInclusion(ctx context.Context, designId uuid.UUID, productId uuid.UUID, location usecases.Location) error {
	query := `DELETE FROM design.cart_inclusions WHERE room_design_id = $1 AND product_id = $2 AND location = $3`

	cmdTag, err := r.db.Exec(ctx, query, designId, productId, string(location))
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to delete cart inclusion",
			slog.String("error", err.Error()),
			slog.String("designId", designId.String()),
			slog.String("productId", productId.String()),
			slog.String("location", string(location)))
		return fmt.Errorf("failed to delete cart inclusion: %w", err)
	}

	if cmdTag.RowsAffected() == 0 {
		r.logger.WarnContext(ctx, "No cart inclusion found to delete",
			slog.String("designId", designId.String()),
			slog.String("productId", productId.String()),
			slog.String("location", string(location)))
		return usecases.ErrNotFound
	}

	r.logger.DebugContext(ctx, "Cart inclusion deleted successfully",
		slog.String("designId", designId.String()),
		slog.String("productId", productId.String()),
		slog.String("location", string(location)))

	return nil
}

// DeleteCartInclusionsForDesign removes all cart inclusions for a design.
func (r *RelationalDb) DeleteCartInclusionsForDesign(ctx context.Context, designId uuid.UUID) error {
	query := `DELETE FROM design.cart_inclusions WHERE room_design_id = $1`

	cmdTag, err := r.db.Exec(ctx, query, designId)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to delete all cart inclusions",
			slog.String("error", err.Error()),
			slog.String("designId", designId.String()))
		return fmt.Errorf("failed to delete all cart inclusions: %w", err)
	}

	r.logger.DebugContext(ctx, "All cart inclusions deleted",
		slog.String("designId", designId.String()),
		slog.Int64("deletedCount", cmdTag.RowsAffected()))

	return nil
}
