// Package gateways_test contains comprehensive unit tests for the OpenAI gateway implementation.
//
// This test suite covers:
// - OpenAI struct creation and interface compliance
// - GenerateSchema function with various struct types and edge cases
// - TitleAndDescription struct JSON marshaling/unmarshaling
// - Helper functions for creating test designs
package gateways_test

import (
	"database/sql"
	"encoding/json"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/openai/openai-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestNewOpenAI(t *testing.T) {
	client := openai.NewClient()
	openAI := gateways.NewOpenAI(client)
	assert.NotNil(t, openAI)
}

func TestGenerateSchema(t *testing.T) {
	type TestStruct struct {
		Name string `json:"name"`
		Age  int    `json:"age"`
	}

	schema := gateways.GenerateSchema[TestStruct]()

	assert.NotNil(t, schema)
	// The schema should be a valid JSON schema object
	schemaBytes, err := json.Marshal(schema)
	require.NoError(t, err)
	assert.Contains(t, string(schemaBytes), "name")
	assert.Contains(t, string(schemaBytes), "age")
}

func TestTitleAndDescriptionStruct(t *testing.T) {
	td := gateways.TitleAndDescription{
		Title:       "Modern Bathroom",
		Description: "A sleek modern bathroom with clean lines",
	}

	jsonBytes, err := json.Marshal(td)
	require.NoError(t, err)

	var unmarshaled gateways.TitleAndDescription
	err = json.Unmarshal(jsonBytes, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, td.Title, unmarshaled.Title)
	assert.Equal(t, td.Description, unmarshaled.Description)
}

func createTestDesign() usecases.Design {
	floorTileID := uuid.New()
	toiletID := uuid.New()
	vanityID := uuid.New()
	faucetID := uuid.New()
	mirrorID := uuid.New()
	paintID := uuid.New()

	return usecases.Design{
		ID:                 uuid.New(),
		ProjectID:          entities.ProjectId("PRJ-TEST"),
		Created:            time.Now(),
		LastUpdated:        time.Now(),
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: &floorTileID,
				Toilet:    &toiletID,
				Mirror:    &mirrorID,
				Paint:     &paintID,
			},
			Vanity:      &vanityID,
			Faucet:      &faucetID,
			Title:       sql.NullString{String: "Test Design", Valid: true},
			Description: sql.NullString{String: "Test Description", Valid: true},
		},
	}
}

func createTestDesignWithAllProducts() usecases.Design {
	design := createTestDesign()

	// Add all possible product IDs
	wallTileID := uuid.New()
	showerWallTileID := uuid.New()
	showerFloorTileID := uuid.New()
	showerSystemID := uuid.New()
	showerShortWallTileID := uuid.New()
	showerGlassID := uuid.New()
	tubID := uuid.New()
	tubDoorID := uuid.New()
	nicheTileID := uuid.New()
	wallpaperID := uuid.New()
	shelvingID := uuid.New()
	lightingID := uuid.New()

	design.WallTile = &wallTileID
	design.ShowerWallTile = &showerWallTileID
	design.ShowerFloorTile = &showerFloorTileID
	design.ShowerSystem = &showerSystemID
	design.ShowerShortWallTile = &showerShortWallTileID
	design.ShowerGlass = &showerGlassID
	design.Tub = &tubID
	design.TubDoor = &tubDoorID
	design.NicheTile = &nicheTileID
	design.Wallpaper = &wallpaperID
	design.Shelving = &shelvingID
	design.Lighting = &lightingID

	return design
}

func createEmptyDesign() usecases.Design {
	return usecases.Design{
		ID:                 uuid.New(),
		ProjectID:          entities.ProjectId("PRJ-EMPTY"),
		Created:            time.Now(),
		LastUpdated:        time.Now(),
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.NoWallTile,
		DesignOptions:      usecases.DesignOptions{
			// No products
		},
	}
}

// Test the TitleAndDescription struct with edge cases
func TestTitleAndDescription_EdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		title       string
		description string
	}{
		{
			name:        "empty strings",
			title:       "",
			description: "",
		},
		{
			name:        "very long title",
			title:       strings.Repeat("A", 1000),
			description: "Short description",
		},
		{
			name:        "very long description",
			title:       "Short title",
			description: strings.Repeat("B", 5000),
		},
		{
			name:        "special characters",
			title:       "Title with émojis 🏠 and spëcial chars",
			description: "Description with newlines\nand\ttabs",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			td := gateways.TitleAndDescription{
				Title:       tt.title,
				Description: tt.description,
			}

			jsonBytes, err := json.Marshal(td)
			require.NoError(t, err)

			var unmarshaled gateways.TitleAndDescription
			err = json.Unmarshal(jsonBytes, &unmarshaled)
			require.NoError(t, err)

			assert.Equal(t, tt.title, unmarshaled.Title)
			assert.Equal(t, tt.description, unmarshaled.Description)
		})
	}
}

// Test GenerateSchema with TitleAndDescription specifically
func TestGenerateSchema_TitleAndDescription(t *testing.T) {
	schema := gateways.GenerateSchema[gateways.TitleAndDescription]()

	assert.NotNil(t, schema)

	schemaBytes, err := json.Marshal(schema)
	require.NoError(t, err)
	schemaJSON := string(schemaBytes)

	// Should contain the expected fields
	assert.Contains(t, schemaJSON, "title")
	assert.Contains(t, schemaJSON, "description")

	// Should be a proper JSON schema
	assert.Contains(t, schemaJSON, "type")
	assert.Contains(t, schemaJSON, "properties")
}

// Test GenerateSchema with empty struct
func TestGenerateSchema_EmptyStruct(t *testing.T) {
	type EmptyStruct struct{}

	schema := gateways.GenerateSchema[EmptyStruct]()
	assert.NotNil(t, schema)

	schemaBytes, err := json.Marshal(schema)
	require.NoError(t, err)

	// Should still be valid JSON schema even for empty struct
	assert.Contains(t, string(schemaBytes), "type")
}

// Test GenerateSchema with struct containing json tags
func TestGenerateSchema_JSONTags(t *testing.T) {
	type TaggedStruct struct {
		FieldOne   string `json:"field_one"`
		FieldTwo   int    `json:"field_two,omitempty"`
		FieldThree bool   `json:"-"` // Should be ignored
	}

	schema := gateways.GenerateSchema[TaggedStruct]()
	assert.NotNil(t, schema)

	schemaBytes, err := json.Marshal(schema)
	require.NoError(t, err)
	schemaJSON := string(schemaBytes)

	// Should respect JSON tags
	assert.Contains(t, schemaJSON, "field_one")
	assert.Contains(t, schemaJSON, "field_two")
	// field_three should be ignored due to json:"-" tag
	assert.NotContains(t, schemaJSON, "field_three")
	assert.NotContains(t, schemaJSON, "FieldThree")
}

// Test TitleAndDescription with JSON marshaling edge cases
func TestTitleAndDescription_JSONMarshaling(t *testing.T) {
	tests := []struct {
		name string
		td   gateways.TitleAndDescription
	}{
		{
			name: "with quotes",
			td: gateways.TitleAndDescription{
				Title:       `Title with "quotes"`,
				Description: `Description with "quotes" and 'apostrophes'`,
			},
		},
		{
			name: "with unicode",
			td: gateways.TitleAndDescription{
				Title:       "Título con acentos",
				Description: "Descripción with émojis 🏠🛁",
			},
		},
		{
			name: "with escape sequences",
			td: gateways.TitleAndDescription{
				Title:       "Title\nwith\nnewlines",
				Description: "Description\twith\ttabs",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonBytes, err := json.Marshal(tt.td)
			require.NoError(t, err)

			var unmarshaled gateways.TitleAndDescription
			err = json.Unmarshal(jsonBytes, &unmarshaled)
			require.NoError(t, err)

			assert.Equal(t, tt.td.Title, unmarshaled.Title)
			assert.Equal(t, tt.td.Description, unmarshaled.Description)
		})
	}
}

// Test design creation helpers
func TestDesignCreationHelpers(t *testing.T) {
	t.Run("createTestDesign", func(t *testing.T) {
		design := createTestDesign()

		assert.NotEqual(t, uuid.Nil, design.ID)
		assert.Equal(t, entities.ProjectId("PRJ-TEST"), design.ProjectID)
		assert.Equal(t, usecases.Preview, design.Status)
		assert.Equal(t, usecases.VanityWall, design.WallpaperPlacement)
		assert.Equal(t, usecases.HalfWall, design.WallTilePlacement)

		// Should have some products
		assert.NotNil(t, design.FloorTile)
		assert.NotNil(t, design.Toilet)
		assert.NotNil(t, design.Vanity)
		assert.NotNil(t, design.Faucet)
		assert.NotNil(t, design.Mirror)
		assert.NotNil(t, design.Paint)

		// Should have valid title and description
		assert.True(t, design.Title.Valid)
		assert.Equal(t, "Test Design", design.Title.String)
		assert.True(t, design.Description.Valid)
		assert.Equal(t, "Test Description", design.Description.String)
	})

	t.Run("createTestDesignWithAllProducts", func(t *testing.T) {
		design := createTestDesignWithAllProducts()

		// Should have all products set
		assert.NotNil(t, design.FloorTile)
		assert.NotNil(t, design.Toilet)
		assert.NotNil(t, design.Vanity)
		assert.NotNil(t, design.Faucet)
		assert.NotNil(t, design.Mirror)
		assert.NotNil(t, design.Paint)
		assert.NotNil(t, design.WallTile)
		assert.NotNil(t, design.ShowerWallTile)
		assert.NotNil(t, design.ShowerFloorTile)
		assert.NotNil(t, design.ShowerSystem)
		assert.NotNil(t, design.ShowerShortWallTile)
		assert.NotNil(t, design.ShowerGlass)
		assert.NotNil(t, design.Tub)
		assert.NotNil(t, design.TubDoor)
		assert.NotNil(t, design.NicheTile)
		assert.NotNil(t, design.Wallpaper)
		assert.NotNil(t, design.Shelving)
		assert.NotNil(t, design.Lighting)
	})

	t.Run("createEmptyDesign", func(t *testing.T) {
		design := createEmptyDesign()

		assert.NotEqual(t, uuid.Nil, design.ID)
		assert.Equal(t, entities.ProjectId("PRJ-EMPTY"), design.ProjectID)
		assert.Equal(t, usecases.Preview, design.Status)
		assert.Equal(t, usecases.VanityWall, design.WallpaperPlacement)
		assert.Equal(t, usecases.NoWallTile, design.WallTilePlacement)

		// Should have no products
		assert.Nil(t, design.FloorTile)
		assert.Nil(t, design.Toilet)
		assert.Nil(t, design.Vanity)
		assert.Nil(t, design.Faucet)
		assert.Nil(t, design.Mirror)
		assert.Nil(t, design.Paint)
	})
}
