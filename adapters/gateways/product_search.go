package gateways

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type ProductSearch struct {
	hostname string
}

func NewProductSearch(hostname string) *ProductSearch {
	if hostname == "" {
		panic("hostname cannot be empty")
	}
	return &ProductSearch{hostname: hostname}
}

func (p *ProductSearch) FindProductsViaAI(ctx context.Context, filters usecases.ProductSearchFilters) ([]uuid.UUID, error) {
	adapterFilters := adapters.FiltersFromUsecase(filters)

	// Create request body with filters wrapped in a "filters" field
	// and optional "query" field extracted from the usecase filters.
	requestBody := struct {
		Query   *string               `json:"query,omitempty"`
		Filters adapters.FilterParams `json:"filters"`
	}{
		Query:   filters.Query,
		Filters: adapterFilters,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	url := fmt.Sprintf("https://%s/search/ai/v3/search", p.hostname)
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create AI search request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute AI search request: %w", err)
	}
	defer closeBody(resp.Body)

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Printf("Failed to read response body from %s: %v", url, err)
		} else {
			log.Printf("Failed to search products via AI from %s: %v\n", url, string(body))
		}
		return nil, fmt.Errorf("error response from AI Search for filters: %s", resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var searchResponse struct {
		ProductIds []uuid.UUID `json:"productIds"`
	}
	if err := json.Unmarshal(body, &searchResponse); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response from AI Search: %w", err)
	}

	return searchResponse.ProductIds, nil
}

func (p *ProductSearch) FindProducts(ctx context.Context, urlQueryParams string) ([]uuid.UUID, error) {
	urlQueryParams = strings.TrimSpace(urlQueryParams)
	if urlQueryParams == "" {
		return nil, fmt.Errorf("urlQueryParams cannot be empty or whitespace only")
	}

	url := fmt.Sprintf("https://%s/search/products?%s", p.hostname, urlQueryParams)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer closeBody(resp.Body)

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Printf("Failed to read response body from %s: %v", url, err)
		} else {
			log.Printf("Failed to search products via %s: %v\n", url, string(body))
		}
		return nil, fmt.Errorf("error response from Product Search for query '%s': %s", urlQueryParams, resp.Status)
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var searchResponse struct {
		Data []struct {
			Id string `json:"id"`
		} `json:"data"`
	}
	if err := json.Unmarshal(body, &searchResponse); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response from Product Search for query '%s': %w", urlQueryParams, err)
	}

	productIds := make([]uuid.UUID, 0, len(searchResponse.Data))
	for i, product := range searchResponse.Data {
		id, err := uuid.Parse(product.Id)
		if err != nil {
			return nil, fmt.Errorf("failed to parse product ID '%s' at index %d for query '%s': %w",
				product.Id, i, urlQueryParams, err)
		}
		productIds = append(productIds, id)
	}

	return productIds, nil
}
