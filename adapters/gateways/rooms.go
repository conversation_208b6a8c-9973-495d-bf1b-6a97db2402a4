package gateways

import (
	"encoding/json"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type Rooms struct {
	defaultRoomLayout entities.RoomLayout
}

func NewRooms(defaultScan json.RawMessage) *Rooms {
	var scan adapters.RoomLayout
	if err := json.Unmarshal(defaultScan, &scan); err != nil {
		panic(err)
	}
	if err := json.Unmarshal([]byte(DefaultMeasurements), &scan.Measurements); err != nil {
		panic(err)
	}
	return &Rooms{defaultRoomLayout: scan.ToEntity()}
}

func (r *Rooms) DefaultRoomLayout() entities.RoomLayout {
	return r.defaultRoomLayout
}

const DefaultMeasurements = `{
  "ceilingArea": 64.89221,
  "floorArea": 50.3027878,
  "halfWallTileLength": 306.588379,
  "linearLengthOfWall": 276.567932,
  "nichesArea": 10.4444237,
  "showerAreaHeight": 75.66934,
  "showerWallArea": 58.05525,
  "tubLength": 58.36333,
  "vanityHalfWallArea": 36.19948,
  "vanityLength": 78.5976,
  "vanityWallArea": 82.4565048,
  "vanityWallLength": 124.112495,
  "wallHalfArea": 89.42161,
  "wallPaintArea": 203.687836
}`
