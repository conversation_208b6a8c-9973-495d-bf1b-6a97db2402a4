package gateways

import (
	"context"
	"errors"
	"fmt"
	"log/slog"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// templateQueryBase uses the template.template_details view to simplify template retrieval.
// This view consolidates the complex joins across multiple template-related tables:
// - template.templates (main template data)
// - template.template_product_selections (product selections)
// - template.template_options (template options like tub types, shower glass)
// - template.template_provenance (brand information, optional)
// - public.legacy_lookup (legacy ID mapping, optional)
// Note: vanity_scaling_options are fetched separately using an optimized batch query.
const templateQueryBase = ` SELECT
		id, updated_at, color_scheme, style, name, image_url,
		description, inspiration, atmosphere, color_palette,
		material_palette, highlighted_brand_urls,
		floor_tile, lighting, mirror, paint, shelving, toilet,
		shower_floor_tile, shower_wall_tile, tub_filler,
		wall_tile_placement, wall_tile, wallpaper_placement, wallpaper,
		alcove_tub, freestanding_tub, shower_glass_fixed, shower_glass_sliding,
		shower_system_full, shower_system_shower, tub_door_fixed, tub_door_sliding,
		lighting_brand, plumbing_brand, toilet_brand, vanity_brand, vanity_storage,
		legacy_id
	FROM template.template_details
`

// InsertTemplate persists a Template entity to the database.
func (r *RelationalDb) InsertTemplate(ctx context.Context,
	template usecases.Template, legacyId string) (uuid.UUID, error) {

	var zeroUUID uuid.UUID
	if template.ID == zeroUUID {
		template.ID = uuid.New()
		r.logger.InfoContext(ctx, "Generated new ID for template",
			slog.String("templateID", template.ID.String()))
	}

	tx, err := r.db.Begin(ctx)
	if err != nil {
		return zeroUUID, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err != nil {
			if err := tx.Rollback(ctx); err != nil {
				r.logger.ErrorContext(ctx, "Failed to rollback transaction",
					slog.String("error", err.Error()))
			}
		}
	}()

	templateQuery := `
		INSERT INTO template.templates (
			id, color_scheme, style, name, description, image_url, inspiration,
			atmosphere, color_palette, material_palette, highlighted_brand_urls
		) VALUES (
			@id, @color_scheme, @style, @name, @description, @image_url, @inspiration,
			@atmosphere, @color_palette, @material_palette, @highlighted_brand_urls
		)`

	brandUrls := make([]string, len(template.HighlightedBrandUrls))
	for i, url := range template.HighlightedBrandUrls {
		brandUrls[i] = url.String()
	}

	_, err = tx.Exec(ctx, templateQuery, pgx.NamedArgs{
		"id":                     template.ID,
		"color_scheme":           template.ColorScheme,
		"style":                  template.Style,
		"name":                   template.Name,
		"description":            template.Description,
		"image_url":              template.ImageURL.String(),
		"inspiration":            template.Inspiration,
		"atmosphere":             template.Atmosphere,
		"color_palette":          template.ColorPalette,
		"material_palette":       template.MaterialPalette,
		"highlighted_brand_urls": brandUrls,
	})
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to insert into templates", slog.String("error", err.Error()))
		return zeroUUID, fmt.Errorf("unable to create template: %w", err)
	}

	productSelectionsQuery := `
		INSERT INTO template.template_product_selections (
			template_id, floor_tile, lighting, mirror, paint, shelving, toilet,
			shower_floor_tile, shower_wall_tile, tub_filler, wall_tile_placement,
			wall_tile, wallpaper_placement, wallpaper
		) VALUES (
			@template_id, @floor_tile, @lighting, @mirror, @paint, @shelving, @toilet,
			@shower_floor_tile, @shower_wall_tile, @tub_filler, @wall_tile_placement,
			@wall_tile, @wallpaper_placement, @wallpaper
		)`

	_, err = tx.Exec(ctx, productSelectionsQuery, pgx.NamedArgs{
		"template_id":         template.ID,
		"floor_tile":          template.FloorTile,
		"lighting":            template.Lighting,
		"mirror":              template.Mirror,
		"paint":               template.Paint,
		"shelving":            template.Shelving,
		"toilet":              template.Toilet,
		"shower_floor_tile":   template.ShowerFloorTile,
		"shower_wall_tile":    template.ShowerWallTile,
		"tub_filler":          template.TubFiller,
		"wall_tile_placement": template.WallTilePlacement,
		"wall_tile":           template.WallTile,
		"wallpaper_placement": template.WallpaperPlacement,
		"wallpaper":           template.Wallpaper,
	})
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to insert into template_product_selections",
			slog.String("error", err.Error()), slog.String("templateID", template.ID.String()))
		return zeroUUID, fmt.Errorf("could not save template product selections: %w", err)
	}

	optionsQuery := `
		INSERT INTO template.template_options (
			template_id, alcove_tub, freestanding_tub, shower_glass_fixed,
			shower_glass_sliding, shower_system_full, shower_system_shower,
			tub_door_fixed, tub_door_sliding
		) VALUES (
			@template_id, @alcove_tub, @freestanding_tub, @shower_glass_fixed,
			@shower_glass_sliding, @shower_system_full, @shower_system_shower,
			@tub_door_fixed, @tub_door_sliding
		)`

	_, err = tx.Exec(ctx, optionsQuery, pgx.NamedArgs{
		"template_id":          template.ID,
		"alcove_tub":           template.AlcoveTub,
		"freestanding_tub":     template.FreestandingTub,
		"shower_glass_fixed":   template.ShowerGlassFixed,
		"shower_glass_sliding": template.ShowerGlassSliding,
		"shower_system_full":   template.ShowerSystemCombo,
		"shower_system_shower": template.ShowerSystemSolo,
		"tub_door_fixed":       template.TubDoorFixed,
		"tub_door_sliding":     template.TubDoorSliding,
	})
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to insert into template_options",
			slog.String("error", err.Error()), slog.String("templateID", template.ID.String()))
		return zeroUUID, fmt.Errorf("could not save template options: %w", err)
	}

	if template.LightingBrand != nil || template.PlumbingBrand != nil || template.ToiletBrand != nil || template.VanityBrand != nil || template.VanityStorage != nil {
		provenanceQuery := `
			INSERT INTO template.template_provenance (
				template_id, lighting_brand, plumbing_brand, toilet_brand,
				vanity_brand, vanity_storage
			) VALUES (
				@template_id, @lighting_brand, @plumbing_brand, @toilet_brand,
				@vanity_brand, @vanity_storage
			)`

		lightingBrand := ""
		if template.LightingBrand != nil {
			lightingBrand = *template.LightingBrand
		}
		plumbingBrand := ""
		if template.PlumbingBrand != nil {
			plumbingBrand = *template.PlumbingBrand
		}
		toiletBrand := ""
		if template.ToiletBrand != nil {
			toiletBrand = *template.ToiletBrand
		}
		vanityBrand := ""
		if template.VanityBrand != nil {
			vanityBrand = *template.VanityBrand
		}
		vanityStorage := ""
		if template.VanityStorage != nil {
			vanityStorage = *template.VanityStorage
		}

		_, err = tx.Exec(ctx, provenanceQuery, pgx.NamedArgs{
			"template_id":    template.ID,
			"lighting_brand": lightingBrand,
			"plumbing_brand": plumbingBrand,
			"toilet_brand":   toiletBrand,
			"vanity_brand":   vanityBrand,
			"vanity_storage": vanityStorage,
		})
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to insert into template_provenance",
				slog.String("error", err.Error()), slog.String("templateID", template.ID.String()))
			return zeroUUID, fmt.Errorf("could not save template provenance: %w", err)
		}
	}

	if len(template.VanityScalingOptions) > 0 {
		vanityScalingQuery := `
			INSERT INTO template.vanity_scaling_options (
				template_id, min_vanity_length_inches, vanity_product_id, faucet_product_id
			) VALUES (
				@template_id, @min_vanity_length_inches, @vanity_product_id, @faucet_product_id
			)`

		for minVanityLengthInches, option := range template.VanityScalingOptions {
			_, err = tx.Exec(ctx, vanityScalingQuery, pgx.NamedArgs{
				"template_id":              template.ID,
				"min_vanity_length_inches": minVanityLengthInches,
				"vanity_product_id":        option.VanityProductID,
				"faucet_product_id":        option.FaucetProductID,
			})
			if err != nil {
				r.logger.ErrorContext(ctx, "Failed to insert into vanity_scaling_options",
					slog.String("error", err.Error()), slog.String("templateID", template.ID.String()))
				return zeroUUID, fmt.Errorf("could not save vanity scaling options: %w", err)
			}
		}
	}

	if legacyId != "" && len(legacyId) == 2 {
		r.logger.InfoContext(ctx, "Inserting legacy ID for template", slog.String("legacyID", legacyId),
			slog.String("templateID", template.ID.String()))
		legacyLookupQuery := `
		INSERT INTO public.legacy_lookup (id, template_id)
		VALUES (@id, @template_id)`

		_, err = tx.Exec(ctx, legacyLookupQuery, pgx.NamedArgs{
			"id":          legacyId,
			"template_id": template.ID,
		})
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to insert into legacy_lookup",
				slog.String("error", err.Error()), slog.String("templateID", template.ID.String()))
			return zeroUUID, fmt.Errorf("could not save legacy lookup ID: %w", err)
		}
	}

	if err = tx.Commit(ctx); err != nil {
		r.logger.ErrorContext(ctx, "Failed to commit transaction",
			slog.String("error", err.Error()), slog.String("templateID", template.ID.String()))
		return zeroUUID, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return template.ID, nil
}

// ReadTemplate fetches a single Template entity from the database by its ID.
// It uses a view that joins related tables to construct the Template object.
func (r *RelationalDb) ReadTemplate(ctx context.Context, id uuid.UUID) (usecases.Template, error) {
	query := templateQueryBase + " WHERE id = $1"

	row := r.db.QueryRow(ctx, query, id)
	template, err := scanTemplate(row)
	if err != nil {
		return usecases.Template{}, err
	}

	// Fetch vanity scaling options separately
	vanityQuery := `
		SELECT min_vanity_length_inches, vanity_product_id, faucet_product_id
		FROM template.vanity_scaling_options
		WHERE template_id = $1`

	rows, err := r.db.Query(ctx, vanityQuery, id)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query vanity scaling options for template",
			slog.String("error", err.Error()), slog.String("templateID", id.String()))
		return usecases.Template{}, fmt.Errorf("failed to query vanity scaling options: %w", err)
	}
	defer rows.Close()

	template.VanityScalingOptions = make(map[int]usecases.VanityScalingOption)
	for rows.Next() {
		var size int
		var vanityProductID, faucetProductID uuid.UUID
		err := rows.Scan(&size, &vanityProductID, &faucetProductID)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to parse vanity scaling option for template",
				slog.String("error", err.Error()), slog.String("templateID", id.String()))
			return usecases.Template{}, fmt.Errorf("failed to scan vanity scaling option: %w", err)
		}
		template.VanityScalingOptions[size] = usecases.VanityScalingOption{
			VanityProductID: vanityProductID,
			FaucetProductID: faucetProductID,
		}
	}

	if err = rows.Err(); err != nil {
		r.logger.ErrorContext(ctx, "Failed to iterate over vanity scaling options for template",
			slog.String("error", err.Error()), slog.String("templateID", id.String()))
		return usecases.Template{}, fmt.Errorf("error iterating vanity scaling options: %w", err)
	}

	return template, nil
}

// ReadAllTemplates fetches all Template entities from the database.
func (r *RelationalDb) ReadAllTemplates(ctx context.Context) ([]usecases.Template, error) {
	query := templateQueryBase + " ORDER BY created_at DESC"

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query templates in DB", slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to query templates: %w", err)
	}
	defer rows.Close()

	var templates []usecases.Template
	for rows.Next() {
		template, err := scanTemplate(rows)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to parse template row", slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to scan template: %w", err)
		}
		templates = append(templates, template)
	}

	if err = rows.Err(); err != nil {
		r.logger.ErrorContext(ctx, "Failed to iterate over template rows", slog.String("error", err.Error()))
		return nil, fmt.Errorf("error iterating template rows: %w", err)
	}

	// Fetch vanity scaling options for all templates in a single query
	err = r.populateVanityScalingOptions(ctx, templates)
	if err != nil {
		return nil, err
	}

	return templates, nil
}

// TemplatesById fetches multiple Template entities from the database by their IDs.
func (r *RelationalDb) TemplatesById(ctx context.Context, templateIds []uuid.UUID) ([]usecases.Template, error) {
	if len(templateIds) == 0 {
		return []usecases.Template{}, nil
	}

	query := templateQueryBase + " WHERE id = ANY($1) ORDER BY created_at DESC"

	rows, err := r.db.Query(ctx, query, templateIds)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query templates by IDs in DB", slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to query templates by IDs: %w", err)
	}
	defer rows.Close()

	var templates []usecases.Template
	for rows.Next() {
		template, err := scanTemplate(rows)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to parse template row", slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to scan template: %w", err)
		}
		templates = append(templates, template)
	}

	if err = rows.Err(); err != nil {
		r.logger.ErrorContext(ctx, "Failed to iterate over template rows", slog.String("error", err.Error()))
		return nil, fmt.Errorf("error iterating template rows: %w", err)
	}

	// Fetch vanity scaling options for all templates in a single query
	err = r.populateVanityScalingOptions(ctx, templates)
	if err != nil {
		return nil, err
	}

	return templates, nil
}

// ReadTemplateByLegacyId fetches a single Template entity from the database by its legacy ID.
// It joins with the legacy_lookup table to find the template UUID, then fetches the complete template.
func (r *RelationalDb) ReadTemplateByLegacyId(ctx context.Context, legacyId string) (usecases.Template, error) {
	query := templateQueryBase + " WHERE legacy_id = $1"

	row := r.db.QueryRow(ctx, query, legacyId)
	template, err := scanTemplate(row)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			r.logger.ErrorContext(ctx, "Failed to find template by legacy ID", slog.String("legacyID", legacyId))
			return usecases.Template{}, usecases.ErrNotFound
		}
		r.logger.ErrorContext(ctx, "Failed to scan template row",
			slog.String("error", err.Error()), slog.String("legacyID", legacyId))
		return usecases.Template{}, err
	}

	// Fetch vanity scaling options separately
	vanityQuery := `
		SELECT min_vanity_length_inches, vanity_product_id, faucet_product_id
		FROM template.vanity_scaling_options
		WHERE template_id = $1`

	rows, err := r.db.Query(ctx, vanityQuery, template.ID)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query vanity scaling options for template",
			slog.String("error", err.Error()), slog.String("templateID", template.ID.String()))
		return usecases.Template{}, fmt.Errorf("failed to query vanity scaling options: %w", err)
	}
	defer rows.Close()

	template.VanityScalingOptions = make(map[int]usecases.VanityScalingOption)
	for rows.Next() {
		var size int
		var vanityProductID, faucetProductID uuid.UUID
		err := rows.Scan(&size, &vanityProductID, &faucetProductID)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to parse vanity scaling option for template",
				slog.String("error", err.Error()), slog.String("templateID", template.ID.String()))
			return usecases.Template{}, fmt.Errorf("failed to scan vanity scaling option: %w", err)
		}
		template.VanityScalingOptions[size] = usecases.VanityScalingOption{
			VanityProductID: vanityProductID,
			FaucetProductID: faucetProductID,
		}
	}

	if err = rows.Err(); err != nil {
		r.logger.ErrorContext(ctx, "Failed to iterate over vanity scaling options for template",
			slog.String("error", err.Error()), slog.String("templateID", template.ID.String()))
		return usecases.Template{}, fmt.Errorf("error iterating vanity scaling options: %w", err)
	}

	return template, nil
}

// populateVanityScalingOptions fetches vanity scaling options for multiple templates in a single query
// and populates the VanityScalingOptions field for each template.
func (r *RelationalDb) populateVanityScalingOptions(ctx context.Context, templates []usecases.Template) error {
	if len(templates) == 0 {
		return nil
	}

	templateIds := make([]uuid.UUID, len(templates))
	for i, template := range templates {
		templateIds[i] = template.ID
	}

	vanityQuery := `
		SELECT template_id, min_vanity_length_inches, vanity_product_id, faucet_product_id
		FROM template.vanity_scaling_options
		WHERE template_id = ANY($1)
		ORDER BY template_id, min_vanity_length_inches`

	rows, err := r.db.Query(ctx, vanityQuery, templateIds)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query vanity scaling options for templates",
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to query vanity scaling options: %w", err)
	}
	defer rows.Close()

	vanityOptionsByTemplate := make(map[uuid.UUID]map[int]usecases.VanityScalingOption)
	for rows.Next() {
		var templateID uuid.UUID
		var size int
		var vanityProductID, faucetProductID uuid.UUID
		err := rows.Scan(&templateID, &size, &vanityProductID, &faucetProductID)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to scan vanity scaling option",
				slog.String("error", err.Error()))
			return fmt.Errorf("failed to scan vanity scaling option: %w", err)
		}

		if vanityOptionsByTemplate[templateID] == nil {
			vanityOptionsByTemplate[templateID] = make(map[int]usecases.VanityScalingOption)
		}
		vanityOptionsByTemplate[templateID][size] = usecases.VanityScalingOption{
			VanityProductID: vanityProductID,
			FaucetProductID: faucetProductID,
		}
	}

	if err = rows.Err(); err != nil {
		r.logger.ErrorContext(ctx, "Failed to iterate over vanity scaling options",
			slog.String("error", err.Error()))
		return fmt.Errorf("error iterating vanity scaling options: %w", err)
	}

	for i := range templates {
		if options, exists := vanityOptionsByTemplate[templates[i].ID]; exists {
			templates[i].VanityScalingOptions = options
		} else {
			templates[i].VanityScalingOptions = make(map[int]usecases.VanityScalingOption)
		}
	}

	return nil
}
