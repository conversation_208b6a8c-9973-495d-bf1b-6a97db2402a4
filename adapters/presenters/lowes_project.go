package presenters

import (
	"context"
	"log/slog"
	"net/http"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// ProjectPresenter presents Design objects as Project data
type ProjectPresenter struct {
	DataPresenter
}

// NewProjectPresenter creates a new ProjectPresenter
func NewProjectPresenter(logger *slog.Logger, w http.ResponseWriter) *ProjectPresenter {
	return &ProjectPresenter{
		DataPresenter: NewDataPresenter(logger, w),
	}
}

// PresentDesign presents a single Design as a Project
func (lp *ProjectPresenter) PresentDesign(ctx context.Context, design usecases.Design, areaSqFt float64) {
	project := adapters.FromUsecaseDesignToProject(design, areaSqFt)
	lp.PresentData(ctx, project)
}
