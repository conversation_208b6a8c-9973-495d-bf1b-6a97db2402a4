package presenters

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type PresetPresenter struct {
	DataPresenter
	defaultRoomLayout   json.RawMessage
	defaultMeasurements json.RawMessage
}

func NewPresetPresenter(w http.ResponseWriter, logger *slog.Logger, defaultRoomLayout json.RawMessage, defaultMeasurements json.RawMessage) PresetPresenter {
	return PresetPresenter{
		DataPresenter:       NewDataPresenter(logger, w),
		defaultRoomLayout:   defaultRoomLayout,
		defaultMeasurements: defaultMeasurements,
	}
}

func (pp PresetPresenter) PresentPreset(ctx context.Context, preset usecases.Preset) {
	if preset.RoomLayout.RawData == nil {
		preset.RoomLayout.RawData = pp.defaultRoomLayout
	}
	preset.Measurements = pp.defaultMeasurements
	p := adapters.FromUsecasePreset(preset)
	pp.w.Header().Set("Location", fmt.Sprintf("/presets/%s", preset.Id))
	pp.PresentData(ctx, p)
}
