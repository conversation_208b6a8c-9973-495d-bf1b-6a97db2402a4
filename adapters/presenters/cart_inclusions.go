package presenters

import (
	"context"
	"log/slog"
	"net/http"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// CartInclusionsPresenter handles presenting cart inclusions data
type CartInclusionsPresenter struct {
	DataPresenter
}

// NewCartInclusionsPresenter creates a new CartInclusionsPresenter
func NewCartInclusionsPresenter(logger *slog.Logger, w http.ResponseWriter) *CartInclusionsPresenter {
	return &CartInclusionsPresenter{
		DataPresenter: NewDataPresenter(logger, w),
	}
}

// PresentCartInclusions presents cart inclusions as JSON
func (p *CartInclusionsPresenter) PresentCartInclusions(ctx context.Context, inclusions usecases.CartInclusions) {
	p.logger.DebugContext(ctx, "Presenting cart inclusions",
		slog.Int("count", len(inclusions)))

	// Convert to a JSON-serializable format
	// Since the composite key can't be directly serialized, we'll create a slice of inclusions
	// with the key information embedded in each inclusion
	serializable := make([]usecases.CartInclusion, 0, len(inclusions))
	for _, inclusion := range inclusions {
		serializable = append(serializable, inclusion)
	}

	p.PresentData(ctx, serializable)
}

// CartInclusionOutcomePresenter handles cart inclusion operation outcomes
type CartInclusionOutcomePresenter struct {
	OutcomePresenter
}

// NewCartInclusionOutcomePresenter creates a new CartInclusionOutcomePresenter
func NewCartInclusionOutcomePresenter(logger *slog.Logger, w http.ResponseWriter) *CartInclusionOutcomePresenter {
	return &CartInclusionOutcomePresenter{
		OutcomePresenter: NewOutcomePresenter(logger, w),
	}
}

// PresentError handles cart inclusion specific errors
func (p *CartInclusionOutcomePresenter) PresentError(err error) {
	p.logger.Error("Error with cart inclusion operation", slog.String("error message", err.Error()))
	p.OutcomePresenter.PresentError(err)
}
