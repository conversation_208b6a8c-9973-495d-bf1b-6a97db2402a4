package presenters

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"net/url"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type RenditionsPresenter struct {
	DataPresenter
	defaultDesignerImageURL url.URL
}

func NewRenditionsPresenter(logger *slog.Logger, baseCdnURL string, w http.ResponseWriter) *RenditionsPresenter {
	parsedUrl, err := url.Parse(baseCdnURL)
	if err != nil {
		panic(err)
	}
	parsedUrl.Path = "/images/designers/bond-studio.jpg"
	defaultDesignerImageURL := *parsedUrl
	return &RenditionsPresenter{
		DataPresenter:           NewDataPresenter(logger, w),
		defaultDesignerImageURL: defaultDesignerImageURL,
	}
}

func (rp *RenditionsPresenter) PresentRenditions(ctx context.Context, renditions []entities.Rendition) {
	output := make([]adapters.Rendition, len(renditions))
	defaultDesignerImage := rp.defaultDesignerImageURL.String()
	for i, r := range renditions {
		output[i] = adapters.FromDomainRendition(r)
		if output[i].DesignerImage == nil {
			output[i].DesignerImage = &defaultDesignerImage
		}
	}
	wrappedOutput := map[string]any{
		"data": output,
	}
	rp.PresentData(ctx, wrappedOutput)
}

type RenditionCreationOutcomePresenter struct {
	OutcomePresenter
	designId uuid.UUID
}

func NewRenditionCreationOutcomePresenter(logger *slog.Logger, w http.ResponseWriter, designId uuid.UUID) *RenditionCreationOutcomePresenter {
	return &RenditionCreationOutcomePresenter{OutcomePresenter: NewOutcomePresenter(logger, w), designId: designId}
}

func (p *RenditionCreationOutcomePresenter) ConveySuccessWithResource(rendition entities.Rendition) {
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	var zeroUUID uuid.UUID
	if rendition.Id == uuid.Nil || rendition.Id == zeroUUID {
		http.Error(p.w, "Missing/invalid ID in created rendition", http.StatusInternalServerError)
		return
	}
	p.w.Header().Set("Location", fmt.Sprintf("/designs/%s/renditions/%s", p.designId, rendition.Id))
	p.w.Header().Set("Content-Type", "application/json")
	wrappedOutput := map[string]any{
		"data": adapters.FromDomainRendition(rendition),
	}
	p.w.WriteHeader(http.StatusCreated)
	p.PresentData(context.Background(), wrappedOutput)
}
