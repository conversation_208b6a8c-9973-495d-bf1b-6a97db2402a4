package presenters

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type TemplatesPresenter struct {
	DataPresenter
	useLegacyIds bool
}

func NewTemplatesPresenter(logger *slog.Logger, w http.ResponseWriter, useLegacyIds bool) *TemplatesPresenter {
	return &TemplatesPresenter{DataPresenter: NewDataPresenter(logger, w), useLegacyIds: useLegacyIds}
}

func (tp *TemplatesPresenter) PresentTemplate(ctx context.Context, template usecases.Template) {
	output := adapters.FromUsecaseTemplate(template, tp.useLegacyIds)
	wrappedOutput := map[string]any{
		"data": []adapters.Template{output},
	}
	tp.PresentData(ctx, wrappedOutput)
}

func (tp *TemplatesPresenter) PresentTemplates(ctx context.Context, templates []usecases.Template) {
	output := make([]adapters.Template, len(templates))
	for i, t := range templates {
		output[i] = adapters.FromUsecaseTemplate(t, tp.useLegacyIds)
	}
	wrappedOutput := map[string]any{
		"data": output,
	}
	tp.PresentData(ctx, wrappedOutput)
}

// TemplateCreationOutcomePresenter handles the presentation of template creation/mutation outcomes
type TemplateCreationOutcomePresenter struct {
	OutcomePresenter
}

func NewTemplateCreationOutcomePresenter(logger *slog.Logger, w http.ResponseWriter) *TemplateCreationOutcomePresenter {
	return &TemplateCreationOutcomePresenter{OutcomePresenter: NewOutcomePresenter(logger, w)}
}

func (p *TemplateCreationOutcomePresenter) ConveySuccessWithNewResource(template usecases.Template) {
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	var zeroUUID uuid.UUID
	if template.ID == uuid.Nil || template.ID == zeroUUID {
		http.Error(p.w, "Missing/invalid ID in created template", http.StatusInternalServerError)
		return
	}
	p.w.Header().Set("Location", fmt.Sprintf("/templates/%s", template.ID))
	p.w.Header().Set("Content-Type", "application/json")
	p.w.WriteHeader(http.StatusCreated)

	output := adapters.FromUsecaseTemplate(template, false)
	wrappedOutput := map[string]any{
		"data": output,
	}
	response, err := json.MarshalIndent(wrappedOutput, "", "  ")
	if err != nil {
		http.Error(p.w, err.Error(), http.StatusInternalServerError)
		return
	}
	if _, err := p.w.Write(response); err != nil {
		p.logger.Error("Failed to write response", slog.String("error message", err.Error()))
	}
}
