package presenters_test

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

func TestNewProjectIdPresenter(t *testing.T) {
	t.Run("should create presenter with valid parameters", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		logger := slog.Default()
		presenter := presenters.NewProjectIdPresenter(logger, recorder)
		assert.NotNil(t, presenter)
	})

	t.Run("should create presenter with nil logger (uses default)", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewProjectIdPresenter(nil, recorder)
		assert.NotNil(t, presenter)
	})

	t.Run("should panic with nil response writer", func(t *testing.T) {
		logger := slog.Default()
		assert.Panics(t, func() {
			presenters.NewProjectIdPresenter(logger, nil)
		})
	})
}

func TestProjectIdPresenter_PresentProjectId(t *testing.T) {
	t.Run("should present project ID as JSON", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		logger := slog.Default()
		presenter := presenters.NewProjectIdPresenter(logger, recorder)

		projectId := entities.NewProjectId("PRJ-12345")
		ctx := context.Background()

		presenter.PresentProjectId(ctx, projectId)

		assert.Equal(t, 200, recorder.Code)
		assert.Equal(t, "application/json", recorder.Header().Get("Content-Type"))
		assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"))

		var response map[string]string
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "PRJ-12345", response["projectId"])
	})

	t.Run("should handle empty project ID", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		logger := slog.Default()
		presenter := presenters.NewProjectIdPresenter(logger, recorder)

		projectId := entities.NewProjectId("")
		ctx := context.Background()

		presenter.PresentProjectId(ctx, projectId)

		assert.Equal(t, 200, recorder.Code)

		var response map[string]string
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "", response["projectId"])
	})

	t.Run("should handle project ID with special characters", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		logger := slog.Default()
		presenter := presenters.NewProjectIdPresenter(logger, recorder)

		projectId := entities.NewProjectId("PRJ-123/456%789")
		ctx := context.Background()

		presenter.PresentProjectId(ctx, projectId)

		assert.Equal(t, 200, recorder.Code)

		var response map[string]string
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "PRJ-123/456%789", response["projectId"])
	})
}
