package presenters

import (
	"context"
	"database/sql"
	"encoding/json"
	"log/slog"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestProjectPresenter_PresentDesign(t *testing.T) {
	// Create a test design
	designID := uuid.New()
	projectID := entities.ProjectId("test-project")
	now := time.Now()

	design := usecases.Design{
		ID:          designID,
		ProjectID:   projectID,
		Created:     now.Add(-24 * time.Hour),
		LastUpdated: now,
		Status:      usecases.Preview,
		DesignOptions: usecases.DesignOptions{
			Title:             sql.NullString{String: "Test ", Valid: true},
			NumSKUs:           sql.NullInt32{Int32: 10, Valid: true},
			TotalPriceInCents: sql.NullInt32{Int32: 1500000, Valid: true}, // $15,000
		},
	}

	// Create a test response writer
	w := httptest.NewRecorder()
	logger := slog.Default()

	// Create presenter and present the design
	presenter := NewProjectPresenter(logger, w)
	presenter.PresentDesign(context.Background(), design, 50.0)

	// Verify the response
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))

	// Parse the JSON response
	var project adapters.Project
	err := json.Unmarshal(w.Body.Bytes(), &project)
	assert.NoError(t, err)

	// Verify the project data
	assert.Equal(t, "Test ", project.Name)
	assert.WithinDuration(t, now, project.UpdatedAt, time.Second)
	assert.Equal(t, 15000.0, project.CostUSD)
	assert.Equal(t, 10, project.NumItems)
	assert.Equal(t, 50.0, project.AreaSqFt)
}
