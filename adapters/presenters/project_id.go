package presenters

import (
	"context"
	"log/slog"
	"net/http"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type ProjectIdPresenter struct {
	DataPresenter
}

func NewProjectIdPresenter(logger *slog.Logger, w http.ResponseWriter) *ProjectIdPresenter {
	return &ProjectIdPresenter{
		DataPresenter: NewDataPresenter(logger, w),
	}
}

func (p *ProjectIdPresenter) PresentProjectId(ctx context.Context, projectId entities.ProjectId) {
	response := map[string]string{
		"projectId": projectId.String(),
	}
	p.PresentData(ctx, response)
}
