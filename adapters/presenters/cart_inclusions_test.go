package presenters_test

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestCartInclusionsPresenter_PresentCartInclusions(t *testing.T) {
	ctx := context.Background()
	w := httptest.NewRecorder()
	presenter := presenters.NewCartInclusionsPresenter(nil, w)

	productId1 := uuid.New()
	productId2 := uuid.New()

	key1 := usecases.CartInclusionKey{ProductID: productId1, Location: usecases.LocationFloor}
	key2 := usecases.CartInclusionKey{ProductID: productId2, Location: usecases.LocationWall}

	inclusions := usecases.CartInclusions{
		key1: {
			ProductID:    productId1,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 2,
		},
		key2: {
			ProductID:    productId2,
			Location:     usecases.LocationWall,
			Include:      false,
			QuantityDiff: -1,
		},
	}

	presenter.PresentCartInclusions(ctx, inclusions)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))

	// Parse response body
	var response []usecases.CartInclusion
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify inclusions are in array format
	assert.Len(t, response, 2)

	// Find inclusions by product ID
	var inclusion1, inclusion2 *usecases.CartInclusion
	for i := range response {
		if response[i].ProductID == productId1 {
			inclusion1 = &response[i]
		} else if response[i].ProductID == productId2 {
			inclusion2 = &response[i]
		}
	}

	// Verify inclusion 1
	require.NotNil(t, inclusion1)
	assert.Equal(t, productId1, inclusion1.ProductID)
	assert.Equal(t, usecases.LocationFloor, inclusion1.Location)
	assert.True(t, inclusion1.Include)
	assert.Equal(t, 2, inclusion1.QuantityDiff)

	// Verify inclusion 2
	require.NotNil(t, inclusion2)
	assert.Equal(t, productId2, inclusion2.ProductID)
	assert.Equal(t, usecases.LocationWall, inclusion2.Location)
	assert.False(t, inclusion2.Include)
	assert.Equal(t, -1, inclusion2.QuantityDiff)
}

func TestCartInclusionsPresenter_PresentCartInclusions_Empty(t *testing.T) {
	ctx := context.Background()
	w := httptest.NewRecorder()
	presenter := presenters.NewCartInclusionsPresenter(nil, w)

	inclusions := usecases.CartInclusions{}

	presenter.PresentCartInclusions(ctx, inclusions)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

	// Parse response body
	var response []usecases.CartInclusion
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify empty array
	assert.Len(t, response, 0)
}

func TestCartInclusionOutcomePresenter_ConveySuccess(t *testing.T) {
	w := httptest.NewRecorder()
	presenter := presenters.NewCartInclusionOutcomePresenter(nil, w)

	presenter.ConveySuccess()

	// Verify response
	assert.Equal(t, http.StatusNoContent, w.Code)
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
	assert.Empty(t, w.Body.String())
}

func TestCartInclusionOutcomePresenter_PresentError(t *testing.T) {
	t.Run("invalid payload error", func(t *testing.T) {
		w := httptest.NewRecorder()
		presenter := presenters.NewCartInclusionOutcomePresenter(nil, w)

		presenter.PresentError(usecases.ErrInvalidPayload)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
		assert.Contains(t, w.Body.String(), "invalid payload")
	})

	t.Run("not found error", func(t *testing.T) {
		w := httptest.NewRecorder()
		presenter := presenters.NewCartInclusionOutcomePresenter(nil, w)

		presenter.PresentError(usecases.ErrNotFound)

		assert.Equal(t, http.StatusNotFound, w.Code)
		assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
		assert.Contains(t, w.Body.String(), "not found")
	})
}
