package presenters

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type DataPresenter struct {
	w      http.ResponseWriter
	logger *slog.Logger
}

func NewDataPresenter(logger *slog.Logger, w http.ResponseWriter) DataPresenter {
	if usecases.IsNil(w) {
		panic("w cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return DataPresenter{logger: logger, w: w}
}

func (dp DataPresenter) PresentError(err error) {
	var status int
	switch err {
	case usecases.ErrInvalidPayload:
		status = http.StatusBadRequest
	case usecases.ErrNotFound:
		status = http.StatusNotFound
	case usecases.ErrConflict:
		status = http.StatusConflict
	default:
		status = http.StatusInternalServerError
	}
	http.Error(dp.w, err.Error(), status)
}

func (dp DataPresenter) PresentData(ctx context.Context, data any) {
	bytes, err := json.Marshal(data)
	if err != nil {
		dp.logger.ErrorContext(ctx, "Could not marshal data into JSON",
			slog.String("error", err.Error()))
		http.Error(dp.w, err.Error(), http.StatusInternalServerError)
		return
	}
	dp.w.Header().Set("Access-Control-Allow-Origin", "*")
	dp.w.Header().Set("Content-Type", "application/json")
	if _, err = dp.w.Write(bytes); err != nil {
		http.Error(dp.w, err.Error(), http.StatusInternalServerError)
	}
}
