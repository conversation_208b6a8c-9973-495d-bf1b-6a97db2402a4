package adapters

import (
	"time"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Project represents the structure for a home renovation project.
type Project struct {
	Name      string    `json:"name"`
	UpdatedAt time.Time `json:"updated_at"`
	CostUSD   float64   `json:"cost_usd"`
	NumItems  int       `json:"num_items"`
	AreaSqFt  float64   `json:"area_sq_ft"`
	ImageURL  *string   `json:"image_url,omitempty"`
}

// FromUsecaseDesignToProject converts a usecases.Design to a Project adapter
func FromUsecaseDesignToProject(design usecases.Design, areaSqFt float64) Project {
	// Extract name from title if available, otherwise use a default
	name := "My Home Renovation Project"
	if design.Title.Valid && design.Title.String != "" {
		name = design.Title.String
	}

	// Convert total price from cents to dollars
	costUSD := 0.0
	if design.TotalPriceInCents.Valid {
		costUSD = float64(design.TotalPriceInCents.Int32) / 100.0
	}

	// Use SKU count as number of items
	numItems := 0
	if design.NumSKUs.Valid {
		numItems = int(design.NumSKUs.Int32)
	}

	// Use the first rendition's URL as image URL if available
	var imageURL *string
	if len(design.Renditions) > 0 && design.Renditions[0].URL != nil {
		url := design.Renditions[0].URL.String()
		imageURL = &url
	}

	return Project{
		Name:      name,
		UpdatedAt: design.LastUpdated,
		CostUSD:   costUSD,
		NumItems:  numItems,
		AreaSqFt:  areaSqFt,
		ImageURL:  imageURL,
	}
}
