package adapters

import (
	"database/sql"
	"net/url"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestFromUsecaseDesignToProject(t *testing.T) {
	// Create a test design
	designID := uuid.New()
	projectID := entities.ProjectId("test-project")
	now := time.Now()

	testURL, _ := url.Parse("https://example.com/image.jpg")

	design := usecases.Design{
		ID:          designID,
		ProjectID:   projectID,
		Created:     now.Add(-24 * time.Hour),
		LastUpdated: now,
		Status:      usecases.Preview,
		DesignOptions: usecases.DesignOptions{
			Title:             sql.NullString{String: "Modern Spa ", Valid: true},
			NumSKUs:           sql.NullInt32{Int32: 15, Valid: true},
			TotalPriceInCents: sql.NullInt32{Int32: 2500000, Valid: true}, // $25,000
		},
		Renditions: []entities.Rendition{
			{
				Id:  uuid.New(),
				URL: testURL,
			},
		},
	}

	// Convert to Project
	project := FromUsecaseDesignToProject(design, 50.0)

	// Verify the conversion
	assert.Equal(t, "Modern Spa ", project.Name)
	assert.Equal(t, now, project.UpdatedAt)
	assert.Equal(t, 25000.0, project.CostUSD)
	assert.Equal(t, 15, project.NumItems)
	assert.Equal(t, 50.0, project.AreaSqFt)
	assert.NotNil(t, project.ImageURL)
	assert.Equal(t, "https://example.com/image.jpg", *project.ImageURL)
}

func TestFromUsecaseDesignToProject_WithDefaults(t *testing.T) {
	// Create a minimal test design
	designID := uuid.New()
	projectID := entities.ProjectId("test-project")
	now := time.Now()

	design := usecases.Design{
		ID:          designID,
		ProjectID:   projectID,
		Created:     now.Add(-24 * time.Hour),
		LastUpdated: now,
		Status:      usecases.Preview,
		// No title, price, or SKU count
		Renditions: []entities.Rendition{}, // No renditions
	}

	// Convert to Project
	project := FromUsecaseDesignToProject(design, 0.0)

	// Verify the defaults
	assert.Equal(t, "My Home Renovation Project", project.Name)
	assert.Equal(t, now, project.UpdatedAt)
	assert.Equal(t, 0.0, project.CostUSD)
	assert.Equal(t, 0, project.NumItems)
	assert.Equal(t, 0.0, project.AreaSqFt)
	assert.Nil(t, project.ImageURL)
}
