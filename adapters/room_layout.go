package adapters

import (
	"encoding/binary"
	"encoding/json"

	"github.com/cespare/xxhash/v2"
	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type Position struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
	Z float64 `json:"z"`
}

type Rotation struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
	Z float64 `json:"z"`
}

type Scale struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
	Z float64 `json:"z"`
}

type Point2D struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

// BaseElement contains common fields for all room elements
type BaseElement struct {
	Identifier       string   `json:"identifier"`
	ParentIdentifier *string  `json:"parentIdentifier"`
	Position         Position `json:"position"`
	Rotation         Rotation `json:"rotation"`
	ShortName        *string  `json:"shortName"`
}

// ScalableElement extends BaseElement with scale information
type ScalableElement struct {
	BaseElement
	Scale Scale `json:"scale"`
}

// ShapedElement extends BaseElement with shape information
type ShapedElement struct {
	BaseElement
	Shape []Point2D `json:"shape"`
}

// Shower represents a shower area
type Shower struct {
	BaseElement
	CurbHeight    float64   `json:"curbHeight"`
	CurbThickness float64   `json:"curbThickness"`
	Shape         []Point2D `json:"shape"`
}

type Areas struct {
	Showers []Shower `json:"showers"`
}

type Ceiling struct {
	ShapedElement
}

type Door struct {
	ScalableElement
	Swing string `json:"swing"`
	Type  string `json:"type"`
}

type Floor struct {
	ShapedElement
}

// Light represents a light fixture
type Light struct {
	ScalableElement
}

type Niche struct {
	ScalableElement
}

type ShowerSystem struct {
	ScalableElement
	ShowerAreaIdentifier string `json:"showerAreaIdentifier"`
	Type                 string `json:"type"`
}

type Toilet struct {
	ScalableElement
	Type string `json:"type"`
}

type Tub struct {
	ScalableElement
	Type string `json:"type"`
}

type Vanity struct {
	ScalableElement
	Type string `json:"type"`
}

type Wall struct {
	ShapedElement
	Thickness float64 `json:"thickness"`
}

type Measurements struct {
	CeilingArea                 *float64 `json:"ceilingArea"`
	CurbArea                    *float64 `json:"curbArea"`
	FloorArea                   *float64 `json:"floorArea"`
	HalfWallTileLength          *float64 `json:"halfWallTileLength"`
	LinearLengthOfWall          *float64 `json:"linearLengthOfWall"`
	NichesArea                  *float64 `json:"nichesArea"`
	ShowerAreaHeight            *float64 `json:"showerAreaHeight"`
	ShowerFloorArea             *float64 `json:"showerFloorArea"`
	ShowerLongestOpenSideLength *float64 `json:"showerLongestOpenSideLength"`
	ShowerOpenSides             *int     `json:"showerOpenSides"`
	ShowerWallArea              *float64 `json:"showerWallArea"`
	TotalShowerCurbLength       *float64 `json:"totalShowerCurbLength"`
	TubLength                   *float64 `json:"tubLength"`
	VanityHalfWallArea          *float64 `json:"vanityHalfWallArea"`
	VanityLength                *float64 `json:"vanityLength"`
	VanityWallArea              *float64 `json:"vanityWallArea"`
	VanityWallLength            *float64 `json:"vanityWallLength"`
	WallHalfArea                *float64 `json:"wallHalfArea"`
	WallPaintArea               *float64 `json:"wallPaintArea"`
}

type RoomLayout struct {
	Areas         Areas          `json:"areas"`
	Ceilings      []Ceiling      `json:"ceilings"`
	Doors         []Door         `json:"doors"`
	Floors        []Floor        `json:"floors"`
	Lights        []Light        `json:"lights"`
	LinenCabinets []BaseElement  `json:"linenCabinets"`
	Mirrors       []BaseElement  `json:"mirrors"`
	Niches        []Niche        `json:"niches"`
	Openings      []BaseElement  `json:"openings"`
	RobeHooks     []BaseElement  `json:"robeHooks"`
	Shelves       []BaseElement  `json:"shelves"`
	ShowerSystems []ShowerSystem `json:"showerSystems"`
	Sinks         []BaseElement  `json:"sinks"`
	Toilets       []Toilet       `json:"toilets"`
	TowelBars     []BaseElement  `json:"towelBars"`
	TowelRings    []BaseElement  `json:"towelRings"`
	TpHolders     []BaseElement  `json:"tpHolders"`
	TubFillers    []BaseElement  `json:"tubFillers"`
	Tubs          []Tub          `json:"tubs"`
	Vanities      []Vanity       `json:"vanities"`
	Walls         []Wall         `json:"walls"`
	Windows       []BaseElement  `json:"windows"`
	Measurements  *Measurements  `json:"measurements,omitempty"`
}

func (rl *RoomLayout) ToEntity() entities.RoomLayout {
	// Marshal the entire RoomLayout structure to get the complete raw data
	rawData, err := json.Marshal(rl)
	if err != nil {
		return entities.RoomLayout{}
	}
	roomLayoutHash := xxhash.Sum64(rawData)

	entity := entities.RoomLayout{
		Hash:    roomLayoutHash,
		RawData: rawData,
	}

	if rl.Measurements != nil && rl.Measurements.CeilingArea != nil {
		entity.AreaSqFt = *rl.Measurements.CeilingArea
	}

	// Extract floor IDs from the structured data
	floorIds := make([]uuid.UUID, 0, len(rl.Floors))
	for _, floor := range rl.Floors {
		if id, err := uuid.Parse(floor.Identifier); err == nil {
			floorIds = append(floorIds, id)
		}
	}
	entity.FloorIds = floorIds

	// Extract toilet IDs from the structured data
	toiletIds := make([]uuid.UUID, 0, len(rl.Toilets))
	for _, toilet := range rl.Toilets {
		if id, err := uuid.Parse(toilet.Identifier); err == nil {
			toiletIds = append(toiletIds, id)
		}
	}
	entity.ToiletIds = toiletIds

	// Extract wall information
	walls := make([]entities.Wall, 0, len(rl.Walls))
	for _, wall := range rl.Walls {
		if wallId, err := uuid.Parse(wall.Identifier); err == nil {
			entityWall := entities.Wall{
				LayoutId: wallId,
				NicheIds: []uuid.UUID{}, // Initialize empty, could be populated from niches with matching parentIdentifier
			}

			// Find niches that belong to this wall
			for _, niche := range rl.Niches {
				if niche.ParentIdentifier != nil && *niche.ParentIdentifier == wall.Identifier {
					if nicheId, err := uuid.Parse(niche.Identifier); err == nil {
						entityWall.NicheIds = append(entityWall.NicheIds, nicheId)
					}
				}
			}

			walls = append(walls, entityWall)
		}
	}
	entity.Walls = walls

	// Extract vanity information
	vanities := make([]entities.Vanity, 0, len(rl.Vanities))
	for _, vanity := range rl.Vanities {
		if vanityId, err := uuid.Parse(vanity.Identifier); err == nil {
			entityVanity := entities.Vanity{
				LayoutId: vanityId,
			}
			// Set MaxLength from measurements if available
			if rl.Measurements != nil && rl.Measurements.VanityLength != nil {
				entityVanity.MaxLength = rl.Measurements.VanityLength
			}
			vanities = append(vanities, entityVanity)
		}
	}
	entity.Vanities = vanities

	// Extract wet area information (combining showers, tubs, etc.)
	wetAreas := make([]entities.WetArea, 0)

	// Group shower systems by shower area
	showerSystemsByArea := make(map[string][]uuid.UUID)
	for _, system := range rl.ShowerSystems {
		if systemId, err := uuid.Parse(system.Identifier); err == nil {
			areaId := system.ShowerAreaIdentifier
			showerSystemsByArea[areaId] = append(showerSystemsByArea[areaId], systemId)
		}
	}

	// Create wet areas from shower areas
	for _, shower := range rl.Areas.Showers {
		if showerId, err := uuid.Parse(shower.Identifier); err == nil {
			systemsForArea := showerSystemsByArea[shower.Identifier]

			wetArea := entities.WetArea{
				LayoutId:           showerId,
				GlassType:          entities.NoShowerEnclosure, // Default, could be determined from other data
				AlcoveTubs:         []entities.AlcoveTub{},
				FreestandingTubIds: []uuid.UUID{},
			}
			// Set MaxTubLength from measurements if available
			if rl.Measurements != nil && rl.Measurements.TubLength != nil {
				wetArea.MaxTubLength = rl.Measurements.TubLength
			}

			// Set MaxShowerGlassLength if possible
			if rl.Measurements != nil && rl.Measurements.ShowerOpenSides != nil &&
				*rl.Measurements.ShowerOpenSides == 1 && rl.Measurements.ShowerLongestOpenSideLength != nil {
				wetArea.MaxShowerGlassLength = rl.Measurements.ShowerLongestOpenSideLength
			}

			// Check if this shower has a parent tub (alcove tub scenario)
			if shower.ParentIdentifier != nil {
				for _, tub := range rl.Tubs {
					if tub.Identifier == *shower.ParentIdentifier {
						if tubId, err := uuid.Parse(tub.Identifier); err == nil {
							alcoveTub := entities.AlcoveTub{
								LayoutId: tubId,
								DoorType: entities.NoShowerEnclosure, // Default
							}
							// Find shower system for this tub/shower combo
							if len(systemsForArea) > 0 {
								alcoveTub.ShowerId = &systemsForArea[0]
								// Remove the assigned system from the list to avoid duplication
								systemsForArea = systemsForArea[1:]
							}
							wetArea.AlcoveTubs = append(wetArea.AlcoveTubs, alcoveTub)
							break // A shower can only have one parent tub
						}
					}
				}
			}

			// Assign any remaining (i.e., non-alcove) shower systems to the main list
			wetArea.ShowerIds = systemsForArea

			wetAreas = append(wetAreas, wetArea)
		}
	}

	// Add standalone tubs as wet areas
	for _, tub := range rl.Tubs {
		// Check if this tub is not already part of a shower area
		isPartOfShower := false
		for _, shower := range rl.Areas.Showers {
			if shower.ParentIdentifier != nil && *shower.ParentIdentifier == tub.Identifier {
				isPartOfShower = true
				break
			}
		}

		if !isPartOfShower {
			if tubId, err := uuid.Parse(tub.Identifier); err == nil {
				wetArea := entities.WetArea{
					LayoutId:           tubId,
					GlassType:          entities.NoShowerEnclosure,
					ShowerIds:          []uuid.UUID{},
					AlcoveTubs:         []entities.AlcoveTub{},
					FreestandingTubIds: []uuid.UUID{tubId},
				}
				// Set MaxTubLength from measurements if available
				if rl.Measurements != nil && rl.Measurements.TubLength != nil {
					wetArea.MaxTubLength = rl.Measurements.TubLength
				}
				wetAreas = append(wetAreas, wetArea)
			}
		}
	}

	entity.WetAreas = wetAreas

	return entity
}

func FromRoomLayoutEntity(entity entities.RoomLayout) RoomLayout {
	var roomLayout RoomLayout

	// First try to unmarshal the complete structure from RawData
	if len(entity.RawData) > 0 {
		err := json.Unmarshal(entity.RawData, &roomLayout)
		if err == nil {
			return roomLayout
		}
	}

	// Fallback: create a minimal structure from entity fields
	roomLayout = RoomLayout{
		Areas:         Areas{Showers: []Shower{}},
		Ceilings:      []Ceiling{},
		Doors:         []Door{},
		Floors:        []Floor{},
		Lights:        []Light{},
		LinenCabinets: []BaseElement{},
		Mirrors:       []BaseElement{},
		Niches:        []Niche{},
		Openings:      []BaseElement{},
		RobeHooks:     []BaseElement{},
		Shelves:       []BaseElement{},
		ShowerSystems: []ShowerSystem{},
		Sinks:         []BaseElement{},
		Toilets:       []Toilet{},
		TowelBars:     []BaseElement{},
		TowelRings:    []BaseElement{},
		TpHolders:     []BaseElement{},
		TubFillers:    []BaseElement{},
		Tubs:          []Tub{},
		Vanities:      []Vanity{},
		Walls:         []Wall{},
		Windows:       []BaseElement{},
		Measurements:  &Measurements{CeilingArea: &entity.AreaSqFt},
	}

	// Populate basic structure from entity fields if RawData parsing failed
	for _, floorId := range entity.FloorIds {
		roomLayout.Floors = append(roomLayout.Floors, Floor{
			ShapedElement: ShapedElement{
				BaseElement: BaseElement{
					Identifier: floorId.String(),
				},
			},
		})
	}

	for _, toiletId := range entity.ToiletIds {
		roomLayout.Toilets = append(roomLayout.Toilets, Toilet{
			ScalableElement: ScalableElement{
				BaseElement: BaseElement{
					Identifier: toiletId.String(),
				},
			},
		})
	}

	for _, wall := range entity.Walls {
		roomLayout.Walls = append(roomLayout.Walls, Wall{
			ShapedElement: ShapedElement{
				BaseElement: BaseElement{
					Identifier: wall.LayoutId.String(),
				},
			},
		})

		// Add niches for this wall
		for _, nicheId := range wall.NicheIds {
			wallIdStr := wall.LayoutId.String()
			roomLayout.Niches = append(roomLayout.Niches, Niche{
				ScalableElement: ScalableElement{
					BaseElement: BaseElement{
						Identifier:       nicheId.String(),
						ParentIdentifier: &wallIdStr,
					},
				},
			})
		}
	}

	for _, vanity := range entity.Vanities {
		roomLayout.Vanities = append(roomLayout.Vanities, Vanity{
			ScalableElement: ScalableElement{
				BaseElement: BaseElement{
					Identifier: vanity.LayoutId.String(),
				},
			},
		})
	}

	// Convert wet areas back to showers, tubs, and shower systems
	for _, wetArea := range entity.WetAreas {
		// Add shower area
		shower := Shower{
			BaseElement: BaseElement{
				Identifier: wetArea.LayoutId.String(),
			},
		}

		// Handle alcove tubs
		for _, alcoveTub := range wetArea.AlcoveTubs {
			// Add the tub
			tub := Tub{
				ScalableElement: ScalableElement{
					BaseElement: BaseElement{
						Identifier: alcoveTub.LayoutId.String(),
					},
				},
				Type: "Alcove",
			}
			roomLayout.Tubs = append(roomLayout.Tubs, tub)

			// Set shower parent to this tub
			tubIdStr := alcoveTub.LayoutId.String()
			shower.ParentIdentifier = &tubIdStr

			// Add shower system if present
			if alcoveTub.ShowerId != nil {
				showerSystem := ShowerSystem{
					ScalableElement: ScalableElement{
						BaseElement: BaseElement{
							Identifier: alcoveTub.ShowerId.String(),
						},
					},
					ShowerAreaIdentifier: wetArea.LayoutId.String(),
					Type:                 "TubAndShowerFaucet",
				}
				roomLayout.ShowerSystems = append(roomLayout.ShowerSystems, showerSystem)
			}
		}

		// Add freestanding tubs
		for _, tubId := range wetArea.FreestandingTubIds {
			tub := Tub{
				ScalableElement: ScalableElement{
					BaseElement: BaseElement{
						Identifier: tubId.String(),
					},
				},
				Type: "Freestanding",
			}
			roomLayout.Tubs = append(roomLayout.Tubs, tub)
		}

		// Add shower systems
		for _, systemId := range wetArea.ShowerIds {
			showerSystem := ShowerSystem{
				ScalableElement: ScalableElement{
					BaseElement: BaseElement{
						Identifier: systemId.String(),
					},
				},
				ShowerAreaIdentifier: wetArea.LayoutId.String(),
			}
			roomLayout.ShowerSystems = append(roomLayout.ShowerSystems, showerSystem)
		}

		// Only add shower if it has content or is standalone
		if len(wetArea.AlcoveTubs) > 0 || len(wetArea.ShowerIds) > 0 {
			roomLayout.Areas.Showers = append(roomLayout.Areas.Showers, shower)
		}
	}

	return roomLayout
}

func Uint64ToBytes(i uint64) []byte {
	var b [8]byte
	binary.LittleEndian.PutUint64(b[:], i)
	return b[:]
}
