package adapters_test

import (
	"encoding/json"
	"fmt"
	"net/url"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const jsonBlob = `{
	"id": "20",
    "style": "Modern",
    "color_scheme": "Bold",
    "name": "Modern Geometry",
    "description": "This modern bathroom design is inspired by the vibrant, bold geometries of Mexico", 
    "atmosphere": "geometric, bold, edgy",
    "color_palette": "black and white, matte black hardware, wood vanity",
    "material_palette": "handmade tile, geometric tile, oval elogated mirrors, dark wood vanity, matte black fixtures",
    "inspiration": "bold geometries of mexico",
    "plumbing_brand": "Delta",
    "lighting_brand": "Elegant Lighting",
    "vanity_brand": "James Martin Vanities",
    "toilet_brand": "Duravit",
    "vanity_storage": "wall mounted with drawers",
    "materials": {
        "id": "20",
        "tub": "",
        "tags": 20,
        "paint": "505c9c7c-4432-43c1-95fd-4650f8e4b775",
        "faucet": "",
        "mirror": "9ea7495d-4a33-4cc0-8832-d2b3545f0950",
        "toilet": "bed4b456-1058-4332-ac4b-ef40dcad5dfd",
        "vanity": "",
        "shelves": "33c3c4c9-4d57-4ae7-895b-00c80630da2c",
        "tubDoor": "",
        "lighting": "92fce210-b77d-49d6-8051-3b347917dc73",
        "robeHook": "97d986a6-d8f8-4f42-a04b-5f536f035304",
        "towelBar": "acd88e3b-3f58-4a33-8be0-86f6e8cb95a3",
        "tpHolder": "d8cfbb8b-c2a6-4bfd-a473-cf3a87ab4b1f",
        "wallTile": "135f1c09-8525-4e55-9997-cc263278ea72",
        "alcoveTub": "f976f7e6-da92-4236-9b53-ff3abc54d1eb",
        "floorTile": "ab07d14c-5b4e-44aa-96ba-1ccf3d901743",
        "towelRing": "be3e7f4d-ace2-443d-9bdc-a2601471d193",
        "tubFiller": "6608a61f-f005-410b-9efd-e6336e58a854",
        "faucetDict": {
            "24": "840eff26-6c5d-4467-b274-8c100b349cfd",
            "30": "045aef8d-0d33-4389-ac88-8a0635f611bd",
            "36": "045aef8d-0d33-4389-ac88-8a0635f611bd",
            "60": "045aef8d-0d33-4389-ac88-8a0635f611bd"
        },
        "vanityDict": {
            "24": "09d3ceb2-85fd-4e7b-86e2-3f9a97d9861f",
            "30": "9d8e170f-e0f1-4ba0-a609-e530b349195c",
            "36": "9de10ac3-166c-41fb-ae22-db8290e5ba6d",
            "60": "2a279059-d531-498d-8258-2171f1827160"
        },
        "showerGlass": "",
        "tubDoorFixed": "f0529d4f-5392-4ed1-91de-0b48ec31c14f",
        "showerWallTile": "135f1c09-8525-4e55-9997-cc263278ea72",
        "tubDoorSliding": "cd5af25c-5cb3-473e-ab7c-036fead0f041",
        "freestandingTub": "397d68a2-1c10-4bb4-96cc-145d0ef19e64",
        "showerFloorTile": "ab07d14c-5b4e-44aa-96ba-1ccf3d901743",
        "isTubDoorVisible": false,
        "showerGlassFixed": "ab32ea6f-a461-4f88-9910-4d13c722b951",
        "showerSystemFull": "8eee8bd7-21a9-4531-ae10-19af150a9980",
        "wallTilePlacement": "HalfWall",
        "showerGlassSliding": "197e7b0e-a554-4483-b956-e49dc80ef592",
        "showerSystemShower": "d16fcd40-cb89-4b9a-80af-ce294ac0f3e8",
        "wallpaperPlacement": "None",
        "isShowerGlassVisible": false
    },
    "render_priority": 1,
    "image_url": "https://cdn.arcstudio.ai/assets/Template20_ModernGeometry.webp",
    "highlighted_brand_urls": [
        "https://cdn.arcstudio.ai/assets/bedrosians-logo.pngImage preview",
        "https://cdn.arcstudio.ai/assets/behr-logo.pngImage preview",
        "https://cdn.arcstudio.ai/assets/kohler-logo-black.pngImage preview",
        "https://cdn.arcstudio.ai/assets/signature-hardware-logo.pngImage preview"
    ]
}`

func Test_FromJsonBlob(t *testing.T) {
	var template adapters.Template
	err := json.Unmarshal([]byte(jsonBlob), &template)
	require.NoError(t, err)
	assert.Equal(t, "20", template.ID)
}

func TestTemplate_AlignId(t *testing.T) {
	t.Run("should set ID when template ID is empty", func(t *testing.T) {
		template := adapters.Template{}
		expectedId := uuid.New()

		err := template.AlignId(expectedId.String())

		require.NoError(t, err)
		assert.Equal(t, expectedId.String(), template.ID)
	})

	t.Run("should set ID when template ID is nil", func(t *testing.T) {
		template := adapters.Template{ID: ""}
		expectedId := uuid.New()

		err := template.AlignId(expectedId.String())

		require.NoError(t, err)
		assert.Equal(t, expectedId.String(), template.ID)
	})

	t.Run("should succeed when template ID matches URL ID", func(t *testing.T) {
		templateId := uuid.New()
		template := adapters.Template{ID: templateId.String()}

		err := template.AlignId(templateId.String())

		require.NoError(t, err)
		assert.Equal(t, templateId.String(), template.ID)
	})

	t.Run("should return error when template ID does not match URL ID", func(t *testing.T) {
		templateId := uuid.New()
		differentId := uuid.New()
		template := adapters.Template{ID: templateId.String()}

		err := template.AlignId(differentId.String())

		require.Error(t, err)
		assert.Contains(t, err.Error(), "template ID mismatch")
		assert.Equal(t, templateId.String(), template.ID) // Should remain unchanged
	})

	t.Run("should return error for invalid UUID", func(t *testing.T) {
		template := adapters.Template{}

		err := template.AlignId("invalid-uuid")

		require.Error(t, err)
		assert.Contains(t, err.Error(), "invalid template UUID")
	})
}

func TestToUsecaseTemplate(t *testing.T) {
	templateId := uuid.New()
	vanityId1 := uuid.New()
	vanityId2 := uuid.New()
	faucetId1 := uuid.New()
	faucetId2 := uuid.New()

	t.Run("should convert valid template successfully", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:   templateId.String(),
			Name: "Test Template",
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.Modern,
			},
			Description:     "A test template",
			Inspiration:     "Test inspiration",
			Atmosphere:      "cozy, modern",
			ColorPalette:    "#FFFFFF, #000000",
			MaterialPalette: "wood, metal",
			Materials: adapters.Materials{
				VanityDict: map[string]uuid.UUID{
					"24": vanityId1,
					"36": vanityId2,
				},
				FaucetDict: map[string]uuid.UUID{
					"24": faucetId1,
					"36": faucetId2,
				},
			},
		}

		usecaseTemplate, err := adapters.ToUsecaseTemplate(adapterTemplate)

		require.NoError(t, err)
		assert.Equal(t, templateId, usecaseTemplate.ID)
		assert.Equal(t, "Test Template", usecaseTemplate.Name)
		assert.Equal(t, usecases.Neutral, usecaseTemplate.ColorScheme)
		assert.Equal(t, usecases.Modern, usecaseTemplate.Style)
		assert.Equal(t, "A test template", usecaseTemplate.Description)
		assert.Equal(t, []string{"cozy", "modern"}, usecaseTemplate.Atmosphere)

		// Check vanity scaling options
		assert.Len(t, usecaseTemplate.VanityScalingOptions, 2)
		assert.Equal(t, vanityId1, usecaseTemplate.VanityScalingOptions[24].VanityProductID)
		assert.Equal(t, faucetId1, usecaseTemplate.VanityScalingOptions[24].FaucetProductID)
		assert.Equal(t, vanityId2, usecaseTemplate.VanityScalingOptions[36].VanityProductID)
		assert.Equal(t, faucetId2, usecaseTemplate.VanityScalingOptions[36].FaucetProductID)
	})

	t.Run("should return error for empty ID", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:   "",
			Name: "Test Template",
		}

		_, err := adapters.ToUsecaseTemplate(adapterTemplate)

		require.Error(t, err)
		assert.Equal(t, usecases.ErrInvalidPayload, err)
	})

	t.Run("should return error for mismatched vanity/faucet dict lengths", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:   templateId.String(),
			Name: "Test Template",
			Materials: adapters.Materials{
				VanityDict: map[string]uuid.UUID{
					"24": vanityId1,
				},
				FaucetDict: map[string]uuid.UUID{
					"24": faucetId1,
					"36": faucetId2,
				},
			},
		}

		_, err := adapters.ToUsecaseTemplate(adapterTemplate)

		require.Error(t, err)
		assert.Equal(t, usecases.ErrInvalidPayload, err)
	})
}

func TestFromUsecaseTemplate(t *testing.T) {
	templateId := uuid.New()
	vanityId1 := uuid.New()
	vanityId2 := uuid.New()
	faucetId1 := uuid.New()
	faucetId2 := uuid.New()
	showerFloorTileId := uuid.New()

	t.Run("should convert usecase template successfully", func(t *testing.T) {
		usecaseTemplate := usecases.Template{
			ID:   templateId,
			Name: "Test Template",
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.Modern,
			},
			Description:     "A test template",
			Inspiration:     "Test inspiration",
			Atmosphere:      []string{"cozy", "modern"},
			ColorPalette:    []string{"#FFFFFF", "#000000"},
			MaterialPalette: []string{"wood", "metal"},
			FixedProductSelections: usecases.FixedProductSelections{
				ShowerFloorTile: &showerFloorTileId,
			},
			WallTilePlacement:  usecases.HalfWall,
			WallpaperPlacement: usecases.NoWallpaper,
			VanityScalingOptions: map[int]usecases.VanityScalingOption{
				24: {VanityProductID: vanityId1, FaucetProductID: faucetId1},
				36: {VanityProductID: vanityId2, FaucetProductID: faucetId2},
			},
		}

		adapterTemplate := adapters.FromUsecaseTemplate(usecaseTemplate, false)
		assert.Equal(t, templateId.String(), adapterTemplate.Materials.ID)

		assert.Equal(t, templateId.String(), adapterTemplate.ID)
		assert.Equal(t, "Test Template", adapterTemplate.Name)
		assert.Equal(t, usecases.Neutral, adapterTemplate.ColorScheme)
		assert.Equal(t, usecases.Modern, adapterTemplate.Style)
		assert.Equal(t, "A test template", adapterTemplate.Description)
		assert.Equal(t, "cozy, modern", adapterTemplate.Atmosphere)
		assert.Equal(t, showerFloorTileId, adapterTemplate.Materials.ShowerFloorTile)
		assert.Nil(t, adapterTemplate.Materials.WallTile)
		assert.Equal(t, usecases.HalfWall, adapterTemplate.Materials.WallTilePlacement)
		assert.Equal(t, usecases.NoWallpaper, adapterTemplate.Materials.WallpaperPlacement)

		// Check vanity/faucet dicts
		assert.Len(t, adapterTemplate.Materials.VanityDict, 2)
		assert.Len(t, adapterTemplate.Materials.FaucetDict, 2)
		assert.Equal(t, vanityId1, adapterTemplate.Materials.VanityDict["24"])
		assert.Equal(t, faucetId1, adapterTemplate.Materials.FaucetDict["24"])
		assert.Equal(t, vanityId2, adapterTemplate.Materials.VanityDict["36"])
		assert.Equal(t, faucetId2, adapterTemplate.Materials.FaucetDict["36"])
	})
}

func TestTemplate_ConversionRoundTrip(t *testing.T) {
	templateId := uuid.New()
	vanityId1 := uuid.New()
	faucetId1 := uuid.New()

	t.Run("should maintain data integrity through round trip conversion", func(t *testing.T) {
		originalTemplate := adapters.Template{
			ID:   templateId.String(),
			Name: "Test Template",
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.Modern,
			},
			Description:     "A test template",
			Inspiration:     "Test inspiration",
			Atmosphere:      "cozy, modern",
			ColorPalette:    "#FFFFFF, #000000",
			MaterialPalette: "wood, metal",
			Materials: adapters.Materials{
				VanityDict: map[string]uuid.UUID{
					"24": vanityId1,
				},
				FaucetDict: map[string]uuid.UUID{
					"24": faucetId1,
				},
			},
		}

		// Convert to usecase and back
		usecaseTemplate, err := adapters.ToUsecaseTemplate(originalTemplate)
		require.NoError(t, err)

		convertedBack := adapters.FromUsecaseTemplate(usecaseTemplate, false)

		// Verify key fields are preserved
		assert.Equal(t, originalTemplate.ID, convertedBack.ID)
		assert.Equal(t, originalTemplate.Name, convertedBack.Name)
		assert.Equal(t, originalTemplate.ColorScheme, convertedBack.ColorScheme)
		assert.Equal(t, originalTemplate.Style, convertedBack.Style)
		assert.Equal(t, originalTemplate.Description, convertedBack.Description)
		assert.Equal(t, originalTemplate.Inspiration, convertedBack.Inspiration)
		assert.Equal(t, originalTemplate.Atmosphere, convertedBack.Atmosphere)
		assert.Equal(t, originalTemplate.ColorPalette, convertedBack.ColorPalette)
		assert.Equal(t, originalTemplate.MaterialPalette, convertedBack.MaterialPalette)
		assert.Equal(t, len(originalTemplate.Materials.VanityDict), len(convertedBack.Materials.VanityDict))
		assert.Equal(t, len(originalTemplate.Materials.FaucetDict), len(convertedBack.Materials.FaucetDict))
		assert.Equal(t, originalTemplate.Materials.VanityDict["24"], convertedBack.Materials.VanityDict["24"])
		assert.Equal(t, originalTemplate.Materials.FaucetDict["24"], convertedBack.Materials.FaucetDict["24"])
	})
}

// TestTemplateConversions_ComprehensiveFieldMapping tests that all fields are properly converted
func TestTemplateConversions_ComprehensiveFieldMapping(t *testing.T) {
	templateId := uuid.New()
	legacyId := "AB"

	// Generate UUIDs for all materials
	paintId := uuid.New()
	mirrorId := uuid.New()
	toiletId := uuid.New()
	shelvesId := uuid.New()
	lightingId := uuid.New()
	floorTileId := uuid.New()
	tubFillerId := uuid.New()
	showerWallTileId := uuid.New()
	showerFloorTileId := uuid.New()
	alcoveTubId := uuid.New()
	freestandingTubId := uuid.New()
	showerGlassFixedId := uuid.New()
	showerGlassSlidingId := uuid.New()
	showerSystemFullId := uuid.New()
	showerSystemShowerId := uuid.New()
	tubDoorFixedId := uuid.New()
	tubDoorSlidingId := uuid.New()
	wallTileId := uuid.New()
	wallpaperId := uuid.New()
	vanityId1 := uuid.New()
	vanityId2 := uuid.New()
	faucetId1 := uuid.New()
	faucetId2 := uuid.New()

	t.Run("ToUsecaseTemplate should convert all fields correctly", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:       templateId.String(),
			LegacyId: &legacyId,
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Bold,
				Style:       usecases.Traditional,
			},
			Name:            "Comprehensive Test Template",
			ImageURL:        "https://example.com/image.jpg",
			Description:     "A comprehensive test template",
			Inspiration:     "Traditional design inspiration",
			Atmosphere:      "elegant, warm, classic",
			ColorPalette:    "cream, gold, burgundy",
			MaterialPalette: "marble, brass, mahogany",
			HighlightedBrandUrls: []string{
				"https://example.com/brand1",
				"https://example.com/brand2",
			},
			VanityStorage: "floating with soft-close drawers",
			VanityBrand:   "Custom Vanities Inc",
			LightingBrand: "Elegant Lighting Co",
			ToiletBrand:   "Premium Toilets",
			PlumbingBrand: "Quality Plumbing",
			Materials: adapters.Materials{
				ID:                 templateId.String(),
				Tags:               18, // Traditional (2) + Bold (16)
				Paint:              paintId,
				Mirror:             mirrorId,
				Toilet:             toiletId,
				Shelves:            shelvesId,
				Lighting:           lightingId,
				FloorTile:          floorTileId,
				TubFiller:          tubFillerId,
				ShowerWallTile:     showerWallTileId,
				ShowerFloorTile:    showerFloorTileId,
				AlcoveTub:          alcoveTubId,
				FreestandingTub:    freestandingTubId,
				ShowerGlassFixed:   showerGlassFixedId,
				ShowerGlassSliding: showerGlassSlidingId,
				ShowerSystemFull:   showerSystemFullId,
				ShowerSystemShower: showerSystemShowerId,
				TubDoorFixed:       tubDoorFixedId,
				TubDoorSliding:     tubDoorSlidingId,
				WallpaperPlacement: usecases.VanityWall,
				Wallpaper:          &wallpaperId,
				WallTilePlacement:  usecases.HalfWall,
				WallTile:           &wallTileId,
				FaucetDict: map[string]uuid.UUID{
					"24": faucetId1,
					"36": faucetId2,
				},
				VanityDict: map[string]uuid.UUID{
					"24": vanityId1,
					"36": vanityId2,
				},
			},
		}

		usecaseTemplate, err := adapters.ToUsecaseTemplate(adapterTemplate)
		require.NoError(t, err)

		// Verify basic fields
		assert.Equal(t, templateId, usecaseTemplate.ID)
		assert.Equal(t, &legacyId, usecaseTemplate.LegacyId)
		assert.Equal(t, usecases.Bold, usecaseTemplate.ColorScheme)
		assert.Equal(t, usecases.Traditional, usecaseTemplate.Style)
		assert.Equal(t, "Comprehensive Test Template", usecaseTemplate.Name)
		assert.Equal(t, "https://example.com/image.jpg", usecaseTemplate.ImageURL.String())
		assert.Equal(t, "A comprehensive test template", usecaseTemplate.Description)
		assert.Equal(t, "Traditional design inspiration", usecaseTemplate.Inspiration)

		// Verify array conversions
		assert.Equal(t, []string{"elegant", "warm", "classic"}, usecaseTemplate.Atmosphere)
		assert.Equal(t, []string{"cream", "gold", "burgundy"}, usecaseTemplate.ColorPalette)
		assert.Equal(t, []string{"marble", "brass", "mahogany"}, usecaseTemplate.MaterialPalette)

		// Verify highlighted brand URLs
		require.Len(t, usecaseTemplate.HighlightedBrandUrls, 2)
		assert.Equal(t, "https://example.com/brand1", usecaseTemplate.HighlightedBrandUrls[0].String())
		assert.Equal(t, "https://example.com/brand2", usecaseTemplate.HighlightedBrandUrls[1].String())

		// Verify fixed product selections
		assert.Equal(t, &paintId, usecaseTemplate.Paint)
		assert.Equal(t, &mirrorId, usecaseTemplate.Mirror)
		assert.Equal(t, &toiletId, usecaseTemplate.Toilet)
		assert.Equal(t, &shelvesId, usecaseTemplate.Shelving)
		assert.Equal(t, &lightingId, usecaseTemplate.Lighting)
		assert.Equal(t, &floorTileId, usecaseTemplate.FloorTile)

		// Verify product selection options
		assert.Equal(t, alcoveTubId, usecaseTemplate.AlcoveTub)
		assert.Equal(t, freestandingTubId, usecaseTemplate.FreestandingTub)
		assert.Equal(t, showerGlassFixedId, usecaseTemplate.ShowerGlassFixed)
		assert.Equal(t, showerGlassSlidingId, usecaseTemplate.ShowerGlassSliding)
		assert.Equal(t, showerSystemFullId, usecaseTemplate.ShowerSystemCombo)
		assert.Equal(t, showerSystemShowerId, usecaseTemplate.ShowerSystemSolo)
		assert.Equal(t, tubDoorFixedId, usecaseTemplate.TubDoorFixed)
		assert.Equal(t, tubDoorSlidingId, usecaseTemplate.TubDoorSliding)

		// Verify template provenance
		assert.Equal(t, "Quality Plumbing", *usecaseTemplate.PlumbingBrand)
		assert.Equal(t, "Elegant Lighting Co", *usecaseTemplate.LightingBrand)
		assert.Equal(t, "Custom Vanities Inc", *usecaseTemplate.VanityBrand)
		assert.Equal(t, "Premium Toilets", *usecaseTemplate.ToiletBrand)
		assert.Equal(t, "floating with soft-close drawers", *usecaseTemplate.VanityStorage)

		// Verify additional fields
		assert.Equal(t, &showerFloorTileId, usecaseTemplate.ShowerFloorTile)
		assert.Equal(t, &showerWallTileId, usecaseTemplate.ShowerWallTile)
		assert.Equal(t, &tubFillerId, usecaseTemplate.TubFiller)
		assert.Equal(t, usecases.HalfWall, usecaseTemplate.WallTilePlacement)
		assert.Equal(t, &wallTileId, usecaseTemplate.WallTile)
		assert.Equal(t, usecases.VanityWall, usecaseTemplate.WallpaperPlacement)
		assert.Equal(t, &wallpaperId, usecaseTemplate.Wallpaper)

		// Verify vanity scaling options
		require.Len(t, usecaseTemplate.VanityScalingOptions, 2)
		assert.Equal(t, vanityId1, usecaseTemplate.VanityScalingOptions[24].VanityProductID)
		assert.Equal(t, faucetId1, usecaseTemplate.VanityScalingOptions[24].FaucetProductID)
		assert.Equal(t, vanityId2, usecaseTemplate.VanityScalingOptions[36].VanityProductID)
		assert.Equal(t, faucetId2, usecaseTemplate.VanityScalingOptions[36].FaucetProductID)
	})

	t.Run("FromUsecaseTemplate should convert all fields correctly", func(t *testing.T) {
		// Create a comprehensive usecase template
		usecaseTemplate := usecases.Template{
			ID:       templateId,
			LegacyId: &legacyId,
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.MidCentury,
			},
			Name:            "Comprehensive Usecase Template",
			ImageURL:        mustParseURL("https://example.com/usecase-image.jpg"),
			Description:     "A comprehensive usecase template",
			Inspiration:     "Mid-century modern inspiration",
			Atmosphere:      []string{"retro", "sleek", "minimalist"},
			ColorPalette:    []string{"orange", "teal", "walnut"},
			MaterialPalette: []string{"teak", "chrome", "ceramic"},
			HighlightedBrandUrls: []url.URL{
				mustParseURL("https://example.com/usecase-brand1"),
				mustParseURL("https://example.com/usecase-brand2"),
			},
			FixedProductSelections: usecases.FixedProductSelections{
				Paint:           &paintId,
				Mirror:          &mirrorId,
				Toilet:          &toiletId,
				Shelving:        &shelvesId,
				Lighting:        &lightingId,
				FloorTile:       &floorTileId,
				ShowerFloorTile: &showerFloorTileId,
				ShowerWallTile:  &showerWallTileId,
				TubFiller:       &tubFillerId,
				WallTile:        &wallTileId,
				Wallpaper:       &wallpaperId,
			},
			ProductSelectionOptions: usecases.ProductSelectionOptions{
				AlcoveTub:          alcoveTubId,
				FreestandingTub:    freestandingTubId,
				ShowerGlassFixed:   showerGlassFixedId,
				ShowerGlassSliding: showerGlassSlidingId,
				ShowerSystemCombo:  showerSystemFullId,
				ShowerSystemSolo:   showerSystemShowerId,
				TubDoorFixed:       tubDoorFixedId,
				TubDoorSliding:     tubDoorSlidingId,
			},
			TemplateProvenance: usecases.TemplateProvenance{
				PlumbingBrand: stringPtr("Modern Plumbing"),
				LightingBrand: stringPtr("Retro Lighting"),
				VanityBrand:   stringPtr("Mid-Century Vanities"),
				ToiletBrand:   stringPtr("Designer Toilets"),
				VanityStorage: stringPtr("wall-mounted with hidden storage"),
			},
			WallTilePlacement:  usecases.FullWall,
			WallpaperPlacement: usecases.AllWalls,
			VanityScalingOptions: map[int]usecases.VanityScalingOption{
				24: {VanityProductID: vanityId1, FaucetProductID: faucetId1},
				36: {VanityProductID: vanityId2, FaucetProductID: faucetId2},
			},
		}

		adapterTemplate := adapters.FromUsecaseTemplate(usecaseTemplate, true)

		// Verify basic fields
		assert.Equal(t, legacyId, adapterTemplate.ID) // Should use legacy ID as primary ID
		// Note: FromUsecaseTemplate doesn't set LegacyId field in adapter template
		assert.Equal(t, usecases.Neutral, adapterTemplate.ColorScheme)
		assert.Equal(t, usecases.MidCentury, adapterTemplate.Style)
		assert.Equal(t, "Comprehensive Usecase Template", adapterTemplate.Name)
		assert.Equal(t, "https://example.com/usecase-image.jpg", adapterTemplate.ImageURL)
		assert.Equal(t, "A comprehensive usecase template", adapterTemplate.Description)
		assert.Equal(t, "Mid-century modern inspiration", adapterTemplate.Inspiration)

		// Verify string conversions
		assert.Equal(t, "retro, sleek, minimalist", adapterTemplate.Atmosphere)
		assert.Equal(t, "orange, teal, walnut", adapterTemplate.ColorPalette)
		assert.Equal(t, "teak, chrome, ceramic", adapterTemplate.MaterialPalette)

		// Verify highlighted brand URLs
		require.Len(t, adapterTemplate.HighlightedBrandUrls, 2)
		assert.Equal(t, "https://example.com/usecase-brand1", adapterTemplate.HighlightedBrandUrls[0])
		assert.Equal(t, "https://example.com/usecase-brand2", adapterTemplate.HighlightedBrandUrls[1])

		// Verify brand fields
		assert.Equal(t, "Modern Plumbing", adapterTemplate.PlumbingBrand)
		assert.Equal(t, "Retro Lighting", adapterTemplate.LightingBrand)
		assert.Equal(t, "Mid-Century Vanities", adapterTemplate.VanityBrand)
		assert.Equal(t, "Designer Toilets", adapterTemplate.ToiletBrand)
		assert.Equal(t, "wall-mounted with hidden storage", adapterTemplate.VanityStorage)

		// Verify materials structure
		assert.Equal(t, legacyId, adapterTemplate.Materials.ID)
		assert.Equal(t, int64(33), adapterTemplate.Materials.Tags) // MidCentury (1) + Neutral (32)
		assert.Equal(t, paintId, adapterTemplate.Materials.Paint)
		assert.Equal(t, mirrorId, adapterTemplate.Materials.Mirror)
		assert.Equal(t, toiletId, adapterTemplate.Materials.Toilet)
		assert.Equal(t, shelvesId, adapterTemplate.Materials.Shelves)
		assert.Equal(t, lightingId, adapterTemplate.Materials.Lighting)
		assert.Equal(t, floorTileId, adapterTemplate.Materials.FloorTile)
		assert.Equal(t, tubFillerId, adapterTemplate.Materials.TubFiller)
		assert.Equal(t, showerWallTileId, adapterTemplate.Materials.ShowerWallTile)
		assert.Equal(t, showerFloorTileId, adapterTemplate.Materials.ShowerFloorTile)
		assert.Equal(t, alcoveTubId, adapterTemplate.Materials.AlcoveTub)
		assert.Equal(t, freestandingTubId, adapterTemplate.Materials.FreestandingTub)
		assert.Equal(t, showerGlassFixedId, adapterTemplate.Materials.ShowerGlassFixed)
		assert.Equal(t, showerGlassSlidingId, adapterTemplate.Materials.ShowerGlassSliding)
		assert.Equal(t, showerSystemFullId, adapterTemplate.Materials.ShowerSystemFull)
		assert.Equal(t, showerSystemShowerId, adapterTemplate.Materials.ShowerSystemShower)
		assert.Equal(t, tubDoorFixedId, adapterTemplate.Materials.TubDoorFixed)
		assert.Equal(t, tubDoorSlidingId, adapterTemplate.Materials.TubDoorSliding)
		assert.Equal(t, usecases.FullWall, adapterTemplate.Materials.WallTilePlacement)
		assert.Equal(t, wallTileId, *adapterTemplate.Materials.WallTile)
		assert.Equal(t, usecases.AllWalls, adapterTemplate.Materials.WallpaperPlacement)
		assert.Equal(t, &wallpaperId, adapterTemplate.Materials.Wallpaper)

		// Verify vanity/faucet dicts
		require.Len(t, adapterTemplate.Materials.VanityDict, 2)
		require.Len(t, adapterTemplate.Materials.FaucetDict, 2)
		assert.Equal(t, vanityId1, adapterTemplate.Materials.VanityDict["24"])
		assert.Equal(t, faucetId1, adapterTemplate.Materials.FaucetDict["24"])
		assert.Equal(t, vanityId2, adapterTemplate.Materials.VanityDict["36"])
		assert.Equal(t, faucetId2, adapterTemplate.Materials.FaucetDict["36"])
	})
}

// TestTemplateConversions_EdgeCases tests edge cases and error conditions
func TestTemplateConversions_EdgeCases(t *testing.T) {
	templateId := uuid.New()

	t.Run("should handle empty string arrays gracefully", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:              templateId.String(),
			Name:            "Empty Arrays Template",
			Atmosphere:      "",
			ColorPalette:    "",
			MaterialPalette: "",
			Materials: adapters.Materials{
				VanityDict: map[string]uuid.UUID{},
				FaucetDict: map[string]uuid.UUID{},
			},
		}

		usecaseTemplate, err := adapters.ToUsecaseTemplate(adapterTemplate)
		require.NoError(t, err)

		assert.Empty(t, usecaseTemplate.Atmosphere)
		assert.Empty(t, usecaseTemplate.ColorPalette)
		assert.Empty(t, usecaseTemplate.MaterialPalette)
		assert.Empty(t, usecaseTemplate.VanityScalingOptions)
	})

	t.Run("should handle single item arrays", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:              templateId.String(),
			Name:            "Single Item Template",
			Atmosphere:      "cozy",
			ColorPalette:    "blue",
			MaterialPalette: "wood",
			Materials: adapters.Materials{
				VanityDict: map[string]uuid.UUID{},
				FaucetDict: map[string]uuid.UUID{},
			},
		}

		usecaseTemplate, err := adapters.ToUsecaseTemplate(adapterTemplate)
		require.NoError(t, err)

		assert.Equal(t, []string{"cozy"}, usecaseTemplate.Atmosphere)
		assert.Equal(t, []string{"blue"}, usecaseTemplate.ColorPalette)
		assert.Equal(t, []string{"wood"}, usecaseTemplate.MaterialPalette)
	})

	t.Run("should handle invalid vanity length keys", func(t *testing.T) {
		vanityId := uuid.New()
		faucetId := uuid.New()

		adapterTemplate := adapters.Template{
			ID:   templateId.String(),
			Name: "Invalid Keys Template",
			Materials: adapters.Materials{
				VanityDict: map[string]uuid.UUID{
					"24":      vanityId,
					"invalid": uuid.New(),
				},
				FaucetDict: map[string]uuid.UUID{
					"24":      faucetId,
					"invalid": uuid.New(),
				},
			},
		}

		usecaseTemplate, err := adapters.ToUsecaseTemplate(adapterTemplate)
		require.NoError(t, err)

		// Should only include valid numeric keys
		require.Len(t, usecaseTemplate.VanityScalingOptions, 1)
		assert.Equal(t, vanityId, usecaseTemplate.VanityScalingOptions[24].VanityProductID)
		assert.Equal(t, faucetId, usecaseTemplate.VanityScalingOptions[24].FaucetProductID)
	})

	t.Run("should handle invalid image URL gracefully", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:       templateId.String(),
			Name:     "Invalid URL Template",
			ImageURL: "://invalid-url",
			Materials: adapters.Materials{
				VanityDict: map[string]uuid.UUID{},
				FaucetDict: map[string]uuid.UUID{},
			},
		}

		_, err := adapters.ToUsecaseTemplate(adapterTemplate)
		require.Error(t, err)
		assert.Equal(t, usecases.ErrInvalidPayload, err)
	})

	t.Run("should handle invalid highlighted brand URLs", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:       templateId.String(),
			Name:     "Invalid Brand URLs Template",
			ImageURL: "https://example.com/valid.jpg",
			HighlightedBrandUrls: []string{
				"https://example.com/valid",
				"://invalid-url",
				"https://example.com/another-valid",
			},
			Materials: adapters.Materials{
				VanityDict: map[string]uuid.UUID{},
				FaucetDict: map[string]uuid.UUID{},
			},
		}

		usecaseTemplate, err := adapters.ToUsecaseTemplate(adapterTemplate)
		require.NoError(t, err)

		require.Len(t, usecaseTemplate.HighlightedBrandUrls, 2)
		assert.Equal(t, "https://example.com/valid", usecaseTemplate.HighlightedBrandUrls[0].String())
		assert.Equal(t, "https://example.com/another-valid", usecaseTemplate.HighlightedBrandUrls[1].String())
	})
}

// TestTemplateConversions_EnumValues tests all enum value combinations
func TestTemplateConversions_EnumValues(t *testing.T) {
	templateId := uuid.New()

	colorSchemes := []usecases.ColorScheme{usecases.Neutral, usecases.Bold}
	styles := []usecases.Style{usecases.Traditional, usecases.Transitional, usecases.MidCentury, usecases.Modern}
	wallpaperPlacements := []usecases.WallpaperPlacement{usecases.NoWallpaper, usecases.AllWalls, usecases.VanityWall}
	wallTilePlacements := []usecases.WallTilePlacement{usecases.NoWallTile, usecases.FullWall, usecases.HalfWall, usecases.VanityFullWall, usecases.VanityHalfWall}

	for _, colorScheme := range colorSchemes {
		for _, style := range styles {
			for _, wallpaperPlacement := range wallpaperPlacements {
				for _, wallTilePlacement := range wallTilePlacements {
					t.Run(fmt.Sprintf("should handle %s_%s_%s_%s", colorScheme, style, wallpaperPlacement, wallTilePlacement), func(t *testing.T) {
						adapterTemplate := adapters.Template{
							ID:   templateId.String(),
							Name: "Enum Test Template",
							Tagged: usecases.Tagged{
								ColorScheme: colorScheme,
								Style:       style,
							},
							ImageURL: "https://example.com/test.jpg",
							Materials: adapters.Materials{
								WallpaperPlacement: wallpaperPlacement,
								WallTilePlacement:  wallTilePlacement,
								VanityDict:         map[string]uuid.UUID{},
								FaucetDict:         map[string]uuid.UUID{},
							},
						}

						usecaseTemplate, err := adapters.ToUsecaseTemplate(adapterTemplate)
						require.NoError(t, err)

						assert.Equal(t, colorScheme, usecaseTemplate.ColorScheme)
						assert.Equal(t, style, usecaseTemplate.Style)
						assert.Equal(t, wallpaperPlacement, usecaseTemplate.WallpaperPlacement)
						assert.Equal(t, wallTilePlacement, usecaseTemplate.WallTilePlacement)

						// Test round-trip conversion
						convertedBack := adapters.FromUsecaseTemplate(usecaseTemplate, false)
						assert.Equal(t, colorScheme, convertedBack.ColorScheme)
						assert.Equal(t, style, convertedBack.Style)
						assert.Equal(t, wallpaperPlacement, convertedBack.Materials.WallpaperPlacement)
						assert.Equal(t, wallTilePlacement, convertedBack.Materials.WallTilePlacement)
					})
				}
			}
		}
	}
}

// Helper functions
func mustParseURL(urlStr string) url.URL {
	u, err := url.Parse(urlStr)
	if err != nil {
		panic(err)
	}
	return *u
}

func stringPtr(s string) *string {
	return &s
}

// TestTemplateConversions_TagsAndLegacyID tests tag conversion and legacy ID handling
func TestTemplateConversions_TagsAndLegacyID(t *testing.T) {
	templateId := uuid.New()

	t.Run("should handle legacy ID preference in FromUsecaseTemplate", func(t *testing.T) {
		legacyId := "XY"
		usecaseTemplate := usecases.Template{
			ID:       templateId,
			LegacyId: &legacyId,
			Name:     "Legacy ID Template",
			ImageURL: mustParseURL("https://example.com/test.jpg"),
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Bold,
				Style:       usecases.Modern,
			},
			VanityScalingOptions: map[int]usecases.VanityScalingOption{},
		}

		adapterTemplate := adapters.FromUsecaseTemplate(usecaseTemplate, true)

		// Should use legacy ID as primary ID
		assert.Equal(t, legacyId, adapterTemplate.ID)
		// Note: FromUsecaseTemplate doesn't set LegacyId field in adapter template
		assert.Equal(t, legacyId, adapterTemplate.Materials.ID)
	})

	t.Run("should fall back to UUID when no legacy ID", func(t *testing.T) {
		usecaseTemplate := usecases.Template{
			ID:       templateId,
			LegacyId: nil,
			Name:     "No Legacy ID Template",
			ImageURL: mustParseURL("https://example.com/test.jpg"),
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.Traditional,
			},
			VanityScalingOptions: map[int]usecases.VanityScalingOption{},
		}

		adapterTemplate := adapters.FromUsecaseTemplate(usecaseTemplate, false)

		// Should use UUID as primary ID
		assert.Equal(t, templateId.String(), adapterTemplate.ID)
		// Note: FromUsecaseTemplate doesn't set LegacyId field in adapter template
		assert.Equal(t, templateId.String(), adapterTemplate.Materials.ID)
	})

	t.Run("should calculate tags correctly for all combinations", func(t *testing.T) {
		testCases := []struct {
			colorScheme usecases.ColorScheme
			style       usecases.Style
			expectedTag int64
		}{
			{usecases.Neutral, usecases.MidCentury, 33},   // 32 + 1
			{usecases.Neutral, usecases.Traditional, 34},  // 32 + 2
			{usecases.Neutral, usecases.Modern, 36},       // 32 + 4
			{usecases.Neutral, usecases.Transitional, 40}, // 32 + 8
			{usecases.Bold, usecases.MidCentury, 17},      // 16 + 1
			{usecases.Bold, usecases.Traditional, 18},     // 16 + 2
			{usecases.Bold, usecases.Modern, 20},          // 16 + 4
			{usecases.Bold, usecases.Transitional, 24},    // 16 + 8
		}

		for _, tc := range testCases {
			t.Run(fmt.Sprintf("%s_%s", tc.colorScheme, tc.style), func(t *testing.T) {
				usecaseTemplate := usecases.Template{
					ID:       templateId,
					Name:     "Tags Test Template",
					ImageURL: mustParseURL("https://example.com/test.jpg"),
					Tagged: usecases.Tagged{
						ColorScheme: tc.colorScheme,
						Style:       tc.style,
					},
					VanityScalingOptions: map[int]usecases.VanityScalingOption{},
				}

				adapterTemplate := adapters.FromUsecaseTemplate(usecaseTemplate, false)
				assert.Equal(t, tc.expectedTag, adapterTemplate.Materials.Tags)
			})
		}
	})
}

// TestTemplateConversions_NilPointerHandling tests nil pointer handling
func TestTemplateConversions_NilPointerHandling(t *testing.T) {
	templateId := uuid.New()

	t.Run("should handle nil pointers in FromUsecaseTemplate", func(t *testing.T) {
		usecaseTemplate := usecases.Template{
			ID:       templateId,
			Name:     "Nil Pointers Template",
			ImageURL: mustParseURL("https://example.com/test.jpg"),
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.Modern,
			},
			FixedProductSelections: usecases.FixedProductSelections{
				Paint:           nil,
				Mirror:          nil,
				Toilet:          nil,
				Shelving:        nil,
				Lighting:        nil,
				FloorTile:       nil,
				ShowerFloorTile: nil,
				ShowerWallTile:  nil,
				TubFiller:       nil,
				WallTile:        nil,
				Wallpaper:       nil,
			},
			TemplateProvenance: usecases.TemplateProvenance{
				PlumbingBrand: nil,
				LightingBrand: nil,
				VanityBrand:   nil,
				ToiletBrand:   nil,
				VanityStorage: nil,
			},
			VanityScalingOptions: map[int]usecases.VanityScalingOption{},
		}

		adapterTemplate := adapters.FromUsecaseTemplate(usecaseTemplate, false)

		// Should convert nil pointers to zero values
		assert.Equal(t, uuid.Nil, adapterTemplate.Materials.Paint)
		assert.Equal(t, uuid.Nil, adapterTemplate.Materials.Mirror)
		assert.Equal(t, uuid.Nil, adapterTemplate.Materials.Toilet)
		assert.Equal(t, uuid.Nil, adapterTemplate.Materials.Shelves)
		assert.Equal(t, uuid.Nil, adapterTemplate.Materials.Lighting)
		assert.Equal(t, uuid.Nil, adapterTemplate.Materials.FloorTile)
		assert.Equal(t, uuid.Nil, adapterTemplate.Materials.ShowerFloorTile)
		assert.Equal(t, uuid.Nil, adapterTemplate.Materials.ShowerWallTile)
		assert.Equal(t, uuid.Nil, adapterTemplate.Materials.TubFiller)
		assert.Nil(t, adapterTemplate.Materials.WallTile)
		assert.Nil(t, adapterTemplate.Materials.Wallpaper)

		// Should convert nil string pointers to empty strings
		assert.Equal(t, "", adapterTemplate.PlumbingBrand)
		assert.Equal(t, "", adapterTemplate.LightingBrand)
		assert.Equal(t, "", adapterTemplate.VanityBrand)
		assert.Equal(t, "", adapterTemplate.ToiletBrand)
		assert.Equal(t, "", adapterTemplate.VanityStorage)
	})
}
