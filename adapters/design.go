package adapters

import (
	"database/sql"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// CartInclusion is an alias to the usecase type since they're identical
type CartInclusion = usecases.CartInclusion

// CartInclusionKey is an alias to the usecase type since they're identical
type CartInclusionKey = usecases.CartInclusionKey

// CartInclusions is an alias to the usecase type since they're identical
type CartInclusions = usecases.CartInclusions

type Design struct {
	ID   string `json:"id"`
	Tags int64  `json:"tags"`

	Status      usecases.DesignStatus `json:"status,omitempty"`
	ColorScheme *usecases.ColorScheme `json:"colorScheme,omitempty"`
	Style       *usecases.Style       `json:"style,omitempty"`

	LastUpdatedDateTime *string `json:"lastUpdatedDateTime,omitempty"`
	Title               *string `json:"title,omitempty"`
	Description         *string `json:"description,omitempty"`

	SKUCount     *int32 `json:"skuCount,omitempty"`
	TotalPrice   *int32 `json:"totalPrice,omitempty"`
	LeadTimeDays *int32 `json:"leadTimeDays,omitempty"`

	FloorTile *string `json:"floorTile,omitempty"`
	Toilet    *string `json:"toilet,omitempty"`
	Vanity    *string `json:"vanity,omitempty"`
	Faucet    *string `json:"faucet,omitempty"`
	Mirror    *string `json:"mirror,omitempty"`

	FloorTilePattern       *usecases.TilePattern        `json:"floorTilePattern,omitempty"`
	Lighting               *string                      `json:"lighting,omitempty"`
	NicheTile              *string                      `json:"nicheTile,omitempty"`
	Paint                  *string                      `json:"paint,omitempty"`
	Shelves                *string                      `json:"shelves,omitempty"`
	ShowerFloorTile        *string                      `json:"showerFloorTile,omitempty"`
	ShowerFloorTilePattern *usecases.TilePattern        `json:"showerFloorTilePattern,omitempty"`
	ShowerSystem           *string                      `json:"showerSystem,omitempty"`
	ShowerWallTile         *string                      `json:"showerWallTile,omitempty"`
	ShowerWallTilePattern  *usecases.TilePattern        `json:"showerWallTilePattern,omitempty"`
	ShowerShortWallTile    *string                      `json:"showerShortWallTile,omitempty"`
	ShowerGlass            *string                      `json:"showerGlass,omitempty"`
	Tub                    *string                      `json:"tub,omitempty"`
	TubDoor                *string                      `json:"tubDoor,omitempty"`
	TubFiller              *string                      `json:"tubFiller,omitempty"`
	Wallpaper              *string                      `json:"wallpaper,omitempty"`
	WallpaperPlacement     *usecases.WallpaperPlacement `json:"wallpaperPlacement,omitempty"`
	WallTile               *string                      `json:"wallTile,omitempty"`
	WallTilePlacement      *usecases.WallTilePlacement  `json:"wallTilePlacement,omitempty"`
	WallTilePattern        *usecases.TilePattern        `json:"wallTilePattern,omitempty"`

	IsShowerGlassVisible *bool `json:"isShowerGlassVisible,omitempty"`
	IsTubDoorVisible     *bool `json:"isTubDoorVisible,omitempty"`
	IsNichesVisible      *bool `json:"isNichesVisible,omitempty"`

	Renditions []Rendition `json:"renditions,omitempty"`
}

func (d *Design) AlignId(id string) error {
	if d.ID == "" {
		d.ID = id
	} else if d.ID != id {
		return errors.New("design ID mismatch")
	}
	return nil
}

func MergeDesigns(existing, incoming Design) Design {
	if incoming.Title != nil {
		existing.Title = incoming.Title
	}
	if incoming.Description != nil {
		existing.Description = incoming.Description
	}
	if incoming.SKUCount != nil {
		existing.SKUCount = incoming.SKUCount
	}
	if incoming.TotalPrice != nil {
		existing.TotalPrice = incoming.TotalPrice
	}
	if incoming.LeadTimeDays != nil {
		existing.LeadTimeDays = incoming.LeadTimeDays
	}
	if incoming.FloorTile != nil {
		existing.FloorTile = incoming.FloorTile
	}
	if incoming.Toilet != nil {
		existing.Toilet = incoming.Toilet
	}
	if incoming.Vanity != nil {
		existing.Vanity = incoming.Vanity
	}
	if incoming.Faucet != nil {
		existing.Faucet = incoming.Faucet
	}
	if incoming.Mirror != nil {
		existing.Mirror = incoming.Mirror
	}
	if incoming.FloorTilePattern != nil {
		existing.FloorTilePattern = incoming.FloorTilePattern
	}
	if incoming.Lighting != nil {
		existing.Lighting = incoming.Lighting
	}
	if incoming.NicheTile != nil {
		existing.NicheTile = incoming.NicheTile
	}
	if incoming.Paint != nil {
		existing.Paint = incoming.Paint
	}
	if incoming.Shelves != nil {
		existing.Shelves = incoming.Shelves
	}
	if incoming.ShowerFloorTile != nil {
		existing.ShowerFloorTile = incoming.ShowerFloorTile
	}
	if incoming.ShowerFloorTilePattern != nil {
		existing.ShowerFloorTilePattern = incoming.ShowerFloorTilePattern
	}
	if incoming.ShowerSystem != nil {
		existing.ShowerSystem = incoming.ShowerSystem
	}
	if incoming.ShowerWallTile != nil {
		existing.ShowerWallTile = incoming.ShowerWallTile
	}
	if incoming.ShowerWallTilePattern != nil {
		existing.ShowerWallTilePattern = incoming.ShowerWallTilePattern
	}
	if incoming.ShowerShortWallTile != nil {
		existing.ShowerShortWallTile = incoming.ShowerShortWallTile
	}
	if incoming.ShowerGlass != nil {
		existing.ShowerGlass = incoming.ShowerGlass
	}
	if incoming.Tub != nil {
		existing.Tub = incoming.Tub
	}
	if incoming.TubDoor != nil {
		existing.TubDoor = incoming.TubDoor
	}
	if incoming.TubFiller != nil {
		existing.TubFiller = incoming.TubFiller
	}
	if incoming.Wallpaper != nil {
		existing.Wallpaper = incoming.Wallpaper
	}
	if incoming.WallpaperPlacement != nil {
		existing.WallpaperPlacement = incoming.WallpaperPlacement
	}
	if incoming.WallTile != nil {
		existing.WallTile = incoming.WallTile
	}
	if incoming.WallTilePlacement != nil {
		existing.WallTilePlacement = incoming.WallTilePlacement
	} else if (existing.WallTilePlacement == nil || *existing.WallTilePlacement == usecases.NoWallTile) && existing.WallTile != nil {
		// TODO: consider moving this into a use-case.
		defaultWallTilePlacement := usecases.VanityHalfWall
		existing.WallTilePlacement = &defaultWallTilePlacement
	}
	if incoming.WallTilePattern != nil {
		existing.WallTilePattern = incoming.WallTilePattern
	}
	if incoming.IsShowerGlassVisible != nil {
		existing.IsShowerGlassVisible = incoming.IsShowerGlassVisible
	}
	if incoming.IsTubDoorVisible != nil {
		existing.IsTubDoorVisible = incoming.IsTubDoorVisible
	}
	if incoming.IsNichesVisible != nil {
		existing.IsNichesVisible = incoming.IsNichesVisible
	}
	if incoming.Tags != 0 {
		existing.Tags = incoming.Tags
	} else {
		if incoming.Status != "" {
			existing.Status = incoming.Status
		}
		if incoming.ColorScheme != nil {
			existing.ColorScheme = incoming.ColorScheme
		}
		if incoming.Style != nil {
			existing.Style = incoming.Style
		}
	}
	return existing
}

//------------------------------------------------------------------------------
// Conversion Functions
//------------------------------------------------------------------------------

// Converts a usecases.Design domain entity to a Design API model.
func FromUsecaseDesign(d usecases.Design) Design {
	design := Design{
		ID:                     d.ID.String(),
		Tags:                   tagsFromDomain(d),
		Status:                 d.Status,
		ColorScheme:            d.ColorScheme,
		Style:                  d.Style,
		Title:                  sqlNullStringToStringPtr(d.Title),
		Description:            sqlNullStringToStringPtr(d.Description),
		SKUCount:               sqlNullInt32ToInt32Ptr(d.NumSKUs),
		TotalPrice:             sqlNullInt32ToInt32Ptr(d.TotalPriceInCents),
		LeadTimeDays:           sqlNullInt32ToInt32Ptr(d.LeadTimeDays),
		FloorTile:              uuidPtrToStringPtr(d.FloorTile),
		Toilet:                 uuidPtrToStringPtr(d.Toilet),
		Vanity:                 uuidPtrToStringPtr(d.Vanity),
		Faucet:                 uuidPtrToStringPtr(d.Faucet),
		Mirror:                 uuidPtrToStringPtr(d.Mirror),
		FloorTilePattern:       d.FloorTilePattern,
		Lighting:               uuidPtrToStringPtr(d.Lighting),
		NicheTile:              uuidPtrToStringPtr(d.NicheTile),
		Paint:                  uuidPtrToStringPtr(d.Paint),
		Shelves:                uuidPtrToStringPtr(d.Shelving),
		ShowerFloorTile:        uuidPtrToStringPtr(d.ShowerFloorTile),
		ShowerFloorTilePattern: d.ShowerFloorTilePattern,
		ShowerSystem:           uuidPtrToStringPtr(d.ShowerSystem),
		ShowerWallTile:         uuidPtrToStringPtr(d.ShowerWallTile),
		ShowerWallTilePattern:  d.ShowerWallTilePattern,
		ShowerShortWallTile:    uuidPtrToStringPtr(d.ShowerShortWallTile),
		ShowerGlass:            uuidPtrToStringPtr(d.ShowerGlass),
		Tub:                    uuidPtrToStringPtr(d.Tub),
		TubDoor:                uuidPtrToStringPtr(d.TubDoor),
		TubFiller:              uuidPtrToStringPtr(d.TubFiller),
		Wallpaper:              uuidPtrToStringPtr(d.Wallpaper),
		WallpaperPlacement:     enumToEnumPtr(d.WallpaperPlacement),
		WallTile:               uuidPtrToStringPtr(d.WallTile),
		WallTilePlacement:      enumToEnumPtr(d.WallTilePlacement),
		WallTilePattern:        d.WallTilePattern,
		IsShowerGlassVisible:   boolToBoolPtrIfTrue(d.ShowerGlassVisible),
		IsTubDoorVisible:       boolToBoolPtrIfTrue(d.TubDoorVisible),
		IsNichesVisible:        boolToBoolPtrIfTrue(d.NichesVisible),
	}
	for _, r := range d.Renditions {
		design.Renditions = append(design.Renditions, FromDomainRendition(r))
	}
	if !d.LastUpdated.IsZero() {
		lastUpdated := d.LastUpdated.Format(time.RFC3339)
		design.LastUpdatedDateTime = &lastUpdated
	}
	return design
}

func (sd Design) setUsecaseDesignOptions(do *usecases.DesignOptions) error {
	var err error

	do.Title = stringPtrToSQLNullString(sd.Title)
	do.Description = stringPtrToSQLNullString(sd.Description)

	if sd.ColorScheme != nil {
		do.ColorScheme = sd.ColorScheme
	}
	if sd.Style != nil {
		do.Style = sd.Style
	}

	do.Faucet, err = stringPtrToUUIDPtr(sd.Faucet, "Faucet", err)
	do.FloorTile, err = stringPtrToUUIDPtr(sd.FloorTile, "FloorTile", err)
	do.Mirror, err = stringPtrToUUIDPtr(sd.Mirror, "Mirror", err)
	do.Toilet, err = stringPtrToUUIDPtr(sd.Toilet, "Toilet", err)
	do.Vanity, err = stringPtrToUUIDPtr(sd.Vanity, "Vanity", err)
	do.Lighting, err = stringPtrToUUIDPtr(sd.Lighting, "Lighting", err)
	do.Paint, err = stringPtrToUUIDPtr(sd.Paint, "Paint", err)
	do.Shelving, err = stringPtrToUUIDPtr(sd.Shelves, "Shelves", err)
	do.ShowerFloorTile, err = stringPtrToUUIDPtr(sd.ShowerFloorTile, "ShowerFloorTile", err)
	do.ShowerSystem, err = stringPtrToUUIDPtr(sd.ShowerSystem, "ShowerSystem", err)
	do.ShowerWallTile, err = stringPtrToUUIDPtr(sd.ShowerWallTile, "ShowerWallTile", err)
	do.ShowerShortWallTile, err = stringPtrToUUIDPtr(sd.ShowerShortWallTile, "ShowerShortWallTile", err)
	do.ShowerGlass, err = stringPtrToUUIDPtr(sd.ShowerGlass, "ShowerGlass", err)
	do.NicheTile, err = stringPtrToUUIDPtr(sd.NicheTile, "NicheTile", err)
	do.Tub, err = stringPtrToUUIDPtr(sd.Tub, "Tub", err)
	do.TubDoor, err = stringPtrToUUIDPtr(sd.TubDoor, "TubDoor", err)
	do.TubFiller, err = stringPtrToUUIDPtr(sd.TubFiller, "TubFiller", err)
	do.Wallpaper, err = stringPtrToUUIDPtr(sd.Wallpaper, "Wallpaper", err)
	do.WallTile, err = stringPtrToUUIDPtr(sd.WallTile, "WallTile", err)

	do.NumSKUs = int32PtrToSQLNullInt32(sd.SKUCount)
	do.TotalPriceInCents = int32PtrToSQLNullInt32(sd.TotalPrice)
	do.LeadTimeDays = int32PtrToSQLNullInt32(sd.LeadTimeDays)

	return err
}

// ToUsecaseDesign converts a Design API model to a usecases.Design domain entity.
// It returns an error if any UUID string is invalid.
func (sd Design) ToUsecaseDesign(projectId entities.ProjectId) (usecases.Design, error) {
	d := usecases.NewDesign(nil, &projectId)
	var err error

	if d.ID, err = uuid.Parse(sd.ID); err != nil {
		return d, fmt.Errorf("invalid design ID: %w", err)
	}

	if sd.Tags != 0 {
		d.Status = statusFromTags(sd.Tags)
		d.ColorScheme = colorSchemeFromTags(sd.Tags)
		d.Style = styleFromTags(sd.Tags)
	} else {
		d.Status = sd.Status
		d.ColorScheme = sd.ColorScheme
		d.Style = sd.Style
	}

	if sd.LastUpdatedDateTime != nil && *sd.LastUpdatedDateTime != "" {
		d.LastUpdated, err = time.Parse(time.RFC3339, *sd.LastUpdatedDateTime)
		if err != nil {
			return d, fmt.Errorf("invalid last updated date time: %w", err)
		}
	}
	if err = sd.setUsecaseDesignOptions(&d.DesignOptions); err != nil {
		return d, err
	}

	if sd.WallpaperPlacement != nil {
		d.WallpaperPlacement = *sd.WallpaperPlacement
	} else {
		d.WallpaperPlacement = usecases.VanityWall
	}
	// TODO: consider moving this into a use-case.
	if sd.WallTilePlacement != nil {
		if *sd.WallTilePlacement != usecases.NoWallTile || d.WallTile == nil {
			d.WallTilePlacement = *sd.WallTilePlacement
		} else {
			d.WallTilePlacement = usecases.VanityHalfWall
		}
	} else {
		if d.WallTile != nil {
			d.WallTilePlacement = usecases.VanityHalfWall
		} else {
			d.WallTilePlacement = usecases.NoWallTile
		}
	}
	if sd.FloorTilePattern == nil {
		defaultFloorTilePattern := usecases.HorizontalStacked
		d.FloorTilePattern = &defaultFloorTilePattern
	} else {
		d.FloorTilePattern = canonicalizeTilePattern(sd.FloorTilePattern)
	}
	d.WallTilePattern = canonicalizeTilePattern(sd.WallTilePattern)
	d.ShowerFloorTilePattern = canonicalizeTilePattern(sd.ShowerFloorTilePattern)
	d.ShowerWallTilePattern = canonicalizeTilePattern(sd.ShowerWallTilePattern)

	if sd.IsShowerGlassVisible != nil {
		d.ShowerGlassVisible = *sd.IsShowerGlassVisible
	}
	if sd.IsTubDoorVisible != nil {
		d.TubDoorVisible = *sd.IsTubDoorVisible
	}
	if sd.IsNichesVisible != nil {
		d.NichesVisible = *sd.IsNichesVisible
	}

	// Initialize CartInclusions as empty map to maintain consistency
	d.CartInclusions = make(usecases.CartInclusions)

	return d, nil
}

// ToUsecaseDesignDiff converts a Design API model to a usecases.DesignDiff entity.
// It returns an error if any UUID string is invalid.
func (sd Design) ToUsecaseDesignDiff() (usecases.DesignDiff, error) {
	d := usecases.DesignDiff{}
	var err error

	if d.ID, err = uuid.Parse(sd.ID); err != nil {
		return d, fmt.Errorf("invalid design ID: %w", err)
	}
	if err := sd.setUsecaseDesignOptions(&d.DesignOptions); err != nil {
		return d, err
	}

	if sd.Tags != 0 {
		status := statusFromTags(sd.Tags)
		if status != "" {
			d.Status = &status
		}
		colorScheme := colorSchemeFromTags(sd.Tags)
		if colorScheme != nil {
			d.ColorScheme = colorScheme
		}
		style := styleFromTags(sd.Tags)
		if style != nil {
			d.Style = style
		}
	} else {
		if sd.Status != "" {
			d.Status = &sd.Status
		}
		if sd.ColorScheme != nil {
			d.ColorScheme = sd.ColorScheme
		}
		if sd.Style != nil {
			d.Style = sd.Style
		}
	}

	if sd.WallpaperPlacement != nil {
		d.WallpaperPlacement = sd.WallpaperPlacement
	}
	// TODO: consider moving this into a use-case.
	defaultWallTilePlacement := usecases.VanityHalfWall
	if sd.WallTilePlacement != nil {
		if *sd.WallTilePlacement != usecases.NoWallTile || d.WallTile == nil {
			d.WallTilePlacement = sd.WallTilePlacement
		} else {
			d.WallTilePlacement = &defaultWallTilePlacement
		}
	}

	if sd.WallTilePattern != nil {
		d.WallTilePattern = sd.WallTilePattern
	}
	if sd.ShowerWallTilePattern != nil {
		d.ShowerWallTilePattern = sd.ShowerWallTilePattern
	}
	if sd.ShowerFloorTilePattern != nil {
		d.ShowerFloorTilePattern = sd.ShowerFloorTilePattern
	}
	if sd.FloorTilePattern != nil {
		d.FloorTilePattern = sd.FloorTilePattern
	}

	if sd.IsShowerGlassVisible != nil {
		d.ShowerGlassVisible = sql.NullBool{Bool: *sd.IsShowerGlassVisible, Valid: true}
	}
	if sd.IsTubDoorVisible != nil {
		d.TubDoorVisible = sql.NullBool{Bool: *sd.IsTubDoorVisible, Valid: true}
	}
	if sd.IsNichesVisible != nil {
		d.NichesVisible = sql.NullBool{Bool: *sd.IsNichesVisible, Valid: true}
	}

	d.Removals = map[string]any{}
	return d, nil
}

// ConvertCartInclusionsArrayToMap converts an array of cart inclusions to a map with composite keys.
// This is a helper function to avoid code duplication in HTTP handlers.
func ConvertCartInclusionsArrayToMap(inclusionsArray []CartInclusion) CartInclusions {
	inclusions := make(CartInclusions, len(inclusionsArray))
	for _, inclusion := range inclusionsArray {
		key := CartInclusionKey{
			ProductID: inclusion.ProductID,
			Location:  inclusion.Location,
		}
		inclusions[key] = inclusion
	}
	return inclusions
}

//------------------------------------------------------------------------------
// Helper Functions
//------------------------------------------------------------------------------

func tagsFromDomain(d usecases.Design) int64 {
	tags := int64(0)
	switch d.Status {
	case usecases.Fave:
		tags |= 1073741824
	case usecases.Archived:
		tags |= 536870912
	}
	if d.ColorScheme != nil {
		switch *d.ColorScheme {
		case usecases.Neutral:
			tags |= 32
		case usecases.Bold:
			tags |= 16
		}
	}
	if d.Style != nil {
		switch *d.Style {
		case usecases.MidCentury:
			tags |= 1
		case usecases.Traditional:
			tags |= 2
		case usecases.Modern:
			tags |= 4
		case usecases.Transitional:
			tags |= 8
		}
	}
	return tags
}

func statusFromTags(tags int64) usecases.DesignStatus {
	if tags&1073741824 != 0 {
		return usecases.Fave
	}
	if tags&536870912 != 0 {
		return usecases.Archived
	}
	return ""
}

func colorSchemeFromTags(tags int64) *usecases.ColorScheme {
	if tags&32 != 0 {
		cs := usecases.Neutral
		return &cs
	}
	if tags&16 != 0 {
		cs := usecases.Bold
		return &cs
	}
	return nil
}

func styleFromTags(tags int64) *usecases.Style {
	if tags&1 != 0 {
		s := usecases.MidCentury
		return &s
	}
	if tags&2 != 0 {
		s := usecases.Traditional
		return &s
	}
	if tags&4 != 0 {
		s := usecases.Modern
		return &s
	}
	if tags&8 != 0 {
		s := usecases.Transitional
		return &s
	}
	return nil
}

func uuidPtrToStringPtr(id *uuid.UUID) *string {
	if id == nil {
		return nil
	}
	s := id.String()
	return &s
}

func sqlNullStringToStringPtr(ns sql.NullString) *string {
	if !ns.Valid {
		return nil
	}
	return &ns.String
}

func sqlNullInt32ToInt32Ptr(ni sql.NullInt32) *int32 {
	if !ni.Valid {
		return nil
	}
	return &ni.Int32
}

func enumToEnumPtr[E ~string](e E) *E {
	// Check if the enum has its zero value, assuming it means not set.
	var zero E
	if e == zero {
		return nil
	}
	return &e
}

func stringPtrToSQLNullString(s *string) sql.NullString {
	if s == nil {
		return sql.NullString{}
	}
	return sql.NullString{String: *s, Valid: true}
}

func int32PtrToSQLNullInt32(i *int32) sql.NullInt32 {
	if i == nil {
		return sql.NullInt32{}
	}
	return sql.NullInt32{Int32: *i, Valid: true}
}

func stringPtrToUUIDPtr(s *string, fieldName string, existingErr error) (*uuid.UUID, error) {
	if existingErr != nil {
		return nil, existingErr
	}
	if s == nil || *s == "" {
		return nil, nil
	}
	id, err := uuid.Parse(*s)
	if err != nil {
		return nil, fmt.Errorf("invalid UUID for %s (%s): %w", fieldName, *s, err)
	}
	return &id, nil
}

func canonicalizeTilePattern(tp *usecases.TilePattern) *usecases.TilePattern {
	if tp == nil {
		return nil
	}
	switch *tp {
	case usecases.HorizontalStacked, usecases.VerticalStacked, usecases.HalfOffset, usecases.ThirdOffset, usecases.Herringbone:
		return tp
	case "HorizontalStack":
		*tp = usecases.HorizontalStacked
		return tp
	case "VerticalStack":
		*tp = usecases.VerticalStacked
		return tp
	default:
		log.Panicln("Invalid tile pattern:", *tp)
	}
	return nil
}

func boolToBoolPtrIfTrue(b bool) *bool {
	if b {
		return &b
	}
	return nil
}
