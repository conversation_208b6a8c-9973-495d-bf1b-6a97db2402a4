package adapters

import "gitlab.com/arc-studio-ai/services/room-design/usecases"

// CartInclusion is an alias to the usecase type since they're identical
type CartInclusion = usecases.CartInclusion

// CartInclusionKey is an alias to the usecase type since they're identical
type CartInclusionKey = usecases.CartInclusionKey

// CartInclusions is an alias to the usecase type since they're identical
type CartInclusions = usecases.CartInclusions
