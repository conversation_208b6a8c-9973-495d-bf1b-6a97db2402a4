package adapters_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestFilterParamsToUsecase(t *testing.T) {
	t.Run("minimal conversion", func(t *testing.T) {
		filterParams := adapters.FilterParams{
			Category: usecases.CategoryVanity,
		}

		result := filterParams.ToUsecase()

		assert.Equal(t, usecases.CategoryVanity, result.Category)
		assert.Empty(t, result.Colors)
		assert.Empty(t, result.Styles)
		assert.Equal(t, uint(0), result.PriceRange.Min)
		assert.Nil(t, result.PriceRange.Max)
		assert.Nil(t, result.MaxLeadTime)
		assert.Empty(t, result.Collections)
	})

	t.Run("price range conversion", func(t *testing.T) {
		priceMin := uint(1000)
		priceMax := uint(5000)
		filterParams := adapters.FilterParams{
			Category: usecases.CategoryFaucet,
			PriceGTE: &priceMin,
			PriceLTE: &priceMax,
		}

		result := filterParams.ToUsecase()

		assert.Equal(t, uint(1000), result.PriceRange.Min)
		require.NotNil(t, result.PriceRange.Max)
		assert.Equal(t, uint(5000), *result.PriceRange.Max)
	})

	t.Run("lead time conversion", func(t *testing.T) {
		leadTime := uint(30)
		filterParams := adapters.FilterParams{
			Category:    usecases.CategoryTile,
			LeadTimeLTE: &leadTime,
		}

		result := filterParams.ToUsecase()

		require.NotNil(t, result.MaxLeadTime)
		assert.Equal(t, uint(30), *result.MaxLeadTime)
	})

	t.Run("collections conversion", func(t *testing.T) {
		collection := "Modern Collection"
		filterParams := adapters.FilterParams{
			Category:   usecases.CategoryMirror,
			Collection: &collection,
		}

		result := filterParams.ToUsecase()

		require.Len(t, result.Collections, 1)
		assert.Equal(t, "Modern Collection", result.Collections[0])
	})

	t.Run("faucet hole spacing conversion", func(t *testing.T) {
		faucetSpacing := adapters.SingleHole
		filterParams := adapters.FilterParams{
			Category:                       usecases.CategoryFaucet,
			FaucetHoleSpacingCompatibility: &faucetSpacing,
		}

		result := filterParams.ToUsecase()

		require.NotNil(t, result.FaucetHoleSpacingCompatibility)
		assert.Equal(t, usecases.SingleHole, *result.FaucetHoleSpacingCompatibility)
	})

	t.Run("lighting filters conversion", func(t *testing.T) {
		lightingTypes := []usecases.LightingType{usecases.Sconce}
		numLightsMin := uint(2)
		numLightsMax := uint(6)
		lightingLength := float64(48.0)
		filterParams := adapters.FilterParams{
			Category:          usecases.CategoryLighting,
			LightingTypes:     lightingTypes,
			NumberOfLightsGTE: &numLightsMin,
			NumberOfLightsLTE: &numLightsMax,
			LightingLengthLTE: &lightingLength,
		}

		result := filterParams.ToUsecase()

		assert.Equal(t, lightingTypes, result.LightingTypes)
		assert.Equal(t, uint(2), result.LightingNumBulbs.Min)
		require.NotNil(t, result.LightingNumBulbs.Max)
		assert.Equal(t, uint(6), *result.LightingNumBulbs.Max)
		require.NotNil(t, result.Length.Max)
		assert.Equal(t, float64(48.0), *result.Length.Max)
	})

	t.Run("mirror filters conversion", func(t *testing.T) {
		mirrorType := adapters.MirrorTypeMedicineCabinet
		hasLED := true
		heightMin := float64(24.0)
		heightMax := float64(36.0)
		widthMin := float64(18.0)
		widthMax := float64(30.0)
		filterParams := adapters.FilterParams{
			Category:        usecases.CategoryMirror,
			MirrorType:      &mirrorType,
			MirrorHasLED:    &hasLED,
			MirrorHeightGTE: &heightMin,
			MirrorHeightLTE: &heightMax,
			MirrorWidthGTE:  &widthMin,
			MirrorWidthLTE:  &widthMax,
			MirrorShapes:    []usecases.MirrorShape{usecases.MirrorShapeRectangular},
		}

		result := filterParams.ToUsecase()

		require.NotNil(t, result.MirrorIsMedicineCabinet)
		assert.True(t, *result.MirrorIsMedicineCabinet)
		require.NotNil(t, result.MirrorIsLighted)
		assert.True(t, *result.MirrorIsLighted)
		assert.Equal(t, float64(24.0), result.Height.Min)
		require.NotNil(t, result.Height.Max)
		assert.Equal(t, float64(36.0), *result.Height.Max)
		assert.Equal(t, float64(18.0), result.Length.Min)
		require.NotNil(t, result.Length.Max)
		assert.Equal(t, float64(30.0), *result.Length.Max)
		assert.Equal(t, []usecases.MirrorShape{usecases.MirrorShapeRectangular}, result.MirrorShapes)
	})

	t.Run("shelf filters conversion", func(t *testing.T) {
		shelfCountMin := uint(2)
		shelfCountMax := uint(5)
		shelfLengthMin := float64(12.0)
		shelfLengthMax := float64(48.0)
		filterParams := adapters.FilterParams{
			Category:       usecases.CategoryShelving,
			ShelfCountGTE:  &shelfCountMin,
			ShelfCountLTE:  &shelfCountMax,
			ShelfLengthGTE: &shelfLengthMin,
			ShelfLengthLTE: &shelfLengthMax,
		}

		result := filterParams.ToUsecase()

		assert.Equal(t, uint(2), result.NumShelves.Min)
		require.NotNil(t, result.NumShelves.Max)
		assert.Equal(t, uint(5), *result.NumShelves.Max)
		assert.Equal(t, float64(12.0), result.Length.Min)
		require.NotNil(t, result.Length.Max)
		assert.Equal(t, float64(48.0), *result.Length.Max)
	})

	t.Run("shower filters conversion", func(t *testing.T) {
		hasTubSpout := true
		hasHandshower := false
		enclosureType := usecases.SwingingHinged
		filterParams := adapters.FilterParams{
			Category:              usecases.CategoryShower,
			ShowerHasTubSpout:     &hasTubSpout,
			HandshowerKitIncluded: &hasHandshower,
			ShowerEnclosureType:   &enclosureType,
		}

		result := filterParams.ToUsecase()

		require.NotNil(t, result.ShowerHasTubSpout)
		assert.True(t, *result.ShowerHasTubSpout)
		require.NotNil(t, result.ShowerHasHandheldShowerhead)
		assert.False(t, *result.ShowerHasHandheldShowerhead)
		require.Len(t, result.ShowerEnclosureTypes, 1)
		assert.Equal(t, usecases.SwingingHinged, result.ShowerEnclosureTypes[0])
	})

	t.Run("tile filters conversion", func(t *testing.T) {
		tileLocation := usecases.TileLocationFloor
		sqftMin := float64(10.0)
		sqftMax := float64(100.0)
		filterParams := adapters.FilterParams{
			Category:        usecases.CategoryTile,
			TileLocation:    &tileLocation,
			TileTypes:       []usecases.TileType{usecases.TileTypeCeramic},
			TileShapes:      []usecases.TileShape{usecases.TileShapeSquare},
			TileFinishes:    []usecases.TileFinish{usecases.TileFinishMatte},
			SqftCoverageGTE: &sqftMin,
			SqftCoverageLTE: &sqftMax,
		}

		result := filterParams.ToUsecase()

		require.NotNil(t, result.TileLocation)
		assert.Equal(t, usecases.TileLocationFloor, *result.TileLocation)
		assert.Equal(t, []usecases.TileType{usecases.TileTypeCeramic}, result.TileTypes)
		assert.Equal(t, []usecases.TileShape{usecases.TileShapeSquare}, result.TileShapes)
		assert.Equal(t, []usecases.TileFinish{usecases.TileFinishMatte}, result.TileFinishes)
		assert.Equal(t, float64(10.0), result.TileAreaCoverageSqFt.Min)
		require.NotNil(t, result.TileAreaCoverageSqFt.Max)
		assert.Equal(t, float64(100.0), *result.TileAreaCoverageSqFt.Max)
	})

	t.Run("toilet filters conversion", func(t *testing.T) {
		toiletType := adapters.ToiletTypeWallHung
		hasBidet := true
		filterParams := adapters.FilterParams{
			Category:   usecases.CategoryToilet,
			ToiletType: &toiletType,
			HasBidet:   &hasBidet,
		}

		result := filterParams.ToUsecase()

		require.NotNil(t, result.ToiletMountingPosition)
		assert.Equal(t, usecases.ToiletMountingPositionWall, *result.ToiletMountingPosition)
		require.NotNil(t, result.ToiletMustHaveBidet)
		assert.True(t, *result.ToiletMustHaveBidet)
	})

	t.Run("tub filters conversion", func(t *testing.T) {
		tubType := usecases.Alcove
		tubLengthMin := float64(60.0)
		tubLengthMax := float64(72.0)
		tubFillerPosition := usecases.TubFillerMountingPositionDeck
		filterParams := adapters.FilterParams{
			Category:                  usecases.CategoryTub,
			TubType:                   &tubType,
			TubLengthGTE:              &tubLengthMin,
			TubLengthLTE:              &tubLengthMax,
			TubFillerMountingPosition: &tubFillerPosition,
		}

		result := filterParams.ToUsecase()

		require.NotNil(t, result.TubType)
		assert.Equal(t, usecases.Alcove, *result.TubType)
		assert.Equal(t, float64(60.0), result.Length.Min)
		require.NotNil(t, result.Length.Max)
		assert.Equal(t, float64(72.0), *result.Length.Max)
		require.NotNil(t, result.TubFillerMountingPosition)
		assert.Equal(t, usecases.TubFillerMountingPositionDeck, *result.TubFillerMountingPosition)
	})

	t.Run("vanity filters conversion", func(t *testing.T) {
		vanityType := usecases.VanityTypeFloating
		vanityLengthMin := float64(24.0)
		vanityLengthMax := float64(72.0)
		sinksMin := uint(1)
		sinksMax := uint(2)
		filterParams := adapters.FilterParams{
			Category:         usecases.CategoryVanity,
			VanityType:       &vanityType,
			VanityLengthGTE:  &vanityLengthMin,
			VanityLengthLTE:  &vanityLengthMax,
			NumberOfSinksGTE: &sinksMin,
			NumberOfSinksLTE: &sinksMax,
		}

		result := filterParams.ToUsecase()

		require.Len(t, result.VanityTypes, 1)
		assert.Equal(t, usecases.VanityTypeFloating, result.VanityTypes[0])
		assert.Equal(t, float64(24.0), result.Length.Min)
		require.NotNil(t, result.Length.Max)
		assert.Equal(t, float64(72.0), *result.Length.Max)
		assert.Equal(t, uint(1), result.VanityNumSinks.Min)
		require.NotNil(t, result.VanityNumSinks.Max)
		assert.Equal(t, uint(2), *result.VanityNumSinks.Max)
	})

	t.Run("wallpaper filters conversion", func(t *testing.T) {
		filterParams := adapters.FilterParams{
			Category:              usecases.CategoryWallpaper,
			WallpaperApplications: []usecases.WallpaperApplication{usecases.WallpaperApplicationRemovable},
			WallpaperPatterns:     []usecases.WallpaperPattern{usecases.Geometric},
		}

		result := filterParams.ToUsecase()

		assert.Equal(t, []usecases.WallpaperApplication{usecases.WallpaperApplicationRemovable}, result.WallpaperApplications)
		assert.Equal(t, []usecases.WallpaperPattern{usecases.Geometric}, result.WallpaperPatterns)
	})

	t.Run("invalid faucet hole spacing", func(t *testing.T) {
		invalidSpacing := adapters.FaucetHoleSpacing("INVALID")
		filterParams := adapters.FilterParams{
			Category:                       usecases.CategoryFaucet,
			FaucetHoleSpacingCompatibility: &invalidSpacing,
		}

		result := filterParams.ToUsecase()

		// Should be nil when conversion fails
		assert.Nil(t, result.FaucetHoleSpacingCompatibility)
	})

	t.Run("toilet type freestanding conversion", func(t *testing.T) {
		toiletType := adapters.ToiletTypeFreestanding
		filterParams := adapters.FilterParams{
			Category:   usecases.CategoryToilet,
			ToiletType: &toiletType,
		}

		result := filterParams.ToUsecase()

		require.NotNil(t, result.ToiletMountingPosition)
		assert.Equal(t, usecases.ToiletMountingPositionFloor, *result.ToiletMountingPosition)
	})

	t.Run("mirror type mirror conversion", func(t *testing.T) {
		mirrorType := adapters.MirrorTypeMirror
		filterParams := adapters.FilterParams{
			Category:   usecases.CategoryMirror,
			MirrorType: &mirrorType,
		}

		result := filterParams.ToUsecase()

		require.NotNil(t, result.MirrorIsMedicineCabinet)
		assert.False(t, *result.MirrorIsMedicineCabinet)
	})
}

func TestFiltersFromUsecase(t *testing.T) {
	t.Run("minimal conversion", func(t *testing.T) {
		usecaseFilters := usecases.ProductSearchFilters{
			Category: usecases.CategoryVanity,
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Equal(t, usecases.CategoryVanity, result.Category)
		assert.Empty(t, result.Colors)
		assert.Empty(t, result.Styles)
		assert.Nil(t, result.PriceGTE)
		assert.Nil(t, result.PriceLTE)
		assert.Nil(t, result.LeadTimeLTE)
		assert.Nil(t, result.Collection)
	})

	t.Run("price range conversion", func(t *testing.T) {
		priceMax := uint(5000)
		usecaseFilters := usecases.ProductSearchFilters{
			Category: usecases.CategoryFaucet,
			PriceRange: usecases.UIntRange{
				Min: 1000,
				Max: &priceMax,
			},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.PriceGTE)
		assert.Equal(t, uint(1000), *result.PriceGTE)
		require.NotNil(t, result.PriceLTE)
		assert.Equal(t, uint(5000), *result.PriceLTE)
	})

	t.Run("price range with zero min", func(t *testing.T) {
		priceMax := uint(5000)
		usecaseFilters := usecases.ProductSearchFilters{
			Category: usecases.CategoryFaucet,
			PriceRange: usecases.UIntRange{
				Min: 0,
				Max: &priceMax,
			},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Nil(t, result.PriceGTE)
		require.NotNil(t, result.PriceLTE)
		assert.Equal(t, uint(5000), *result.PriceLTE)
	})

	t.Run("lead time conversion", func(t *testing.T) {
		leadTime := uint(30)
		usecaseFilters := usecases.ProductSearchFilters{
			Category:    usecases.CategoryTile,
			MaxLeadTime: &leadTime,
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.LeadTimeLTE)
		assert.Equal(t, uint(30), *result.LeadTimeLTE)
	})

	t.Run("collections conversion", func(t *testing.T) {
		usecaseFilters := usecases.ProductSearchFilters{
			Category:    usecases.CategoryMirror,
			Collections: []string{"Modern Collection", "Traditional Collection"},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.Collection)
		assert.Equal(t, "Modern Collection", *result.Collection)
	})

	t.Run("empty collections", func(t *testing.T) {
		usecaseFilters := usecases.ProductSearchFilters{
			Category:    usecases.CategoryMirror,
			Collections: []string{},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Nil(t, result.Collection)
	})

	t.Run("faucet hole spacing conversion", func(t *testing.T) {
		faucetSpacing := usecases.WideSpread
		usecaseFilters := usecases.ProductSearchFilters{
			Category:                       usecases.CategoryFaucet,
			FaucetHoleSpacingCompatibility: &faucetSpacing,
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.FaucetHoleSpacingCompatibility)
		assert.Equal(t, adapters.WideSpread, *result.FaucetHoleSpacingCompatibility)
	})

	t.Run("lighting filters conversion", func(t *testing.T) {
		numLightsMax := uint(6)
		lengthMax := float64(48.0)
		usecaseFilters := usecases.ProductSearchFilters{
			Category:      usecases.CategoryLighting,
			LightingTypes: []usecases.LightingType{usecases.Sconce},
			LightingNumBulbs: usecases.UIntRange{
				Min: 2,
				Max: &numLightsMax,
			},
			Length: usecases.FloatRange{
				Min: 0,
				Max: &lengthMax,
			},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Equal(t, []usecases.LightingType{usecases.Sconce}, result.LightingTypes)
		require.NotNil(t, result.NumberOfLightsGTE)
		assert.Equal(t, uint(2), *result.NumberOfLightsGTE)
		require.NotNil(t, result.NumberOfLightsLTE)
		assert.Equal(t, uint(6), *result.NumberOfLightsLTE)
		require.NotNil(t, result.LightingLengthLTE)
		assert.Equal(t, float64(48.0), *result.LightingLengthLTE)
	})

	t.Run("mirror filters conversion", func(t *testing.T) {
		isMedCab := true
		isLighted := false
		heightMax := float64(36.0)
		lengthMax := float64(30.0)
		usecaseFilters := usecases.ProductSearchFilters{
			Category:                usecases.CategoryMirror,
			MirrorIsMedicineCabinet: &isMedCab,
			MirrorIsLighted:         &isLighted,
			MirrorShapes:            []usecases.MirrorShape{usecases.MirrorShapeRectangular},
			Height: usecases.FloatRange{
				Min: 24.0,
				Max: &heightMax,
			},
			Length: usecases.FloatRange{
				Min: 18.0,
				Max: &lengthMax,
			},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.MirrorType)
		assert.Equal(t, adapters.MirrorTypeMedicineCabinet, *result.MirrorType)
		require.NotNil(t, result.MirrorHasLED)
		assert.False(t, *result.MirrorHasLED)
		assert.Equal(t, []usecases.MirrorShape{usecases.MirrorShapeRectangular}, result.MirrorShapes)
		require.NotNil(t, result.MirrorHeightGTE)
		assert.Equal(t, float64(24.0), *result.MirrorHeightGTE)
		require.NotNil(t, result.MirrorHeightLTE)
		assert.Equal(t, float64(36.0), *result.MirrorHeightLTE)
		require.NotNil(t, result.MirrorWidthGTE)
		assert.Equal(t, float64(18.0), *result.MirrorWidthGTE)
		require.NotNil(t, result.MirrorWidthLTE)
		assert.Equal(t, float64(30.0), *result.MirrorWidthLTE)
	})

	t.Run("mirror type regular mirror conversion", func(t *testing.T) {
		isMedCab := false
		usecaseFilters := usecases.ProductSearchFilters{
			Category:                usecases.CategoryMirror,
			MirrorIsMedicineCabinet: &isMedCab,
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.MirrorType)
		assert.Equal(t, adapters.MirrorTypeMirror, *result.MirrorType)
	})

	t.Run("shelf filters conversion", func(t *testing.T) {
		shelfCountMax := uint(5)
		lengthMax := float64(48.0)
		usecaseFilters := usecases.ProductSearchFilters{
			Category: usecases.CategoryShelving,
			NumShelves: usecases.UIntRange{
				Min: 2,
				Max: &shelfCountMax,
			},
			Length: usecases.FloatRange{
				Min: 12.0,
				Max: &lengthMax,
			},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.ShelfCountGTE)
		assert.Equal(t, uint(2), *result.ShelfCountGTE)
		require.NotNil(t, result.ShelfCountLTE)
		assert.Equal(t, uint(5), *result.ShelfCountLTE)
		require.NotNil(t, result.ShelfLengthGTE)
		assert.Equal(t, float64(12.0), *result.ShelfLengthGTE)
		require.NotNil(t, result.ShelfLengthLTE)
		assert.Equal(t, float64(48.0), *result.ShelfLengthLTE)
	})

	t.Run("shower filters conversion", func(t *testing.T) {
		hasTubSpout := true
		hasHandshower := false
		usecaseFilters := usecases.ProductSearchFilters{
			Category:                    usecases.CategoryShower,
			ShowerHasTubSpout:           &hasTubSpout,
			ShowerHasHandheldShowerhead: &hasHandshower,
			ShowerEnclosureTypes:        []usecases.ShowerEnclosureType{usecases.SwingingHinged, usecases.Sliding},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.ShowerHasTubSpout)
		assert.True(t, *result.ShowerHasTubSpout)
		require.NotNil(t, result.HandshowerKitIncluded)
		assert.False(t, *result.HandshowerKitIncluded)
		require.NotNil(t, result.ShowerEnclosureType)
		assert.Equal(t, usecases.SwingingHinged, *result.ShowerEnclosureType)
	})

	t.Run("tile filters conversion", func(t *testing.T) {
		tileLocation := usecases.TileLocationFloor
		sqftMax := float64(100.0)
		usecaseFilters := usecases.ProductSearchFilters{
			Category:     usecases.CategoryTile,
			TileLocation: &tileLocation,
			TileTypes:    []usecases.TileType{usecases.TileTypeCeramic},
			TileShapes:   []usecases.TileShape{usecases.TileShapeSquare},
			TileFinishes: []usecases.TileFinish{usecases.TileFinishMatte},
			TileAreaCoverageSqFt: usecases.FloatRange{
				Min: 10.0,
				Max: &sqftMax,
			},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.TileLocation)
		assert.Equal(t, usecases.TileLocationFloor, *result.TileLocation)
		assert.Equal(t, []usecases.TileType{usecases.TileTypeCeramic}, result.TileTypes)
		assert.Equal(t, []usecases.TileShape{usecases.TileShapeSquare}, result.TileShapes)
		assert.Equal(t, []usecases.TileFinish{usecases.TileFinishMatte}, result.TileFinishes)
		require.NotNil(t, result.SqftCoverageGTE)
		assert.Equal(t, float64(10.0), *result.SqftCoverageGTE)
		require.NotNil(t, result.SqftCoverageLTE)
		assert.Equal(t, float64(100.0), *result.SqftCoverageLTE)
	})

	t.Run("toilet filters conversion", func(t *testing.T) {
		toiletPosition := usecases.ToiletMountingPositionFloor
		hasBidet := true
		usecaseFilters := usecases.ProductSearchFilters{
			Category:               usecases.CategoryToilet,
			ToiletMountingPosition: &toiletPosition,
			ToiletMustHaveBidet:    &hasBidet,
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.ToiletType)
		assert.Equal(t, adapters.ToiletTypeFreestanding, *result.ToiletType)
		require.NotNil(t, result.HasBidet)
		assert.True(t, *result.HasBidet)
	})

	t.Run("toilet wall hung conversion", func(t *testing.T) {
		toiletPosition := usecases.ToiletMountingPositionWall
		usecaseFilters := usecases.ProductSearchFilters{
			Category:               usecases.CategoryToilet,
			ToiletMountingPosition: &toiletPosition,
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.ToiletType)
		assert.Equal(t, adapters.ToiletTypeWallHung, *result.ToiletType)
	})

	t.Run("tub filters conversion", func(t *testing.T) {
		tubType := usecases.Alcove
		tubFillerPosition := usecases.TubFillerMountingPositionDeck
		lengthMax := float64(72.0)
		usecaseFilters := usecases.ProductSearchFilters{
			Category:                  usecases.CategoryTub,
			TubType:                   &tubType,
			TubFillerMountingPosition: &tubFillerPosition,
			Length: usecases.FloatRange{
				Min: 60.0,
				Max: &lengthMax,
			},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.TubType)
		assert.Equal(t, usecases.Alcove, *result.TubType)
		require.NotNil(t, result.TubFillerMountingPosition)
		assert.Equal(t, usecases.TubFillerMountingPositionDeck, *result.TubFillerMountingPosition)
		require.NotNil(t, result.TubLengthGTE)
		assert.Equal(t, float64(60.0), *result.TubLengthGTE)
		require.NotNil(t, result.TubLengthLTE)
		assert.Equal(t, float64(72.0), *result.TubLengthLTE)
	})

	t.Run("vanity filters conversion", func(t *testing.T) {
		sinksMax := uint(2)
		lengthMax := float64(72.0)
		usecaseFilters := usecases.ProductSearchFilters{
			Category:    usecases.CategoryVanity,
			VanityTypes: []usecases.VanityType{usecases.VanityTypeFloating, usecases.VanityTypeFreestanding},
			VanityNumSinks: usecases.UIntRange{
				Min: 1,
				Max: &sinksMax,
			},
			Length: usecases.FloatRange{
				Min: 24.0,
				Max: &lengthMax,
			},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.VanityType)
		assert.Equal(t, usecases.VanityTypeFloating, *result.VanityType)
		require.NotNil(t, result.NumberOfSinksGTE)
		assert.Equal(t, uint(1), *result.NumberOfSinksGTE)
		require.NotNil(t, result.NumberOfSinksLTE)
		assert.Equal(t, uint(2), *result.NumberOfSinksLTE)
		require.NotNil(t, result.VanityLengthGTE)
		assert.Equal(t, float64(24.0), *result.VanityLengthGTE)
		require.NotNil(t, result.VanityLengthLTE)
		assert.Equal(t, float64(72.0), *result.VanityLengthLTE)
	})

	t.Run("wallpaper filters conversion", func(t *testing.T) {
		usecaseFilters := usecases.ProductSearchFilters{
			Category:              usecases.CategoryWallpaper,
			WallpaperApplications: []usecases.WallpaperApplication{usecases.WallpaperApplicationRemovable},
			WallpaperPatterns:     []usecases.WallpaperPattern{usecases.Geometric},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Equal(t, []usecases.WallpaperApplication{usecases.WallpaperApplicationRemovable}, result.WallpaperApplications)
		assert.Equal(t, []usecases.WallpaperPattern{usecases.Geometric}, result.WallpaperPatterns)
	})

	t.Run("zero values not converted", func(t *testing.T) {
		usecaseFilters := usecases.ProductSearchFilters{
			Category: usecases.CategoryMirror,
			Height: usecases.FloatRange{
				Min: 0,
				Max: nil,
			},
			Length: usecases.FloatRange{
				Min: 0,
				Max: nil,
			},
			LightingNumBulbs: usecases.UIntRange{
				Min: 0,
				Max: nil,
			},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Nil(t, result.MirrorHeightGTE)
		assert.Nil(t, result.MirrorWidthGTE)
		assert.Nil(t, result.NumberOfLightsGTE)
	})
}

func TestRoundTripConversions(t *testing.T) {
	t.Run("basic round trip", func(t *testing.T) {
		priceMin := uint(1000)
		priceMax := uint(5000)
		leadTime := uint(30)
		collection := "Modern Collection"

		original := adapters.FilterParams{
			Category:    usecases.CategoryVanity,
			PriceGTE:    &priceMin,
			PriceLTE:    &priceMax,
			LeadTimeLTE: &leadTime,
			Collection:  &collection,
			Colors:      []usecases.ColorGroup{usecases.White, usecases.Black},
			Styles:      []usecases.Style{usecases.Modern},
		}

		// Convert to usecase and back
		usecaseFilters := original.ToUsecase()
		roundTripped := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Equal(t, original.Category, roundTripped.Category)
		assert.Equal(t, original.Colors, roundTripped.Colors)
		assert.Equal(t, original.Styles, roundTripped.Styles)
		require.NotNil(t, roundTripped.PriceGTE)
		assert.Equal(t, *original.PriceGTE, *roundTripped.PriceGTE)
		require.NotNil(t, roundTripped.PriceLTE)
		assert.Equal(t, *original.PriceLTE, *roundTripped.PriceLTE)
		require.NotNil(t, roundTripped.LeadTimeLTE)
		assert.Equal(t, *original.LeadTimeLTE, *roundTripped.LeadTimeLTE)
		require.NotNil(t, roundTripped.Collection)
		assert.Equal(t, *original.Collection, *roundTripped.Collection)
	})

	t.Run("lighting category round trip", func(t *testing.T) {
		numLightsMin := uint(2)
		numLightsMax := uint(6)
		lightingLength := float64(48.0)

		original := adapters.FilterParams{
			Category:          usecases.CategoryLighting,
			LightingTypes:     []usecases.LightingType{usecases.BathBar},
			NumberOfLightsGTE: &numLightsMin,
			NumberOfLightsLTE: &numLightsMax,
			LightingLengthLTE: &lightingLength,
		}

		usecaseFilters := original.ToUsecase()
		roundTripped := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Equal(t, original.Category, roundTripped.Category)
		assert.Equal(t, original.LightingTypes, roundTripped.LightingTypes)
		require.NotNil(t, roundTripped.NumberOfLightsGTE)
		assert.Equal(t, *original.NumberOfLightsGTE, *roundTripped.NumberOfLightsGTE)
		require.NotNil(t, roundTripped.NumberOfLightsLTE)
		assert.Equal(t, *original.NumberOfLightsLTE, *roundTripped.NumberOfLightsLTE)
		require.NotNil(t, roundTripped.LightingLengthLTE)
		assert.Equal(t, *original.LightingLengthLTE, *roundTripped.LightingLengthLTE)
	})

	t.Run("mirror category round trip", func(t *testing.T) {
		mirrorType := adapters.MirrorTypeMedicineCabinet
		hasLED := true
		heightMin := float64(24.0)
		heightMax := float64(36.0)
		widthMin := float64(18.0)
		widthMax := float64(30.0)

		original := adapters.FilterParams{
			Category:        usecases.CategoryMirror,
			MirrorType:      &mirrorType,
			MirrorHasLED:    &hasLED,
			MirrorHeightGTE: &heightMin,
			MirrorHeightLTE: &heightMax,
			MirrorWidthGTE:  &widthMin,
			MirrorWidthLTE:  &widthMax,
			MirrorShapes:    []usecases.MirrorShape{usecases.MirrorShapeOval},
		}

		usecaseFilters := original.ToUsecase()
		roundTripped := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Equal(t, original.Category, roundTripped.Category)
		require.NotNil(t, roundTripped.MirrorType)
		assert.Equal(t, *original.MirrorType, *roundTripped.MirrorType)
		require.NotNil(t, roundTripped.MirrorHasLED)
		assert.Equal(t, *original.MirrorHasLED, *roundTripped.MirrorHasLED)
		assert.Equal(t, original.MirrorShapes, roundTripped.MirrorShapes)
		require.NotNil(t, roundTripped.MirrorHeightGTE)
		assert.Equal(t, *original.MirrorHeightGTE, *roundTripped.MirrorHeightGTE)
		require.NotNil(t, roundTripped.MirrorHeightLTE)
		assert.Equal(t, *original.MirrorHeightLTE, *roundTripped.MirrorHeightLTE)
		require.NotNil(t, roundTripped.MirrorWidthGTE)
		assert.Equal(t, *original.MirrorWidthGTE, *roundTripped.MirrorWidthGTE)
		require.NotNil(t, roundTripped.MirrorWidthLTE)
		assert.Equal(t, *original.MirrorWidthLTE, *roundTripped.MirrorWidthLTE)
	})

	t.Run("toilet category round trip", func(t *testing.T) {
		toiletType := adapters.ToiletTypeWallHung
		hasBidet := true

		original := adapters.FilterParams{
			Category:   usecases.CategoryToilet,
			ToiletType: &toiletType,
			HasBidet:   &hasBidet,
		}

		usecaseFilters := original.ToUsecase()
		roundTripped := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Equal(t, original.Category, roundTripped.Category)
		require.NotNil(t, roundTripped.ToiletType)
		assert.Equal(t, *original.ToiletType, *roundTripped.ToiletType)
		require.NotNil(t, roundTripped.HasBidet)
		assert.Equal(t, *original.HasBidet, *roundTripped.HasBidet)
	})

	t.Run("faucet hole spacing round trip", func(t *testing.T) {
		faucetSpacing := adapters.CenterSet

		original := adapters.FilterParams{
			Category:                       usecases.CategoryFaucet,
			FaucetHoleSpacingCompatibility: &faucetSpacing,
		}

		usecaseFilters := original.ToUsecase()
		roundTripped := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Equal(t, original.Category, roundTripped.Category)
		require.NotNil(t, roundTripped.FaucetHoleSpacingCompatibility)
		assert.Equal(t, *original.FaucetHoleSpacingCompatibility, *roundTripped.FaucetHoleSpacingCompatibility)
	})
}

func TestEdgeCases(t *testing.T) {
	t.Run("nil values", func(t *testing.T) {
		filterParams := adapters.FilterParams{
			Category:                       usecases.CategoryFaucet,
			PriceGTE:                       nil,
			PriceLTE:                       nil,
			LeadTimeLTE:                    nil,
			Collection:                     nil,
			FaucetHoleSpacingCompatibility: nil,
		}

		result := filterParams.ToUsecase()

		assert.Equal(t, usecases.CategoryFaucet, result.Category)
		assert.Equal(t, uint(0), result.PriceRange.Min)
		assert.Nil(t, result.PriceRange.Max)
		assert.Nil(t, result.MaxLeadTime)
		assert.Empty(t, result.Collections)
		assert.Nil(t, result.FaucetHoleSpacingCompatibility)
	})

	t.Run("empty slices", func(t *testing.T) {
		usecaseFilters := usecases.ProductSearchFilters{
			Category:              usecases.CategoryWallpaper,
			Collections:           []string{},
			LightingTypes:         []usecases.LightingType{},
			MirrorShapes:          []usecases.MirrorShape{},
			ShowerEnclosureTypes:  []usecases.ShowerEnclosureType{},
			TileTypes:             []usecases.TileType{},
			TileShapes:            []usecases.TileShape{},
			TileFinishes:          []usecases.TileFinish{},
			VanityTypes:           []usecases.VanityType{},
			WallpaperApplications: []usecases.WallpaperApplication{},
			WallpaperPatterns:     []usecases.WallpaperPattern{},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		assert.Equal(t, usecases.CategoryWallpaper, result.Category)
		assert.Nil(t, result.Collection)
		assert.Empty(t, result.LightingTypes)
		assert.Empty(t, result.MirrorShapes)
		assert.Nil(t, result.ShowerEnclosureType)
		assert.Empty(t, result.TileTypes)
		assert.Empty(t, result.TileShapes)
		assert.Empty(t, result.TileFinishes)
		assert.Nil(t, result.VanityType)
		assert.Empty(t, result.WallpaperApplications)
		assert.Empty(t, result.WallpaperPatterns)
	})

	t.Run("multiple collections only first is used", func(t *testing.T) {
		usecaseFilters := usecases.ProductSearchFilters{
			Category:    usecases.CategoryMirror,
			Collections: []string{"First Collection", "Second Collection", "Third Collection"},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.Collection)
		assert.Equal(t, "First Collection", *result.Collection)
	})

	t.Run("multiple shower enclosure types only first is used", func(t *testing.T) {
		usecaseFilters := usecases.ProductSearchFilters{
			Category:             usecases.CategoryShower,
			ShowerEnclosureTypes: []usecases.ShowerEnclosureType{usecases.SwingingHinged, usecases.Sliding, usecases.Fixed},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.ShowerEnclosureType)
		assert.Equal(t, usecases.SwingingHinged, *result.ShowerEnclosureType)
	})

	t.Run("multiple vanity types only first is used", func(t *testing.T) {
		usecaseFilters := usecases.ProductSearchFilters{
			Category:    usecases.CategoryVanity,
			VanityTypes: []usecases.VanityType{usecases.VanityTypeFloating, usecases.VanityTypeFreestanding, usecases.VanityTypeConsole},
		}

		result := adapters.FiltersFromUsecase(usecaseFilters)

		require.NotNil(t, result.VanityType)
		assert.Equal(t, usecases.VanityTypeFloating, *result.VanityType)
	})
}
