package adapters_test

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestCartInclusionTypeAliases(t *testing.T) {
	// Test that adapter types are properly aliased to usecase types
	productId := uuid.New()

	// Create using adapter type
	adapterInclusion := adapters.CartInclusion{
		ProductID:    productId,
		Location:     usecases.LocationFloor,
		Include:      true,
		QuantityDiff: 2,
	}

	// Should be directly assignable to usecase type since it's an alias
	var usecaseInclusion usecases.CartInclusion = adapterInclusion

	assert.Equal(t, productId, usecaseInclusion.ProductID)
	assert.Equal(t, usecases.LocationFloor, usecaseInclusion.Location)
	assert.True(t, usecaseInclusion.Include)
	assert.Equal(t, 2, usecaseInclusion.QuantityDiff)

	// Test CartInclusionKey alias
	adapterKey := adapters.CartInclusionKey{ProductID: productId, Location: usecases.LocationFloor}
	var usecaseKey usecases.CartInclusionKey = adapterKey
	assert.Equal(t, productId, usecaseKey.ProductID)
	assert.Equal(t, usecases.LocationFloor, usecaseKey.Location)

	// Test CartInclusions alias
	adapterInclusions := adapters.CartInclusions{
		adapterKey: adapterInclusion,
	}
	var usecaseInclusions usecases.CartInclusions = adapterInclusions
	assert.Len(t, usecaseInclusions, 1)
	assert.Equal(t, adapterInclusion, usecaseInclusions[usecaseKey])
}
