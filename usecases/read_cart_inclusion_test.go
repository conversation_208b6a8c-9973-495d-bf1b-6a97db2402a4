package usecases_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FakeCartInclusionsPresenter captures results for verification in tests
type FakeCartInclusionsPresenter struct {
	PresentErrorCalls          []error
	PresentCartInclusionsCalls []PresentCartInclusionsCall
}

type PresentCartInclusionsCall struct {
	Ctx        context.Context
	Inclusions usecases.CartInclusions
}

func NewFakeCartInclusionsPresenter() *FakeCartInclusionsPresenter {
	return &FakeCartInclusionsPresenter{
		PresentErrorCalls:          make([]error, 0),
		PresentCartInclusionsCalls: make([]PresentCartInclusionsCall, 0),
	}
}

func (p *FakeCartInclusionsPresenter) PresentError(err error) {
	p.PresentErrorCalls = append(p.PresentErrorCalls, err)
}

func (p *FakeCartInclusionsPresenter) PresentCartInclusions(ctx context.Context, inclusions usecases.CartInclusions) {
	p.PresentCartInclusionsCalls = append(p.PresentCartInclusionsCalls, PresentCartInclusionsCall{
		Ctx:        ctx,
		Inclusions: inclusions,
	})
}

func TestCartInclusionRetriever_RetrieveCartInclusions(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	retriever := usecases.NewCartInclusionRetriever(repo, nil)
	presenter := NewFakeCartInclusionsPresenter()

	// Create a test design first
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productId1 := uuid.New()
	productId2 := uuid.New()

	// Add test inclusions
	inclusion1 := usecases.CartInclusion{
		ProductID:    productId1,
		Location:     usecases.LocationFloor,
		Include:      true,
		QuantityDiff: 2,
	}
	inclusion2 := usecases.CartInclusion{
		ProductID:    productId2,
		Location:     usecases.LocationWall,
		Include:      false,
		QuantityDiff: -1,
	}

	err = repo.UpsertCartInclusion(ctx, designId, inclusion1)
	require.NoError(t, err)
	err = repo.UpsertCartInclusion(ctx, designId, inclusion2)
	require.NoError(t, err)

	// Retrieve inclusions
	retriever.RetrieveCartInclusions(ctx, presenter, designId)

	// Verify success
	assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
	assert.Len(t, presenter.PresentCartInclusionsCalls, 1, "Expected one call to PresentCartInclusions")

	// Verify inclusions were presented
	call := presenter.PresentCartInclusionsCalls[0]
	assert.Equal(t, ctx, call.Ctx, "Expected correct context")
	assert.Len(t, call.Inclusions, 2, "Expected 2 inclusions")

	// Verify inclusion 1 (inclusions are already a map!)
	key1 := usecases.CartInclusionKey{ProductID: productId1, Location: usecases.LocationFloor}
	inc1, exists := call.Inclusions[key1]
	require.True(t, exists, "Expected inclusion for productId1")
	assert.Equal(t, productId1, inc1.ProductID)
	assert.Equal(t, usecases.LocationFloor, inc1.Location)
	assert.True(t, inc1.Include)
	assert.Equal(t, 2, inc1.QuantityDiff)

	// Verify inclusion 2
	key2 := usecases.CartInclusionKey{ProductID: productId2, Location: usecases.LocationWall}
	inc2, exists := call.Inclusions[key2]
	require.True(t, exists, "Expected inclusion for productId2")
	assert.Equal(t, productId2, inc2.ProductID)
	assert.Equal(t, usecases.LocationWall, inc2.Location)
	assert.False(t, inc2.Include)
	assert.Equal(t, -1, inc2.QuantityDiff)
}

func TestCartInclusionRetriever_RetrieveCartInclusions_EmptyDesign(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	retriever := usecases.NewCartInclusionRetriever(repo, nil)
	presenter := NewFakeCartInclusionsPresenter()

	designId := uuid.New()

	// Retrieve inclusions for design with no inclusions
	retriever.RetrieveCartInclusions(ctx, presenter, designId)

	// Verify success with empty list
	assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
	assert.Len(t, presenter.PresentCartInclusionsCalls, 1, "Expected one call to PresentCartInclusions")

	call := presenter.PresentCartInclusionsCalls[0]
	assert.Equal(t, ctx, call.Ctx, "Expected correct context")
	assert.Len(t, call.Inclusions, 0, "Expected empty inclusions list")
}

func TestCartInclusionRetriever_Constructor(t *testing.T) {
	t.Run("panics with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewCartInclusionRetriever(nil, nil)
		}, "Expected panic with nil repository")
	})

	t.Run("works with nil logger", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		assert.NotPanics(t, func() {
			usecases.NewCartInclusionRetriever(repo, nil)
		}, "Should not panic with nil logger")
	})
}
