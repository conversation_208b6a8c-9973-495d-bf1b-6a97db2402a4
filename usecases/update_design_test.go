package usecases_test

import (
	"context"
	"database/sql"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// TestDesignMutationOutcomePresenter captures the outcome of design mutation operations for testing
type TestDesignMutationOutcomePresenter struct {
	successCalled bool
	errorCalled   bool
	lastError     error
	lastDesign    usecases.Design
	lastStatus    usecases.Status
}

func NewTestDesignMutationOutcomePresenter() *TestDesignMutationOutcomePresenter {
	return &TestDesignMutationOutcomePresenter{}
}

func (p *TestDesignMutationOutcomePresenter) ConveySuccess() {
	p.successCalled = true
}

func (p *TestDesignMutationOutcomePresenter) ConveySuccessWithResource(design usecases.Design, status usecases.Status) {
	p.successCalled = true
	p.lastDesign = design
	p.lastStatus = status
}

func (p *TestDesignMutationOutcomePresenter) PresentError(err error) {
	p.errorCalled = true
	p.lastError = err
}

func (p *TestDesignMutationOutcomePresenter) WasSuccessful() bool {
	return p.successCalled && !p.errorCalled
}

func (p *TestDesignMutationOutcomePresenter) WasError() bool {
	return p.errorCalled
}

func (p *TestDesignMutationOutcomePresenter) GetLastError() error {
	return p.lastError
}

func (p *TestDesignMutationOutcomePresenter) GetLastDesign() usecases.Design {
	return p.lastDesign
}

func (p *TestDesignMutationOutcomePresenter) GetLastStatus() usecases.Status {
	return p.lastStatus
}

func TestNewDesignUpdater(t *testing.T) {
	t.Run("should create updater with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		updater := usecases.NewDesignUpdater(repo)
		assert.NotNil(t, updater)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewDesignUpdater(nil)
		})
	})
}

func TestDesignUpdater_UpdateDesign(t *testing.T) {
	ctx := context.Background()
	designId := uuid.New()
	projectId := entities.ProjectId("TEST-PROJECT")

	existingDesign := usecases.Design{
		ID:                 designId,
		ProjectID:          projectId,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
		DesignOptions: usecases.DesignOptions{
			ColorScheme: &[]usecases.ColorScheme{usecases.Neutral}[0],
			Style:       &[]usecases.Style{usecases.Traditional}[0],
		},
		ShowerGlassVisible: false,
		TubDoorVisible:     false,
		NichesVisible:      false,
	}

	t.Run("should update design successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestDesignMutationOutcomePresenter()
		updater := usecases.NewDesignUpdater(repo)

		// First, save the existing design to the repository
		_, err := repo.UpsertDesign(ctx, existingDesign)
		require.NoError(t, err)

		diff := usecases.DesignDiff{
			ID:     designId,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}
		diff.ColorScheme = &[]usecases.ColorScheme{usecases.Bold}[0]

		updater.UpdateDesign(ctx, presenter, diff)

		// Verify the operation was successful
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed")
		assert.False(t, presenter.WasError(), "Expected no error")
		assert.Equal(t, usecases.Updated, presenter.GetLastStatus())

		// Verify the design was updated in the repository
		updatedDesign, err := repo.ReadDesign(ctx, designId)
		require.NoError(t, err)
		assert.Equal(t, usecases.Fave, updatedDesign.Status)
		assert.Equal(t, usecases.Bold, *updatedDesign.ColorScheme)

		// Verify unchanged fields remain the same
		assert.Equal(t, existingDesign.ProjectID, updatedDesign.ProjectID)
		assert.Equal(t, existingDesign.WallpaperPlacement, updatedDesign.WallpaperPlacement)
		assert.Equal(t, existingDesign.WallTilePlacement, updatedDesign.WallTilePlacement)
		assert.Equal(t, *existingDesign.Style, *updatedDesign.Style)

		// Verify LastUpdated was changed
		assert.True(t, updatedDesign.LastUpdated.After(existingDesign.LastUpdated))

		// Verify the presenter received the updated design
		presentedDesign := presenter.GetLastDesign()
		assert.Equal(t, usecases.Fave, presentedDesign.Status)
		assert.Equal(t, usecases.Bold, *presentedDesign.ColorScheme)
	})

	t.Run("should present error when design ID is zero UUID", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestDesignMutationOutcomePresenter()
		updater := usecases.NewDesignUpdater(repo)

		var zeroUUID uuid.UUID
		diff := usecases.DesignDiff{
			ID:     zeroUUID,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}

		updater.UpdateDesign(ctx, presenter, diff)

		// Verify the operation failed with the expected error
		assert.True(t, presenter.WasError(), "Expected operation to fail")
		assert.False(t, presenter.WasSuccessful(), "Expected no success")
		assert.Equal(t, usecases.ErrInvalidPayload, presenter.GetLastError())
	})

	t.Run("should present error when design ID is uuid.Nil", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestDesignMutationOutcomePresenter()
		updater := usecases.NewDesignUpdater(repo)

		diff := usecases.DesignDiff{
			ID:     uuid.Nil,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}

		updater.UpdateDesign(ctx, presenter, diff)

		// Verify the operation failed with the expected error
		assert.True(t, presenter.WasError(), "Expected operation to fail")
		assert.False(t, presenter.WasSuccessful(), "Expected no success")
		assert.Equal(t, usecases.ErrInvalidPayload, presenter.GetLastError())
	})

	t.Run("should present error when design not found", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestDesignMutationOutcomePresenter()
		updater := usecases.NewDesignUpdater(repo)

		// Don't save any design to the repository, so it won't be found
		nonExistentId := uuid.New()
		diff := usecases.DesignDiff{
			ID:     nonExistentId,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}

		updater.UpdateDesign(ctx, presenter, diff)

		// Verify the operation failed with the expected error
		assert.True(t, presenter.WasError(), "Expected operation to fail")
		assert.False(t, presenter.WasSuccessful(), "Expected no success")
		assert.Equal(t, usecases.ErrNotFound, presenter.GetLastError())
	})

	t.Run("should handle complex design updates", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestDesignMutationOutcomePresenter()
		updater := usecases.NewDesignUpdater(repo)

		// First, save the existing design to the repository
		_, err := repo.UpsertDesign(ctx, existingDesign)
		require.NoError(t, err)

		// Create a complex diff with multiple field updates
		newFaucet := uuid.New()
		newFloorTile := uuid.New()
		diff := usecases.DesignDiff{
			ID:                 designId,
			Status:             &[]usecases.DesignStatus{usecases.Archived}[0],
			WallpaperPlacement: &[]usecases.WallpaperPlacement{usecases.VanityWall}[0],
			WallTilePlacement:  &[]usecases.WallTilePlacement{usecases.HalfWall}[0],
			DesignOptions: usecases.DesignOptions{
				Title:       sql.NullString{String: "Updated Title", Valid: true},
				Description: sql.NullString{String: "Updated Description", Valid: true},
				ColorScheme: &[]usecases.ColorScheme{usecases.Bold}[0],
				Style:       &[]usecases.Style{usecases.Modern}[0],
			},
			ShowerGlassVisible: sql.NullBool{Bool: true, Valid: true},
			TubDoorVisible:     sql.NullBool{Bool: true, Valid: true},
			NichesVisible:      sql.NullBool{Bool: true, Valid: true},
		}
		diff.Faucet = &newFaucet
		diff.FloorTile = &newFloorTile

		updater.UpdateDesign(ctx, presenter, diff)

		// Verify the operation was successful
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed")
		assert.False(t, presenter.WasError(), "Expected no error")

		// Verify all fields were updated correctly
		updatedDesign, err := repo.ReadDesign(ctx, designId)
		require.NoError(t, err)
		assert.Equal(t, usecases.Archived, updatedDesign.Status)
		assert.Equal(t, usecases.VanityWall, updatedDesign.WallpaperPlacement)
		assert.Equal(t, usecases.HalfWall, updatedDesign.WallTilePlacement)
		assert.Equal(t, usecases.Bold, *updatedDesign.ColorScheme)
		assert.Equal(t, usecases.Modern, *updatedDesign.Style)
		assert.Equal(t, "Updated Title", updatedDesign.Title.String)
		assert.Equal(t, "Updated Description", updatedDesign.Description.String)
		assert.Equal(t, newFaucet, *updatedDesign.Faucet)
		assert.Equal(t, newFloorTile, *updatedDesign.FloorTile)
		assert.True(t, updatedDesign.ShowerGlassVisible)
		assert.True(t, updatedDesign.TubDoorVisible)
		assert.True(t, updatedDesign.NichesVisible)
	})

	t.Run("should handle partial updates without affecting other fields", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestDesignMutationOutcomePresenter()
		updater := usecases.NewDesignUpdater(repo)

		// First, save the existing design to the repository
		_, err := repo.UpsertDesign(ctx, existingDesign)
		require.NoError(t, err)

		// Update only the status field
		diff := usecases.DesignDiff{
			ID:     designId,
			Status: &[]usecases.DesignStatus{usecases.Archived}[0],
		}

		updater.UpdateDesign(ctx, presenter, diff)

		// Verify the operation was successful
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed")
		assert.False(t, presenter.WasError(), "Expected no error")

		// Verify only the status was updated
		updatedDesign, err := repo.ReadDesign(ctx, designId)
		require.NoError(t, err)
		assert.Equal(t, usecases.Archived, updatedDesign.Status)

		// Verify all other fields remain unchanged
		assert.Equal(t, existingDesign.ProjectID, updatedDesign.ProjectID)
		assert.Equal(t, existingDesign.WallpaperPlacement, updatedDesign.WallpaperPlacement)
		assert.Equal(t, existingDesign.WallTilePlacement, updatedDesign.WallTilePlacement)
		assert.Equal(t, *existingDesign.ColorScheme, *updatedDesign.ColorScheme)
		assert.Equal(t, *existingDesign.Style, *updatedDesign.Style)
		assert.Equal(t, existingDesign.Title, updatedDesign.Title)
		assert.Equal(t, existingDesign.Description, updatedDesign.Description)
		assert.Equal(t, existingDesign.ShowerGlassVisible, updatedDesign.ShowerGlassVisible)
		assert.Equal(t, existingDesign.TubDoorVisible, updatedDesign.TubDoorVisible)
		assert.Equal(t, existingDesign.NichesVisible, updatedDesign.NichesVisible)
	})

	t.Run("should handle nullable field updates correctly", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestDesignMutationOutcomePresenter()
		updater := usecases.NewDesignUpdater(repo)

		// First, save the existing design to the repository
		_, err := repo.UpsertDesign(ctx, existingDesign)
		require.NoError(t, err)

		// Update nullable fields
		diff := usecases.DesignDiff{
			ID: designId,
			DesignOptions: usecases.DesignOptions{
				Title:       sql.NullString{String: "New Title", Valid: true},
				Description: sql.NullString{String: "New Description", Valid: true},
			},
			ShowerGlassVisible: sql.NullBool{Bool: true, Valid: true},
			TubDoorVisible:     sql.NullBool{Bool: false, Valid: true},
			NichesVisible:      sql.NullBool{Bool: true, Valid: true},
		}

		updater.UpdateDesign(ctx, presenter, diff)

		// Verify the operation was successful
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed")
		assert.False(t, presenter.WasError(), "Expected no error")

		// Verify nullable fields were updated correctly
		updatedDesign, err := repo.ReadDesign(ctx, designId)
		require.NoError(t, err)
		assert.Equal(t, "New Title", updatedDesign.Title.String)
		assert.True(t, updatedDesign.Title.Valid)
		assert.Equal(t, "New Description", updatedDesign.Description.String)
		assert.True(t, updatedDesign.Description.Valid)
		assert.True(t, updatedDesign.ShowerGlassVisible)
		assert.False(t, updatedDesign.TubDoorVisible)
		assert.True(t, updatedDesign.NichesVisible)
	})

	t.Run("should preserve timestamps correctly", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestDesignMutationOutcomePresenter()
		updater := usecases.NewDesignUpdater(repo)

		// First, save the existing design to the repository
		_, err := repo.UpsertDesign(ctx, existingDesign)
		require.NoError(t, err)

		// Get the original timestamps
		originalDesign, err := repo.ReadDesign(ctx, designId)
		require.NoError(t, err)

		// Update the design
		diff := usecases.DesignDiff{
			ID:     designId,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}

		updater.UpdateDesign(ctx, presenter, diff)

		// Verify the operation was successful
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed")

		// Verify timestamp behavior
		updatedDesign, err := repo.ReadDesign(ctx, designId)
		require.NoError(t, err)

		// Created timestamp should remain the same
		assert.Equal(t, originalDesign.Created, updatedDesign.Created)

		// LastUpdated should be newer
		assert.True(t, updatedDesign.LastUpdated.After(originalDesign.LastUpdated) ||
			updatedDesign.LastUpdated.Equal(originalDesign.LastUpdated))
	})
}

func TestMergeDesigns(t *testing.T) {
	designId := uuid.New()
	projectId := entities.ProjectId("TEST-PROJECT")

	faucetId := uuid.New()
	floorTileId := uuid.New()

	baseDesign := usecases.Design{
		ID:                 designId,
		ProjectID:          projectId,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
		DesignOptions: usecases.DesignOptions{
			ColorScheme:      &[]usecases.ColorScheme{usecases.Neutral}[0],
			Style:            &[]usecases.Style{usecases.Traditional}[0],
			FloorTilePattern: &[]usecases.TilePattern{usecases.HorizontalStacked}[0],
			Title:            sql.NullString{String: "Original Title", Valid: true},
			Description:      sql.NullString{String: "Original Description", Valid: true},
			Faucet:           &faucetId,
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: &floorTileId,
			},
		},
		ShowerGlassVisible: false,
		TubDoorVisible:     false,
		NichesVisible:      false,
	}

	t.Run("should merge status field", func(t *testing.T) {
		diff := usecases.DesignDiff{
			ID:     designId,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}

		result := usecases.MergeDesigns(baseDesign, diff)
		assert.Equal(t, usecases.Fave, result.Status)
		// Other fields should remain unchanged
		assert.Equal(t, baseDesign.WallpaperPlacement, result.WallpaperPlacement)
		assert.Equal(t, baseDesign.ColorScheme, result.ColorScheme)
	})

	t.Run("should merge wallpaper placement field", func(t *testing.T) {
		diff := usecases.DesignDiff{
			ID:                 designId,
			WallpaperPlacement: &[]usecases.WallpaperPlacement{usecases.VanityWall}[0],
		}

		result := usecases.MergeDesigns(baseDesign, diff)
		assert.Equal(t, usecases.VanityWall, result.WallpaperPlacement)
		assert.Equal(t, baseDesign.Status, result.Status) // Should remain unchanged
	})

	t.Run("should merge wall tile placement field", func(t *testing.T) {
		diff := usecases.DesignDiff{
			ID:                designId,
			WallTilePlacement: &[]usecases.WallTilePlacement{usecases.HalfWall}[0],
		}

		result := usecases.MergeDesigns(baseDesign, diff)
		assert.Equal(t, usecases.HalfWall, result.WallTilePlacement)
	})

	t.Run("should merge design options fields", func(t *testing.T) {
		newFaucet := uuid.New()
		newFloorTile := uuid.New()

		diff := usecases.DesignDiff{
			ID: designId,
		}
		diff.ColorScheme = &[]usecases.ColorScheme{usecases.Bold}[0]
		diff.Style = &[]usecases.Style{usecases.Modern}[0]
		diff.FloorTilePattern = &[]usecases.TilePattern{usecases.Herringbone}[0]
		diff.Title = sql.NullString{String: "New Title", Valid: true}
		diff.Description = sql.NullString{String: "New Description", Valid: true}
		diff.Faucet = &newFaucet
		diff.FloorTile = &newFloorTile

		result := usecases.MergeDesigns(baseDesign, diff)
		assert.Equal(t, usecases.Bold, *result.ColorScheme)
		assert.Equal(t, usecases.Modern, *result.Style)
		assert.Equal(t, usecases.Herringbone, *result.FloorTilePattern)
		assert.Equal(t, "New Title", result.Title.String)
		assert.Equal(t, "New Description", result.Description.String)
		assert.Equal(t, newFaucet, *result.Faucet)
		assert.Equal(t, newFloorTile, *result.FloorTile)
	})

	t.Run("should merge boolean visibility fields", func(t *testing.T) {
		diff := usecases.DesignDiff{
			ID:                 designId,
			ShowerGlassVisible: sql.NullBool{Bool: true, Valid: true},
			TubDoorVisible:     sql.NullBool{Bool: true, Valid: true},
			NichesVisible:      sql.NullBool{Bool: true, Valid: true},
		}

		result := usecases.MergeDesigns(baseDesign, diff)
		assert.True(t, result.ShowerGlassVisible)
		assert.True(t, result.TubDoorVisible)
		assert.True(t, result.NichesVisible)
	})

	t.Run("should not merge nil or invalid fields", func(t *testing.T) {
		diff := usecases.DesignDiff{
			ID:                 designId,
			Status:             nil,                        // Should not merge
			ShowerGlassVisible: sql.NullBool{Valid: false}, // Should not merge
		}
		// Don't set ColorScheme or Title - they should remain nil/invalid
		diff.Title = sql.NullString{Valid: false} // Should not merge

		result := usecases.MergeDesigns(baseDesign, diff)
		// All fields should remain unchanged
		assert.Equal(t, baseDesign.Status, result.Status)
		assert.Equal(t, baseDesign.ColorScheme, result.ColorScheme)
		assert.Equal(t, baseDesign.Title, result.Title)
		assert.Equal(t, baseDesign.ShowerGlassVisible, result.ShowerGlassVisible)
	})

	t.Run("should handle partial merge", func(t *testing.T) {
		// Only update some fields, leave others unchanged
		diff := usecases.DesignDiff{
			ID:     designId,
			Status: &[]usecases.DesignStatus{usecases.Archived}[0],
		}
		diff.ColorScheme = &[]usecases.ColorScheme{usecases.Bold}[0]
		// Don't update Style, FloorTilePattern, etc.

		result := usecases.MergeDesigns(baseDesign, diff)
		// Updated fields
		assert.Equal(t, usecases.Archived, result.Status)
		assert.Equal(t, usecases.Bold, *result.ColorScheme)
		// Unchanged fields
		assert.Equal(t, baseDesign.Style, result.Style)
		assert.Equal(t, baseDesign.FloorTilePattern, result.FloorTilePattern)
		assert.Equal(t, baseDesign.WallpaperPlacement, result.WallpaperPlacement)
	})
}
