package usecases

import (
	"context"
	"log"

	"github.com/google/uuid"
)

type RenditionDeleter struct {
	renditionRepo renditionRepository
}

func NewRenditionDeleter(renditionRepo renditionRepository) *RenditionDeleter {
	if IsNil(renditionRepo) {
		panic("renditionRepo cannot be nil")
	}
	return &RenditionDeleter{renditionRepo: renditionRepo}
}

func (rd *RenditionDeleter) DeleteRendition(ctx context.Context, presenter OutcomePresenter, renditionId uuid.UUID) {
	var zeroUUID uuid.UUID
	if renditionId == zeroUUID || renditionId == uuid.Nil {
		log.Println("Received request to delete rendition with unspecified ID.")
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	err := rd.renditionRepo.DeleteRendition(ctx, renditionId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.ConveySuccess()
}
