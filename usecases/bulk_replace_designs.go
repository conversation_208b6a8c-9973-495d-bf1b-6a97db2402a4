package usecases

import (
	"context"
	"log/slog"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type repo interface {
	designRepository
	presetRepositoryReplica
	renditionRepository
}

type BulkDesignReplacer struct {
	logger   *slog.Logger
	repo     repo
	monolith monolith
}

func NewBulkDesignReplacer(repo repo, monolith monolith, logger *slog.Logger) *BulkDesignReplacer {
	if IsNil(repo) {
		panic("repo cannot be nil")
	}
	if IsNil(monolith) {
		panic("monolith cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &BulkDesignReplacer{logger: logger, repo: repo, monolith: monolith}
}

func (bdr *BulkDesignReplacer) ReplaceAllDesignsForProject(ctx context.Context,
	presenter OutcomePresenter, designs []Design, projectId entities.ProjectId) {

	var zeroUUID uuid.UUID
	errors := []error{}
	designIds := make([]uuid.UUID, 0, len(designs))
	for _, design := range designs {
		if design.ID == zeroUUID || design.ID == uuid.Nil {
			bdr.logger.ErrorContext(ctx, "Design ID is nil when replacing designs",
				slog.String("designId", design.ID.String()))
			errors = append(errors, ErrInvalidPayload)
			continue
		}
		designIds = append(designIds, design.ID)
		_, err := bdr.repo.UpsertDesign(ctx, design)
		if err != nil {
			bdr.logger.ErrorContext(ctx, "Failed to save design", slog.String("error", err.Error()))
			errors = append(errors, err)
		}
	}
	if err := bdr.repo.DeleteDesignsForProjectExceptSpecified(ctx, projectId, designIds); err != nil {
		bdr.logger.ErrorContext(ctx, "Failed to delete orphaned designs", slog.String("error", err.Error()))
	}
	if err := bdr.repo.MarkAllCompletedRenditionsOutdatedForProject(ctx, projectId); err != nil {
		bdr.logger.ErrorContext(ctx, "Failed to mark renditions as outdated", slog.String("error", err.Error()))
	}
	if len(errors) > 0 {
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	presenter.ConveySuccess()
}
