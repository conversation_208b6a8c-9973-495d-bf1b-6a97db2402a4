package usecases

import (
	"context"

	"github.com/google/uuid"
)

type ProjectIdRetriever struct {
	designRepo designRepositoryReplica
}

func NewProjectIdRetriever(designRepo designRepositoryReplica) *ProjectIdRetriever {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	return &ProjectIdRetriever{designRepo: designRepo}
}

func (pr *ProjectIdRetriever) RetrieveProjectIdForDesign(ctx context.Context, presenter ProjectIdPresenter, designId uuid.UUID) {
	projectId, err := pr.designRepo.ProjectIdForDesign(ctx, designId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentProjectId(ctx, projectId)
}
