package usecases

import (
	"context"

	"github.com/google/uuid"
)

type RenditionRetriever struct {
	renditionRepo renditionRepository
}

func NewRenditionRetriever(renditionRepo renditionRepository) *RenditionRetriever {
	if IsNil(renditionRepo) {
		panic("renditionRepo cannot be nil")
	}
	return &RenditionRetriever{renditionRepo: renditionRepo}
}

func (r *RenditionRetriever) Renditions(ctx context.Context, ids []uuid.UUID,
	presenter RenditionsPresenter) {

	renditions, err := r.renditionRepo.Renditions(ctx, ids)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentRenditions(ctx, renditions)
}

func (r *RenditionRetriever) RenditionsForDesign(ctx context.Context, designId uuid.UUID, presenter RenditionsPresenter) {
	renditions, err := r.renditionRepo.RenditionsForDesign(ctx, designId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentRenditions(ctx, renditions)
}
