package usecases

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type templateRepo interface {
	templateRepositoryReplica
	PanoramicImagesForTemplates(ctx context.Context, templateIds []uuid.UUID) (map[uuid.UUID]url.URL, error)
}

// DesignGenerator creates designs for specified room layouts
type DesignGenerator struct {
	logger        *slog.Logger
	templateRepo  templateRepo
	monolith      monolith
	rooms         rooms
	productSearch productSearch
	genAI         genAI
}

func NewDesignGenerator(templateRepo templateRepo, monolith monolith, rooms rooms, productSearch productSearch, genAI genAI,
	logger *slog.Logger) *DesignGenerator {

	if IsNil(templateRepo) {
		panic("templateRepo cannot be nil")
	}
	if IsNil(monolith) {
		panic("monolith cannot be nil")
	}
	if IsNil(rooms) {
		panic("rooms cannot be nil")
	}
	if IsNil(productSearch) {
		panic("productSearch cannot be nil")
	}
	if IsNil(genAI) {
		panic("genAI cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignGenerator{logger: logger, templateRepo: templateRepo, monolith: monolith, rooms: rooms, productSearch: productSearch, genAI: genAI}
}

func (dg *DesignGenerator) GenerateDesignForProjectViaAI(ctx context.Context,
	projectId entities.ProjectId, userInput string, presenter DesignsPresenter) {

	if IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	dg.logger.InfoContext(ctx, "Generating design for project via AI",
		slog.String("projectId", projectId.String()))

	roomLayout, err := dg.monolith.GetLayoutForProject(ctx, projectId)
	if err != nil {
		dg.logger.ErrorContext(ctx, "Failed to fetch layout for project",
			slog.String("projectId", projectId.String()),
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}
	dg.GenerateDesignViaAI(ctx, roomLayout, userInput, presenter)
}

// productSearchResult holds the result of a product search for a specific category
type productSearchResult struct {
	filters      ProductSearchFilters
	productUUIDs []uuid.UUID
	err          error
}

// GenerateDesignViaAI creates a design via AI and then presents it using the provided DesignsPresenter
func (dg *DesignGenerator) GenerateDesignViaAI(ctx context.Context,
	roomLayout entities.RoomLayout, userInput string, presenter DesignsPresenter) {

	start := time.Now()
	design := NewEmptyDesign()

	// Define product categories to search for.
	// This map can only handle 1 product per category.
	// TODO: Handle multiple products per category for tiles.
	floor := TileLocationFloor
	catMap := map[Category]productSearchResult{
		CategoryTile: {
			filters: ProductSearchFilters{
				Category:     CategoryTile,
				TileLocation: &floor,
			},
		},
		CategoryPaint: {
			filters: ProductSearchFilters{
				Category: CategoryPaint,
			},
		},
		CategoryVanity: {
			filters: ProductSearchFilters{
				Category: CategoryVanity,
			},
		},
		CategoryFaucet: {
			filters: ProductSearchFilters{
				Category: CategoryFaucet,
			},
		},
		CategoryMirror: {
			filters: ProductSearchFilters{
				Category: CategoryMirror,
			},
		},
		CategoryLighting: {
			filters: ProductSearchFilters{
				Category: CategoryLighting,
			},
		},
		CategoryWallpaper: {
			filters: ProductSearchFilters{
				Category: CategoryWallpaper,
			},
		},
	}

	// Create a channel to collect results
	resultsChan := make(chan productSearchResult, len(catMap))
	var wg sync.WaitGroup
	// Launch goroutines for each category
	dg.logger.InfoContext(ctx, "Searching for products for AI design generation...",
		slog.String("projectId", design.ProjectID.String()))
	for _, result := range catMap {
		result.filters.Query = &userInput
		wg.Add(1)
		go dg.searchProductsForCategory(ctx, result.filters, resultsChan, &wg)
	}
	// Wait for all goroutines to complete and close the channel
	go func() {
		wg.Wait()
		close(resultsChan)
	}()

	// Collect results
	var searchErrors []string
	for result := range resultsChan {
		if result.err != nil {
			dg.logger.ErrorContext(ctx, "Failed to search products for category",
				slog.String("category", string(result.filters.Category)),
				slog.String("error", result.err.Error()))
			searchErrors = append(searchErrors, result.err.Error())
		}
		catMap[result.filters.Category] = result
	}

	// If there were search errors, present the error and return
	if len(searchErrors) > 0 {
		dg.logger.ErrorContext(ctx, "Product Search failed for some categories",
			slog.Int("errorCount", len(searchErrors)))
		presenter.PresentError(fmt.Errorf("product search failed for some categories: %s",
			strings.Join(searchErrors, "; ")))
		return
	}

	dg.populateDesignWithProducts(&design, catMap)
	dg.logger.InfoContext(ctx, "Successfully generated design via AI",
		slog.String("projectId", design.ProjectID.String()),
		slog.String("designId", design.ID.String()),
		slog.String("latency", time.Since(start).String()))
	presenter.PresentDesign(ctx, design)
}

// searchProductsForCategory searches for products in a specific category and sends the result to the channel
func (dg *DesignGenerator) searchProductsForCategory(ctx context.Context, filters ProductSearchFilters,
	resultsChan chan<- productSearchResult, wg *sync.WaitGroup) {

	defer wg.Done()
	dg.logger.DebugContext(ctx, "Searching for products..",
		slog.String("category", string(filters.Category)))
	productUUIDs, err := dg.productSearch.FindProductsViaAI(ctx, filters)

	if err != nil {
		dg.logger.ErrorContext(ctx, "Failed to search products for category",
			slog.String("category", string(filters.Category)),
			slog.String("error", err.Error()))
	} else if len(productUUIDs) == 0 {
		dg.logger.WarnContext(ctx, "No products found 😟",
			slog.String("category", string(filters.Category)))
	} else {
		dg.logger.InfoContext(ctx, "Successfully found products",
			slog.String("category", string(filters.Category)),
			slog.Int("productCount", len(productUUIDs)))
	}

	// Send result to channel
	resultsChan <- productSearchResult{
		filters:      filters,
		productUUIDs: productUUIDs,
		err:          err,
	}
}

// populateDesignWithProducts populates the design with products from search results
func (dg *DesignGenerator) populateDesignWithProducts(design *Design, results map[Category]productSearchResult) {
	// Helper function to get first product UUID from results, if available
	getFirstProduct := func(category Category) *uuid.UUID {
		if result, exists := results[category]; exists && len(result.productUUIDs) > 0 {
			return &result.productUUIDs[0]
		}
		return nil
	}

	design.Toilet = getFirstProduct(CategoryToilet)
	design.Mirror = getFirstProduct(CategoryMirror)
	design.Lighting = getFirstProduct(CategoryLighting)
	design.Paint = getFirstProduct(CategoryPaint)
	design.Shelving = getFirstProduct(CategoryShelving)
	design.FloorTile = getFirstProduct(CategoryTile)
	design.Wallpaper = getFirstProduct(CategoryWallpaper)
	design.Faucet = getFirstProduct(CategoryFaucet)
	design.Vanity = getFirstProduct(CategoryVanity)
}

// GenerateDesignsFromTemplates creates a slice of designs from templates and room layout,
// then presents them using the provided DesignsPresenter
func (dg *DesignGenerator) GenerateDesignsFromTemplates(ctx context.Context,
	roomLayout entities.RoomLayout, templates []Template, panoramicImagesForTemplates map[uuid.UUID]url.URL,
	presenter DesignsPresenter) {

	if IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	if len(templates) == 0 {
		dg.logger.DebugContext(ctx, "No templates provided for design generation")
		presenter.PresentDesigns(ctx, []Design{})
		return
	}
	dg.logger.DebugContext(ctx, "Generating designs from templates",
		slog.Int("templateCount", len(templates)),
		slog.String("roomLayoutId", roomLayout.Id.String()),
	)

	designs := make([]Design, 0, len(templates))
	for _, template := range templates {
		design, err := dg.generateDesignFromTemplate(roomLayout, template)
		if err != nil {
			dg.logger.ErrorContext(ctx, "Failed to generate design from template",
				slog.String("templateId", template.ID.String()),
				slog.String("error", err.Error()))
			presenter.PresentError(err)
			return
		}
		if panoramicImagesForTemplates == nil {
			designs = append(designs, design)
			continue
		}

		panoURL, ok := panoramicImagesForTemplates[template.ID]
		if ok {
			design.Renditions = []entities.Rendition{
				{URL: &panoURL, Status: entities.RenditionCompleted, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			}
		} else {
			dg.logger.ErrorContext(ctx, "Failed to find panoramic image for template",
				slog.String("templateId", template.ID.String()))
		}
		designs = append(designs, design)
	}

	dg.logger.InfoContext(ctx, "Successfully generated designs from templates",
		slog.Int("designCount", len(designs)))
	presenter.PresentDesigns(ctx, designs)
}

// GenerateDesignsForProjectFromTemplates fetches the templates and project layout by IDs,
// then generates designs from those templates using GenerateDesignsFromTemplates.
func (dg *DesignGenerator) GenerateDesignsForProjectFromTemplates(ctx context.Context,
	projectId entities.ProjectId, templateIds []uuid.UUID, presenter DesignsPresenter) {

	if IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	if len(templateIds) == 0 {
		dg.logger.DebugContext(ctx, "No template IDs provided for design generation")
		presenter.PresentDesigns(ctx, []Design{})
		return
	}
	dg.logger.InfoContext(ctx, "Generating designs for project from templates",
		slog.String("projectId", projectId.String()),
		slog.Int("templateCount", len(templateIds)))

	roomLayout, err := dg.monolith.GetLayoutForProject(ctx, projectId)
	if err != nil {
		dg.logger.ErrorContext(ctx, "Failed to fetch layout for project",
			slog.String("projectId", projectId.String()),
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}
	templates, err := dg.templateRepo.TemplatesById(ctx, templateIds)
	if err != nil {
		dg.logger.ErrorContext(ctx, "Failed to fetch templates",
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}

	var panos map[uuid.UUID]url.URL
	defaultRoomLayout := dg.rooms.DefaultRoomLayout()
	if defaultRoomLayout.Hash != roomLayout.Hash {
		dg.logger.WarnContext(ctx, "Room layout is different from the default; skipping instant panos.")
		dg.GenerateDesignsFromTemplates(ctx, roomLayout, templates, panos, presenter)
		return
	}

	panos, err = dg.templateRepo.PanoramicImagesForTemplates(ctx, templateIds)
	if err != nil {
		dg.logger.ErrorContext(ctx, "Failed to fetch panoramic images for templates",
			slog.String("error", err.Error()))
	}
	dg.GenerateDesignsFromTemplates(ctx, roomLayout, templates, panos, presenter)
}

// generateDesignFromTemplate converts a single template into a design based on the room layout
func (dg *DesignGenerator) generateDesignFromTemplate(
	roomLayout entities.RoomLayout, template Template) (Design, error) {

	now := time.Now()
	design := NewEmptyDesign()
	design.Created = now
	design.LastUpdated = now
	design.WallpaperPlacement = template.WallpaperPlacement
	design.WallTilePlacement = template.WallTilePlacement
	design.DesignOptions = DesignOptions{
		ColorScheme:            &template.ColorScheme,
		Style:                  &template.Style,
		Title:                  sql.NullString{String: template.Name, Valid: true},
		Description:            sql.NullString{String: template.Description, Valid: true},
		FixedProductSelections: template.FixedProductSelections,
	}

	dg.applyLayoutSpecificProducts(&design, roomLayout, template)
	return design, nil
}

// applyLayoutSpecificProducts selects appropriate products based on room layout features
func (dg *DesignGenerator) applyLayoutSpecificProducts(
	design *Design, roomLayout entities.RoomLayout, template Template) {

	if len(roomLayout.WetAreas) > 0 {
		design.ShowerWallTile = template.ShowerWallTile
		wetArea := roomLayout.WetAreas[0]
		if len(wetArea.ShowerIds) > 0 {
			if len(wetArea.AlcoveTubs) > 0 {
				design.ShowerSystem = &template.ShowerSystemCombo
			} else {
				design.ShowerSystem = &template.ShowerSystemSolo
			}
		}

		switch wetArea.GlassType {
		case entities.SlidingShowerEnclosure:
			design.ShowerGlass = &template.ShowerGlassSliding
		case entities.FixedShowerEnclosure:
			design.ShowerGlass = &template.ShowerGlassFixed
		}

		if len(wetArea.AlcoveTubs) > 0 {
			design.Tub = &template.AlcoveTub
			alcoveTub := wetArea.AlcoveTubs[0]
			switch alcoveTub.DoorType {
			case entities.SlidingShowerEnclosure:
				design.TubDoor = &template.TubDoorSliding
			case entities.FixedShowerEnclosure:
				design.TubDoor = &template.TubDoorFixed
			}
		} else if len(wetArea.FreestandingTubIds) > 0 {
			design.Tub = &template.FreestandingTub
			design.TubFiller = template.TubFiller
			design.ShowerFloorTile = template.ShowerFloorTile
		}
	}

	if len(roomLayout.Vanities) > 0 {
		vanity := roomLayout.Vanities[0]
		if vanity.MaxLength != nil {
			maxLengthInches := int(*vanity.MaxLength)
			// Find the largest vanity that fits within the max length
			var bestLength int
			var bestOption VanityScalingOption
			var found bool

			for length, scalingOption := range template.VanityScalingOptions {
				if length <= maxLengthInches && length > bestLength {
					bestLength = length
					bestOption = scalingOption
					found = true
				}
			}

			if found {
				design.Vanity = &bestOption.VanityProductID
				design.Faucet = &bestOption.FaucetProductID
			}
		}
	}

	if dg.hasNiches(roomLayout) && template.ShowerWallTile != nil {
		design.NicheTile = template.ShowerWallTile
	}
}

func (dg *DesignGenerator) hasNiches(roomLayout entities.RoomLayout) bool {
	for _, wall := range roomLayout.Walls {
		if len(wall.NicheIds) > 0 {
			return true
		}
	}
	return false
}
