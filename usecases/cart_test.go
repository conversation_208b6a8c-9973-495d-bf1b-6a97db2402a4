package usecases

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

// newTestDesign creates a new Design with initialized CartInclusions for testing.
// If an ID is provided, it will be used; otherwise a new UUID is generated.
func newTestDesign(projectID entities.ProjectId, id ...uuid.UUID) Design {
	var designID uuid.UUID
	if len(id) > 0 {
		designID = id[0]
	} else {
		designID = uuid.New()
	}

	return Design{
		ID:             designID,
		ProjectID:      projectID,
		Created:        time.Now(),
		LastUpdated:    time.Now(),
		CartInclusions: make(CartInclusions),
	}
}

func TestDesignAddCartInclusion(t *testing.T) {
	design := newTestDesign(entities.NewProjectId("TEST-PROJECT"))

	productID := uuid.New()
	location := LocationFloor

	// Initially no inclusions (but map is initialized)
	assert.Len(t, design.CartInclusions, 0)

	// Add cart inclusion
	design.AddCartInclusion(productID, location, true, 2)

	// Now should have inclusions
	assert.Len(t, design.CartInclusions, 1)

	// Verify inclusion
	key := CartInclusionKey{ProductID: productID, Location: location}
	cartInclusion, exists := design.CartInclusions[key]
	require.True(t, exists)
	assert.Equal(t, productID, cartInclusion.ProductID)
	assert.Equal(t, location, cartInclusion.Location)
	assert.True(t, cartInclusion.Include)
	assert.Equal(t, 2, cartInclusion.QuantityDiff)
}

func TestDesignUpdateCartInclusion(t *testing.T) {
	design := newTestDesign(entities.NewProjectId("TEST-PROJECT"))

	productID := uuid.New()
	location := LocationWall

	// Add initial inclusion
	design.AddCartInclusion(productID, location, true, 1)

	// Update the inclusion
	design.AddCartInclusion(productID, location, false, 3)

	// Verify update
	key := CartInclusionKey{ProductID: productID, Location: location}
	updated := design.CartInclusions[key]
	assert.Equal(t, productID, updated.ProductID)
	assert.Equal(t, location, updated.Location)
	assert.False(t, updated.Include)
	assert.Equal(t, 3, updated.QuantityDiff)
}

func TestDesignMultipleCartInclusions(t *testing.T) {
	design := newTestDesign(entities.NewProjectId("TEST-PROJECT"))

	product1 := uuid.New()
	product2 := uuid.New()
	location1 := LocationShowerFloor
	location2 := LocationShowerWall

	// Add multiple inclusions
	design.AddCartInclusion(product1, location1, true, 1)
	design.AddCartInclusion(product2, location2, false, -1)

	// Verify both inclusions exist
	assert.Len(t, design.CartInclusions, 2)

	key1 := CartInclusionKey{ProductID: product1, Location: location1}
	result1, exists1 := design.CartInclusions[key1]
	require.True(t, exists1)
	assert.Equal(t, product1, result1.ProductID)
	assert.Equal(t, location1, result1.Location)
	assert.True(t, result1.Include)
	assert.Equal(t, 1, result1.QuantityDiff)

	key2 := CartInclusionKey{ProductID: product2, Location: location2}
	result2, exists2 := design.CartInclusions[key2]
	require.True(t, exists2)
	assert.Equal(t, product2, result2.ProductID)
	assert.Equal(t, location2, result2.Location)
	assert.False(t, result2.Include)
	assert.Equal(t, -1, result2.QuantityDiff)
}

func TestDesignCartInclusionsIntegration(t *testing.T) {
	design := newTestDesign(entities.NewProjectId("TEST-PROJECT"))

	// Initially no inclusions (but map is initialized)
	assert.Len(t, design.CartInclusions, 0)

	productID := uuid.New()
	location := LocationShowerNiche

	// Add cart inclusion
	design.AddCartInclusion(productID, location, true, 2)

	// Now should have inclusions
	assert.Len(t, design.CartInclusions, 1)

	// Verify inclusion
	key := CartInclusionKey{ProductID: productID, Location: location}
	cartInclusion, exists := design.CartInclusions[key]
	require.True(t, exists)
	assert.Equal(t, productID, cartInclusion.ProductID)
	assert.Equal(t, location, cartInclusion.Location)
	assert.True(t, cartInclusion.Include)
	assert.Equal(t, 2, cartInclusion.QuantityDiff)
}

func TestDesignClearCartInclusions(t *testing.T) {
	design := newTestDesign(entities.NewProjectId("TEST-PROJECT"))

	productID := uuid.New()
	location := LocationUnspecified

	// Add inclusion
	design.AddCartInclusion(productID, location, true, 1)
	assert.Len(t, design.CartInclusions, 1)

	// Clear cart inclusions
	design.ClearCartInclusions()
	assert.Len(t, design.CartInclusions, 0)
}

func TestDesignRemoveLastInclusion(t *testing.T) {
	design := newTestDesign(entities.NewProjectId("TEST-PROJECT"))

	productID := uuid.New()
	location := LocationUnspecified

	// Add inclusion
	design.AddCartInclusion(productID, location, true, 1)
	assert.Len(t, design.CartInclusions, 1)

	// Remove the only inclusion
	design.RemoveCartInclusion(productID, location)

	// Inclusions should be empty but not nil
	assert.Len(t, design.CartInclusions, 0)
}

func TestDesignSameProductDifferentLocations(t *testing.T) {
	design := newTestDesign(entities.NewProjectId("TEST-PROJECT"))

	productID := uuid.New()
	floorLocation := LocationFloor
	wallLocation := LocationWall

	// Add same product at different locations with different settings
	design.AddCartInclusion(productID, floorLocation, true, 2)
	design.AddCartInclusion(productID, wallLocation, false, -1)

	// Should have 2 inclusions for the same product
	assert.Len(t, design.CartInclusions, 2)

	// Verify floor inclusion
	floorKey := CartInclusionKey{ProductID: productID, Location: floorLocation}
	floorInclusion, exists := design.CartInclusions[floorKey]
	require.True(t, exists)
	assert.Equal(t, productID, floorInclusion.ProductID)
	assert.Equal(t, floorLocation, floorInclusion.Location)
	assert.True(t, floorInclusion.Include)
	assert.Equal(t, 2, floorInclusion.QuantityDiff)

	// Verify wall inclusion
	wallKey := CartInclusionKey{ProductID: productID, Location: wallLocation}
	wallInclusion, exists := design.CartInclusions[wallKey]
	require.True(t, exists)
	assert.Equal(t, productID, wallInclusion.ProductID)
	assert.Equal(t, wallLocation, wallInclusion.Location)
	assert.False(t, wallInclusion.Include)
	assert.Equal(t, -1, wallInclusion.QuantityDiff)

	// Remove only the floor inclusion
	design.RemoveCartInclusion(productID, floorLocation)
	assert.Len(t, design.CartInclusions, 1)

	// Wall inclusion should still exist
	wallInclusion, exists = design.CartInclusions[wallKey]
	require.True(t, exists)
	assert.Equal(t, wallLocation, wallInclusion.Location)
}
