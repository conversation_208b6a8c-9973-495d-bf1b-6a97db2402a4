package usecases

import (
	"net/url"
	"time"

	"github.com/google/uuid"
)

type Tagged struct {
	ColorScheme ColorScheme `json:"color_scheme"`
	Style       Style       `json:"style"`
}

// TODO: make the JSON field name capitalization consistently camelCase.
type Template struct {
	ID        uuid.UUID `json:"id"`
	LegacyId  *string   `json:"legacy_id,omitempty"`
	UpdatedAt time.Time `json:"updatedAt"`

	Tagged
	Name     string  `json:"name"`
	ImageURL url.URL `json:"image_url"`

	Description          string    `json:"description"`
	Inspiration          string    `json:"inspiration"`
	Atmosphere           []string  `json:"atmosphere"`
	ColorPalette         []string  `json:"color_palette"`
	MaterialPalette      []string  `json:"material_palette"`
	HighlightedBrandUrls []url.URL `json:"highlighted_brand_urls,omitempty"`

	FixedProductSelections
	ProductSelectionOptions
	TemplateProvenance

	// Additional fields from template_product_selections table
	WallTilePlacement  WallTilePlacement  `json:"wallTilePlacement"`
	WallpaperPlacement WallpaperPlacement `json:"wallpaperPlacement"`

	// Vanity scaling options
	VanityScalingOptions map[int]VanityScalingOption
}

type ProductSelectionOptions struct {
	AlcoveTub       uuid.UUID `json:"alcoveTub"`
	FreestandingTub uuid.UUID `json:"freestandingTub"`

	ShowerGlassFixed   uuid.UUID `json:"showerGlassFixed"`
	ShowerGlassSliding uuid.UUID `json:"showerGlassSliding"`

	ShowerSystemCombo uuid.UUID `json:"showerSystemFull"`
	ShowerSystemSolo  uuid.UUID `json:"showerSystemShower"`

	TubDoorFixed   uuid.UUID `json:"tubDoorFixed"`
	TubDoorSliding uuid.UUID `json:"tubDoorSliding"`
}

type TemplateProvenance struct {
	LightingBrand *string `json:"lighting_brand,omitempty"`
	PlumbingBrand *string `json:"plumbing_brand,omitempty"`
	ToiletBrand   *string `json:"toilet_brand,omitempty"`
	VanityBrand   *string `json:"vanity_brand,omitempty"`

	VanityStorage *string `json:"vanity_storage,omitempty"`
}

type VanityScalingOption struct {
	VanityProductID uuid.UUID
	FaucetProductID uuid.UUID
}
