package usecases_test

import (
	"database/sql"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestMergeDesigns_AdditionalCases(t *testing.T) {
	// Helper function to create a base design
	createBaseDesign := func() usecases.Design {
		return usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "TEST-PROJECT",
			Created:            time.Now().Add(-time.Hour),
			LastUpdated:        time.Now().Add(-time.Hour),
			Status:             usecases.Preview,
			WallpaperPlacement: usecases.NoWallpaper,
			WallTilePlacement:  usecases.NoWallTile,
			DesignOptions: usecases.DesignOptions{
				ColorScheme: &[]usecases.ColorScheme{usecases.Neutral}[0],
				Style:       &[]usecases.Style{usecases.Traditional}[0],
				Title:       sql.NullString{String: "Original Title", Valid: true},
				Description: sql.NullString{String: "Original Description", Valid: true},
			},
			ShowerGlassVisible: false,
			TubDoorVisible:     false,
			NichesVisible:      false,
		}
	}

	t.Run("should always update LastUpdated timestamp", func(t *testing.T) {
		existing := createBaseDesign()
		originalLastUpdated := existing.LastUpdated

		// Wait a small amount to ensure timestamp difference
		time.Sleep(1 * time.Millisecond)

		incoming := usecases.DesignDiff{
			ID:     existing.ID,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}

		result := usecases.MergeDesigns(existing, incoming)

		assert.True(t, result.LastUpdated.After(originalLastUpdated), "LastUpdated should always be updated")
	})

	t.Run("should merge pricing and lead time fields", func(t *testing.T) {
		existing := createBaseDesign()

		incoming := usecases.DesignDiff{
			ID: existing.ID,
			DesignOptions: usecases.DesignOptions{
				NumSKUs:           sql.NullInt32{Int32: 25, Valid: true},
				TotalPriceInCents: sql.NullInt32{Int32: 150000, Valid: true},
				LeadTimeDays:      sql.NullInt32{Int32: 14, Valid: true},
			},
		}

		result := usecases.MergeDesigns(existing, incoming)

		assert.Equal(t, int32(25), result.NumSKUs.Int32)
		assert.True(t, result.NumSKUs.Valid)
		assert.Equal(t, int32(150000), result.TotalPriceInCents.Int32)
		assert.True(t, result.TotalPriceInCents.Valid)
		assert.Equal(t, int32(14), result.LeadTimeDays.Int32)
		assert.True(t, result.LeadTimeDays.Valid)
	})

	t.Run("should handle wallpaper removal", func(t *testing.T) {
		existing := createBaseDesign()
		wallpaperId := uuid.New()
		existing.Wallpaper = &wallpaperId

		incoming := usecases.DesignDiff{
			ID:       existing.ID,
			Removals: map[string]any{"wallpaper": true},
		}

		result := usecases.MergeDesigns(existing, incoming)

		assert.Nil(t, result.Wallpaper, "Wallpaper should be removed")
	})

	t.Run("should handle wall tile removal", func(t *testing.T) {
		existing := createBaseDesign()
		wallTileId := uuid.New()
		existing.WallTile = &wallTileId

		incoming := usecases.DesignDiff{
			ID:       existing.ID,
			Removals: map[string]any{"wallTile": true},
		}

		result := usecases.MergeDesigns(existing, incoming)

		assert.Nil(t, result.WallTile, "Wall tile should be removed")
	})

	t.Run("should not change fields when incoming values are nil or invalid", func(t *testing.T) {
		existing := createBaseDesign()
		originalStatus := existing.Status
		originalColorScheme := existing.ColorScheme
		originalTitle := existing.Title

		incoming := usecases.DesignDiff{
			ID:     existing.ID,
			Status: nil, // Should not change
			DesignOptions: usecases.DesignOptions{
				ColorScheme: nil,                                      // Should not change
				Title:       sql.NullString{String: "", Valid: false}, // Should not change
			},
		}

		result := usecases.MergeDesigns(existing, incoming)

		assert.Equal(t, originalStatus, result.Status)
		assert.Equal(t, originalColorScheme, result.ColorScheme)
		assert.Equal(t, originalTitle, result.Title)
	})

	t.Run("should preserve existing values when not specified in diff", func(t *testing.T) {
		existing := createBaseDesign()
		originalID := existing.ID
		originalProjectID := existing.ProjectID
		originalCreated := existing.Created

		incoming := usecases.DesignDiff{
			ID: existing.ID,
			DesignOptions: usecases.DesignOptions{
				Title: sql.NullString{String: "Only Title Changed", Valid: true},
			},
		}

		result := usecases.MergeDesigns(existing, incoming)

		// These should remain unchanged
		assert.Equal(t, originalID, result.ID)
		assert.Equal(t, originalProjectID, result.ProjectID)
		assert.Equal(t, originalCreated, result.Created)
		assert.Equal(t, existing.Status, result.Status)
		assert.Equal(t, existing.ColorScheme, result.ColorScheme)
		assert.Equal(t, existing.Description, result.Description)

		// Only title should change
		assert.Equal(t, "Only Title Changed", result.Title.String)
	})

	t.Run("should handle complex merge with multiple changes", func(t *testing.T) {
		existing := createBaseDesign()
		originalLastUpdated := existing.LastUpdated

		floorTileId := uuid.New()
		lightingId := uuid.New()
		newColorScheme := usecases.Bold

		incoming := usecases.DesignDiff{
			ID:     existing.ID,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
			DesignOptions: usecases.DesignOptions{
				ColorScheme: &newColorScheme,
				Title:       sql.NullString{String: "Complex Design", Valid: true},
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: &floorTileId,
					Lighting:  &lightingId,
				},
				NumSKUs:           sql.NullInt32{Int32: 30, Valid: true},
				TotalPriceInCents: sql.NullInt32{Int32: 200000, Valid: true},
			},
			WallpaperPlacement: &[]usecases.WallpaperPlacement{usecases.AllWalls}[0],
			ShowerGlassVisible: sql.NullBool{Bool: true, Valid: true},
			Removals:           map[string]any{"wallTile": true},
		}

		result := usecases.MergeDesigns(existing, incoming)

		// Verify all changes were applied
		assert.Equal(t, usecases.Fave, result.Status)
		assert.Equal(t, &newColorScheme, result.ColorScheme)
		assert.Equal(t, "Complex Design", result.Title.String)
		assert.Equal(t, &floorTileId, result.FloorTile)
		assert.Equal(t, &lightingId, result.Lighting)
		assert.Equal(t, int32(30), result.NumSKUs.Int32)
		assert.Equal(t, int32(200000), result.TotalPriceInCents.Int32)
		assert.Equal(t, usecases.AllWalls, result.WallpaperPlacement)
		assert.True(t, result.ShowerGlassVisible)
		assert.Nil(t, result.WallTile)
		assert.True(t, result.LastUpdated.After(originalLastUpdated))

		// Verify unchanged fields
		assert.Equal(t, existing.ID, result.ID)
		assert.Equal(t, existing.ProjectID, result.ProjectID)
		assert.Equal(t, existing.Created, result.Created)
		assert.Equal(t, existing.Description, result.Description)
	})
}
