package usecases

import (
	"github.com/google/uuid"
)

// Location represents the physical location where a product is placed in the room design.
// This corresponds to the design.location_type enum in the database.
type Location string

const (
	LocationUnspecified Location = "Unspecified"
	LocationFloor       Location = "Floor"
	LocationShowerFloor Location = "ShowerFloor"
	LocationShowerNiche Location = "ShowerNiche"
	LocationShowerWall  Location = "ShowerWall"
	LocationWall        Location = "Wall"
)

// CartInclusion represents a user's cart inclusion selection for a product in the design materials list.
// This is a value object that captures the frontend's key-value records for cart inclusion state.
type CartInclusion struct {
	ProductID    uuid.UUID `json:"productId"`
	Location     Location  `json:"location"`
	Include      bool      `json:"include"`
	QuantityDiff int       `json:"quantityDiff"`
}

// CartInclusionKey represents the composite key for cart inclusions (productId + location).
// Since the database uses a composite primary key of (room_design_id, product_id, location),
// we need to track inclusions by both product ID and location.
type CartInclusionKey struct {
	ProductID uuid.UUID
	Location  Location
}

// CartInclusions represents a collection of cart inclusion selections keyed by product ID and location.
type CartInclusions map[CartInclusionKey]CartInclusion

// Add adds or updates a cart inclusion for a product at a specific location.
func (ci CartInclusions) Add(productID uuid.UUID, location Location, include bool, quantityDiff int) {
	key := CartInclusionKey{
		ProductID: productID,
		Location:  location,
	}
	ci[key] = CartInclusion{
		ProductID:    productID,
		Location:     location,
		Include:      include,
		QuantityDiff: quantityDiff,
	}
}

// Remove removes a cart inclusion for a product at a specific location.
func (ci CartInclusions) Remove(productID uuid.UUID, location Location) {
	key := CartInclusionKey{
		ProductID: productID,
		Location:  location,
	}
	delete(ci, key)
}

// Validate validates the cart inclusion data.
func (c CartInclusion) Validate() error {
	// TODO: It would make sense to move UUID validation to a helper function that can be reused across the project.
	// Validate product ID
	var zeroUUID uuid.UUID
	if c.ProductID == zeroUUID || c.ProductID == uuid.Nil {
		return ErrInvalidPayload
	}

	return nil
}

// IsRedundant checks if this cart inclusion represents a redundant state.
// Returns true if quantity adjustment is 0 and item is included (effectively no change).
func (c CartInclusion) IsRedundant() bool {
	return c.QuantityDiff == 0 && c.Include
}

// Note: One cart inclusion method has been implemented on Design in design.go
// - ClearCartInclusions()
