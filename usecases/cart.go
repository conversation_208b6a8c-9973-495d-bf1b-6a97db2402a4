package usecases

import (
	"time"

	"github.com/google/uuid"
)

// Location represents the physical location where a product is placed in the room design.
// This corresponds to the design.location_type enum in the database.
type Location string

const (
	LocationUnspecified Location = "Unspecified"
	LocationFloor       Location = "Floor"
	LocationShowerFloor Location = "ShowerFloor"
	LocationShowerNiche Location = "ShowerNiche"
	LocationShowerWall  Location = "ShowerWall"
	LocationWall        Location = "Wall"
)

// CartInclusion represents a user's cart inclusion selection for a product in the design materials list.
// This is a value object that captures the frontend's key-value records for cart inclusion state.
type CartInclusion struct {
	ProductID    uuid.UUID `json:"productId"`
	Location     Location  `json:"location"`
	Include      bool      `json:"include"`
	QuantityDiff int       `json:"quantityDiff"`
}

// CartInclusionKey represents the composite key for cart inclusions (productId + location).
// Since the database uses a composite primary key of (room_design_id, product_id, location),
// we need to track inclusions by both product ID and location.
type CartInclusionKey struct {
	ProductID uuid.UUID
	Location  Location
}

// CartInclusions represents a collection of cart inclusion selections keyed by product ID and location.
type CartInclusions map[CartInclusionKey]CartInclusion

// AddCartInclusion adds or updates a cart inclusion for a product at a specific location.
func (d *Design) AddCartInclusion(productID uuid.UUID, location Location, include bool, quantityDiff int) {
	key := CartInclusionKey{
		ProductID: productID,
		Location:  location,
	}
	d.CartInclusions[key] = CartInclusion{
		ProductID:    productID,
		Location:     location,
		Include:      include,
		QuantityDiff: quantityDiff,
	}
	d.LastUpdated = time.Now()
}

// RemoveCartInclusion removes a cart inclusion for a product at a specific location.
func (d *Design) RemoveCartInclusion(productID uuid.UUID, location Location) {
	key := CartInclusionKey{
		ProductID: productID,
		Location:  location,
	}
	delete(d.CartInclusions, key)
	d.LastUpdated = time.Now()
}

// Note: Cart inclusion methods may be implemented on Design in design.go
// - ClearCartInclusions()
