package usecases

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type RenditionSaver struct {
	renditionRepo renditionRepository
}

func NewRenditionSaver(renditionRepo renditionRepository) *RenditionSaver {
	if IsNil(renditionRepo) {
		panic("renditionRepo cannot be nil")
	}
	return &RenditionSaver{renditionRepo: renditionRepo}
}

func (r *RenditionSaver) Save(ctx context.Context, designId uuid.UUID, rendition entities.Rendition, presenter OutcomePresenter) {
	id, err := r.renditionRepo.InsertRendition(ctx, designId, rendition)
	if err != nil {
		presenter.PresentError(err)
	}
	rendition.Id = id
	presenter.ConveySuccess()
}
