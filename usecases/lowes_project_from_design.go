package usecases

import (
	"context"
	"fmt"
	"log"

	"github.com/google/uuid"
)

type ProjectSynthesizer struct {
	designRepo designRepositoryReplica
	monolith   monolith
}

func NewProjectSynthesizer(designRepo designRepositoryReplica, monolith monolith) *ProjectSynthesizer {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	if IsNil(monolith) {
		panic("monolith cannot be nil")
	}
	return &ProjectSynthesizer{designRepo: designRepo, monolith: monolith}
}

type ProjectPresenter interface {
	PresentDesign(ctx context.Context, design Design, areaSqFt float64)
	PresentError(err error)
}

func (ps *ProjectSynthesizer) SynthesizeProjectFromDesign(ctx context.Context, presenter ProjectPresenter, designId uuid.UUID) {
	design, err := ps.designRepo.ReadDesign(ctx, designId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	layout, err := ps.monolith.GetLayoutForProject(ctx, design.ProjectID)
	if err != nil {
		log.Printf("failed to fetch project layout for design %s: %v", designId, err)
		presenter.PresentError(fmt.Errorf("failed to fetch project layout for design %s: %w", designId, err))
		return
	}
	presenter.PresentDesign(ctx, design, layout.AreaSqFt)
}
