package usecases_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Fake implementations are in test_helpers_test.go

func TestNewProjectIdRetriever(t *testing.T) {
	t.Run("should create retriever with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		retriever := usecases.NewProjectIdRetriever(repo)
		assert.NotNil(t, retriever)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewProjectIdRetriever(nil)
		})
	})
}

func TestProjectIdRetriever_RetrieveProjectIdForDesign(t *testing.T) {
	ctx := context.Background()
	designId := uuid.New()
	projectId := entities.NewProjectId("PRJ-12345")

	t.Run("should present project ID when found", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeProjectIdPresenter()
		retriever := usecases.NewProjectIdRetriever(repo)

		// First, save a design with the project ID to the repository
		testDesign := usecases.Design{
			ID:        designId,
			ProjectID: projectId,
			Status:    usecases.Preview,
		}
		_, err := repo.UpsertDesign(ctx, testDesign)
		require.NoError(t, err)

		retriever.RetrieveProjectIdForDesign(ctx, presenter, designId)

		// Verify the project ID was presented
		AssertPresentProjectIdCalled(t, presenter, projectId)
		AssertPresentErrorNotCalled(t, presenter)
	})

	t.Run("should present error when design not found", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeProjectIdPresenter()
		retriever := usecases.NewProjectIdRetriever(repo)

		// Don't save any design to the repository
		nonExistentId := uuid.New()
		retriever.RetrieveProjectIdForDesign(ctx, presenter, nonExistentId)

		// Verify error was presented
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Empty(t, presenter.PresentProjectIdCalls, "Expected PresentProjectId not to be called")
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		// For integration testing with FakeRelDb, we test normal operation
		// since FakeRelDb doesn't typically fail
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeProjectIdPresenter()
		retriever := usecases.NewProjectIdRetriever(repo)

		// Test with a design that doesn't exist
		nonExistentId := uuid.New()
		retriever.RetrieveProjectIdForDesign(ctx, presenter, nonExistentId)

		// Verify error was presented
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Empty(t, presenter.PresentProjectIdCalls, "Expected PresentProjectId not to be called")
	})
}
