package usecases

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

// RenditionUpdater updates a rendition's status and URL.
// It also updates the associated design's title & description when the rendition is completed.
type RenditionUpdater struct {
	renditionRepo renditionRepository
	designRepo    designRepository
	catalog       catalog
	genAI         genAI
	logger        *slog.Logger
}

func NewRenditionUpdater(renditionRepo renditionRepository, genAI genAI, designRepo designRepository, catalog catalog, logger *slog.Logger) *RenditionUpdater {
	if IsNil(renditionRepo) {
		panic("renditionRepo cannot be nil")
	}
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	if IsNil(catalog) {
		panic("catalog cannot be nil")
	}
	if IsNil(genAI) {
		panic("genAI cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &RenditionUpdater{renditionRepo: renditionRepo, genAI: genAI, designRepo: designRepo, catalog: catalog, logger: logger}
}

func (ru *RenditionUpdater) UpdateRendition(ctx context.Context,
	presenter OutcomePresenter, diff entities.RenditionDiff) {

	var zeroUUID uuid.UUID
	if diff.Id == zeroUUID || diff.Id == uuid.Nil {
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	if diff.Status == entities.RenditionCompleted && diff.URL == nil {
		ru.logger.ErrorContext(ctx, "Rendition URL was empty with Completed status", slog.String("renditionId", diff.Id.String()))
		presenter.PresentError(ErrInvalidPayload)
		return
	}

	if err := ru.renditionRepo.UpdateRendition(ctx, diff); err != nil {
		presenter.PresentError(err)
		return
	}

	if diff.Status == entities.RenditionCompleted {
		if err := ru.regenerateProseForDesign(ctx, diff.Id); err != nil {
			ru.logger.ErrorContext(ctx, "Failed to regenerate design prose.",
				slog.String("error", err.Error()), slog.String("renditionId", diff.Id.String()))
		}
	}

	presenter.ConveySuccess()
}

func (ru *RenditionUpdater) regenerateProseForDesign(ctx context.Context, renditionId uuid.UUID) error {
	renditions, err := ru.renditionRepo.Renditions(ctx, []uuid.UUID{renditionId})
	if err != nil || len(renditions) == 0 {
		return fmt.Errorf("rendition to be marked as completed does not exist: %w", err)
	}
	rendition := renditions[0]
	if rendition.DesignId == nil {
		return fmt.Errorf("rendition to be marked completed is not associated with a design")
	}
	design, err := ru.designRepo.ReadDesign(ctx, *rendition.DesignId)
	if err != nil {
		return fmt.Errorf("unable to find associated design (%s) when marking rendition completed: %w", rendition.DesignId, err)
	}
	ru.logger.InfoContext(ctx, "Generating title & description for design...",
		slog.String("projectId", design.ProjectID.String()), slog.String("designId", design.ID.String()))
	productDescriptions, err := ru.catalog.ProductDescriptionsForDesign(ctx, design)
	if err != nil {
		ru.logger.ErrorContext(ctx, "Failed to build product descriptions for design",
			slog.String("designId", design.ID.String()), slog.String("projectId", design.ProjectID.String()),
			slog.String("error", err.Error()))
		return err
	}
	title, description, err := ru.genAI.GenerateDesignTitleAndDescription(ctx, productDescriptions)
	if err != nil {
		ru.logger.ErrorContext(ctx, "Failed to generate title & description for design",
			slog.String("designId", design.ID.String()), slog.String("projectId", design.ProjectID.String()),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to generate title & description for design (%s) of completed rendition: %w", design.ID, err)
	}
	design.Title = sql.NullString{String: title, Valid: true}
	design.Description = sql.NullString{String: description, Valid: true}
	if _, err := ru.designRepo.UpsertDesign(ctx, design); err != nil {
		return fmt.Errorf("unable to save design (%s) of completed rendition with new title & description: %w", design.ID, err)
	}
	return nil
}
