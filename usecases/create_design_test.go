package usecases_test

import (
	"context"
	"database/sql"
	"net/url"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Fake implementations are in test_helpers_test.go

func TestNewDesignCreater(t *testing.T) {
	t.Run("should create creater with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		ai := NewFakeGenAI()
		creater := usecases.NewDesignCreater(repo, repo, catalog, ai, nil)
		assert.NotNil(t, creater)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		ai := NewFakeGenAI()
		assert.Panics(t, func() {
			usecases.NewDesignCreater(nil, repo, catalog, ai, nil)
		})
	})

	t.Run("should panic with nil rendition repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		ai := NewFakeGenAI()
		assert.Panics(t, func() {
			usecases.NewDesignCreater(repo, nil, catalog, ai, nil)
		})
	})

	t.Run("should panic with nil AI", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		assert.Panics(t, func() {
			usecases.NewDesignCreater(repo, repo, catalog, nil, nil)
		})
	})
}

func TestDesignCreater_CreateDesign(t *testing.T) {
	ctx := context.Background()
	projectId := entities.ProjectId("TEST-PROJECT")

	testDesign := usecases.Design{
		ProjectID:          projectId,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
		DesignOptions: usecases.DesignOptions{
			Title:       sql.NullString{String: "Fake Title", Valid: true},
			Description: sql.NullString{String: "Fake Description", Valid: true},
		},
	}

	t.Run("should create design successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		presenter := NewFakeDesignMutationOutcomePresenter()
		ai := NewFakeGenAI()
		creater := usecases.NewDesignCreater(repo, repo, catalog, ai, nil)

		creater.CreateDesign(ctx, presenter, testDesign)

		// Verify the design was created and success was conveyed
		assert.NotEmpty(t, presenter.ConveySuccessWithResourceCalls, "Expected ConveySuccessWithResource to be called")
		AssertPresentErrorNotCalled(t, presenter)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		failingRepo := NewFailingDesignRepository()
		failingRepo.ShouldFail = true
		catalog := gateways.NewFakeCatalog()
		presenter := NewFakeDesignMutationOutcomePresenter()
		ai := NewFakeGenAI()
		creater := usecases.NewDesignCreater(failingRepo, failingRepo, catalog, ai, nil)

		creater.CreateDesign(ctx, presenter, testDesign)

		// Verify error was presented
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Empty(t, presenter.ConveySuccessWithResourceCalls, "Expected ConveySuccessWithResource not to be called")
	})

	t.Run("should handle design with existing ID", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		presenter := NewFakeDesignMutationOutcomePresenter()
		ai := NewFakeGenAI()
		creater := usecases.NewDesignCreater(repo, repo, catalog, ai, nil)

		existingId := uuid.New()
		designWithId := testDesign
		designWithId.ID = existingId

		creater.CreateDesign(ctx, presenter, designWithId)

		// Verify the design was created successfully
		assert.NotEmpty(t, presenter.ConveySuccessWithResourceCalls, "Expected ConveySuccessWithResource to be called")
		AssertPresentErrorNotCalled(t, presenter)
	})

	t.Run("should handle design with all optional fields", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		presenter := NewFakeDesignMutationOutcomePresenter()
		ai := NewFakeGenAI()
		creater := usecases.NewDesignCreater(repo, repo, catalog, ai, nil)

		// Create a design with all optional fields populated
		faucetId := uuid.New()
		floorTileId := uuid.New()
		lightingId := uuid.New()
		mirrorId := uuid.New()
		paintId := uuid.New()
		toiletId := uuid.New()
		vanityId := uuid.New()

		fullDesign := usecases.Design{
			ProjectID:          projectId,
			Status:             usecases.Fave,
			WallpaperPlacement: usecases.VanityWall,
			WallTilePlacement:  usecases.HalfWall,
			DesignOptions: usecases.DesignOptions{
				Title:            sql.NullString{String: "Fake Title", Valid: true},
				Description:      sql.NullString{String: "Fake Description", Valid: true},
				ColorScheme:      &[]usecases.ColorScheme{usecases.Bold}[0],
				Style:            &[]usecases.Style{usecases.Modern}[0],
				FloorTilePattern: &[]usecases.TilePattern{usecases.Herringbone}[0],
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: &floorTileId,
					Lighting:  &lightingId,
					Mirror:    &mirrorId,
					Paint:     &paintId,
					Toilet:    &toiletId,
				},
				Faucet: &faucetId,
				Vanity: &vanityId,
			},
			ShowerGlassVisible: true,
			TubDoorVisible:     true,
			NichesVisible:      true,
		}

		creater.CreateDesign(ctx, presenter, fullDesign)

		// Verify the design was created successfully
		assert.NotEmpty(t, presenter.ConveySuccessWithResourceCalls, "Expected ConveySuccessWithResource to be called")
		AssertPresentErrorNotCalled(t, presenter)
	})

	t.Run("should handle different design statuses", func(t *testing.T) {
		statuses := []usecases.DesignStatus{
			usecases.Preview,
			usecases.Fave,
			usecases.Archived,
		}

		for _, status := range statuses {
			t.Run("status_"+string(status), func(t *testing.T) {
				repo := gateways.NewFakeRelDb()
				catalog := gateways.NewFakeCatalog()
				presenter := NewFakeDesignMutationOutcomePresenter()
				ai := NewFakeGenAI()
				creater := usecases.NewDesignCreater(repo, repo, catalog, ai, nil)

				design := testDesign
				design.Status = status

				creater.CreateDesign(ctx, presenter, design)

				// Verify the design was created successfully
				assert.NotEmpty(t, presenter.ConveySuccessWithResourceCalls, "Expected ConveySuccessWithResource to be called")
				AssertPresentErrorNotCalled(t, presenter)
			})
		}
	})
	t.Run("should save renditions when design includes them", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		presenter := NewFakeDesignMutationOutcomePresenter()
		ai := NewFakeGenAI()
		creater := usecases.NewDesignCreater(repo, repo, catalog, ai, nil)

		// Create a design with renditions
		testURL1, _ := url.Parse("https://example.com/rendition1.jpg")
		testURL2, _ := url.Parse("https://example.com/rendition2.jpg")

		designWithRenditions := testDesign
		designWithRenditions.Renditions = []entities.Rendition{
			{
				Id:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
				Status:    entities.RenditionCompleted,
				URL:       testURL1,
			},
			{
				Id:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
				Status:    entities.RenditionPending,
				URL:       testURL2,
			},
		}

		creater.CreateDesign(ctx, presenter, designWithRenditions)

		// Verify the design was created successfully
		assert.NotEmpty(t, presenter.ConveySuccessWithResourceCalls, "Expected ConveySuccessWithResource to be called")
		AssertPresentErrorNotCalled(t, presenter)

		// Verify the returned design has the renditions with updated IDs
		returnedDesign := presenter.ConveySuccessWithResourceCalls[0].Design
		assert.Len(t, returnedDesign.Renditions, 2, "Expected 2 renditions in returned design")

		// Verify renditions were actually saved in the repository
		savedRenditions, err := repo.RenditionsForDesign(ctx, returnedDesign.ID)
		assert.NoError(t, err, "Should be able to retrieve saved renditions")
		assert.Len(t, savedRenditions, 2, "Expected 2 renditions to be saved")

		// Verify the rendition IDs match between returned design and saved renditions
		for i, savedRendition := range savedRenditions {
			assert.Equal(t, returnedDesign.Renditions[i].Id, savedRendition.Id, "Rendition IDs should match")
		}
	})
	t.Run("should present error when rendition saving fails", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		failingRenditionRepo := NewFailingRenditionRepository()
		failingRenditionRepo.ShouldFail = true
		presenter := NewFakeDesignMutationOutcomePresenter()
		ai := NewFakeGenAI()
		creater := usecases.NewDesignCreater(repo, failingRenditionRepo, catalog, ai, nil)

		// Create a design with renditions
		testURL, _ := url.Parse("https://example.com/rendition.jpg")
		designWithRenditions := testDesign
		designWithRenditions.Renditions = []entities.Rendition{
			{
				Id:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
				Status:    entities.RenditionCompleted,
				URL:       testURL,
			},
		}

		creater.CreateDesign(ctx, presenter, designWithRenditions)

		// Verify error was presented when rendition saving fails
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Empty(t, presenter.ConveySuccessWithResourceCalls, "Expected ConveySuccessWithResource not to be called")
	})
}
