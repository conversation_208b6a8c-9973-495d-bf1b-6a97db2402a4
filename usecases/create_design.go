package usecases

import (
	"context"
	"database/sql"
	"log/slog"
)

// TODO: do we even need this type at all; seems redundant with DesignSaver.
type DesignCreater struct {
	designRepo    designRepository
	renditionRepo renditionRepository
	catalog       catalog
	genAI         genAI
	logger        *slog.Logger
}

func NewDesignCreater(designRepo designRepository, renditionRepo renditionRepository, catalog catalog, genAI genAI, logger *slog.Logger) *DesignCreater {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	if IsNil(renditionRepo) {
		panic("renditionRepo cannot be nil")
	}
	if IsNil(catalog) {
		panic("catalog cannot be nil")
	}
	if IsNil(genAI) {
		panic("genAI cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignCreater{designRepo: designRepo, renditionRepo: renditionRepo, catalog: catalog, genAI: genAI, logger: logger}
}

func (dc *DesignCreater) CreateDesign(ctx context.Context, presenter DesignMutationOutcomePresenter, design Design) {
	if !design.Description.Valid {
		dc.logger.InfoContext(ctx, "Generating title & description for new design",
			slog.String("projectId", design.ProjectID.String()), slog.String("designId", design.ID.String()))
		productDescriptions, err := dc.catalog.ProductDescriptionsForDesign(ctx, design)
		if err != nil {
			dc.logger.ErrorContext(ctx, "Failed to build product descriptions for design",
				slog.String("designId", design.ID.String()), slog.String("projectId", design.ProjectID.String()),
				slog.String("error", err.Error()))
		} else {
			title, description, err := dc.genAI.GenerateDesignTitleAndDescription(ctx, productDescriptions)
			if err != nil {
				// Log the error but continue to save the design without the generated content.
				dc.logger.ErrorContext(ctx, "Failed to generate title & description for new design",
					slog.String("designId", design.ID.String()), slog.String("projectId", design.ProjectID.String()),
					slog.String("error", err.Error()))
			} else {
				design.Title = sql.NullString{String: title, Valid: true}
				design.Description = sql.NullString{String: description, Valid: true}
			}
		}
	} else if !design.Title.Valid {
		design.Title = sql.NullString{String: "Custom design", Valid: true}
	}

	var err error
	if design.ID, err = dc.designRepo.UpsertDesign(ctx, design); err != nil {
		presenter.PresentError(err)
		return
	}

	// Save any renditions that are included with the design
	if len(design.Renditions) > 0 {
		dc.logger.InfoContext(ctx, "Saving renditions for new design",
			slog.String("designId", design.ID.String()),
			slog.Int("renditionCount", len(design.Renditions)))

		for i, rendition := range design.Renditions {
			renditionId, err := dc.renditionRepo.InsertRendition(ctx, design.ID, rendition)
			if err != nil {
				dc.logger.ErrorContext(ctx, "Failed to save rendition for new design",
					slog.String("designId", design.ID.String()),
					slog.String("error", err.Error()))
				presenter.PresentError(err)
				return
			}
			// Update the rendition ID & timestamp in the design for the response
			design.Renditions[i].Id = renditionId
			design.Renditions[i].UpdatedAt = rendition.UpdatedAt
		}
	}

	dc.logger.InfoContext(ctx, "Created new design.",
		slog.String("designId", design.ID.String()), slog.String("projectId", design.ProjectID.String()))
	presenter.ConveySuccessWithResource(design, Created)
}
