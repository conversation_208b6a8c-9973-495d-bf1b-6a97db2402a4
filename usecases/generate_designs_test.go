package usecases_test

import (
	"context"
	"net/url"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func createTestDesignGenerator() *usecases.DesignGenerator {
	// For testing the existing GenerateDesignsFromTemplates method, we don't need real implementations
	// since it doesn't use the templateRepo, monolith, or rooms dependencies.
	return usecases.NewDesignGenerator(
		&FakeTemplateRepo{}, NewFakeMonolith(), gateways.NewRooms([]byte(`{}`)), gateways.NewFakeProductSearch(), NewFakeGenAI(),
		nil)
}

type FakeTemplateRepo struct {
	templatesToReturn []usecases.Template
	shouldFail        bool
	errorToReturn     error
}

func (f *FakeTemplateRepo) ReadTemplate(ctx context.Context, templateId uuid.UUID) (usecases.Template, error) {
	if f.shouldFail {
		return usecases.Template{}, f.errorToReturn
	}
	return usecases.Template{}, nil
}

func (f *FakeTemplateRepo) ReadTemplateByLegacyId(ctx context.Context, legacyId string) (usecases.Template, error) {
	if f.shouldFail {
		return usecases.Template{}, f.errorToReturn
	}
	return usecases.Template{}, nil
}

func (f *FakeTemplateRepo) ReadAllTemplates(ctx context.Context) ([]usecases.Template, error) {
	if f.shouldFail {
		return nil, f.errorToReturn
	}
	return f.templatesToReturn, nil
}

func (f *FakeTemplateRepo) TemplatesById(ctx context.Context, templateIds []uuid.UUID) ([]usecases.Template, error) {
	if f.shouldFail {
		return nil, f.errorToReturn
	}
	return f.templatesToReturn, nil
}

func (r *FakeTemplateRepo) PanoramicImagesForTemplates(ctx context.Context, templateIds []uuid.UUID) (map[uuid.UUID]url.URL, error) {
	return map[uuid.UUID]url.URL{}, nil
}

func TestDesignGenerator_GenerateDesignsFromTemplates(t *testing.T) {
	ctx := context.Background()

	t.Run("should generate designs from templates successfully", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		roomLayout := createTestRoomLayout()
		templates := []usecases.Template{
			createTestTemplate("Modern Template", usecases.Modern, usecases.Bold),
			createTestTemplate("Traditional Template", usecases.Traditional, usecases.Neutral),
		}
		panoramicImages := map[uuid.UUID]url.URL{
			templates[0].ID: {Scheme: "https", Host: "example.com", Path: "/modern.jpg"},
			templates[1].ID: {Scheme: "https", Host: "example.com", Path: "/traditional.jpg"},
		}

		generator.GenerateDesignsFromTemplates(ctx, roomLayout, templates, panoramicImages, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 2)

		// Verify first design
		design1 := designs[0]
		assert.Equal(t, "Modern Template", design1.Title.String)
		assert.Equal(t, usecases.Modern, *design1.Style)
		assert.Equal(t, usecases.Bold, *design1.ColorScheme)
		assert.Equal(t, "/modern.jpg", design1.Renditions[0].URL.Path)

		// Verify second design
		design2 := designs[1]
		assert.Equal(t, "Traditional Template", design2.Title.String)
		assert.Equal(t, usecases.Traditional, *design2.Style)
		assert.Equal(t, usecases.Neutral, *design2.ColorScheme)
		assert.Equal(t, "/traditional.jpg", design2.Renditions[0].URL.Path)
	})

	t.Run("should handle empty templates list", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		roomLayout := createTestRoomLayout()
		templates := []usecases.Template{}
		panoramicImages := map[uuid.UUID]url.URL{}

		generator.GenerateDesignsFromTemplates(ctx, roomLayout, templates, panoramicImages, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		assert.Empty(t, designs)
	})

	t.Run("should panic with nil presenter", func(t *testing.T) {
		generator := createTestDesignGenerator()
		roomLayout := createTestRoomLayout()
		templates := []usecases.Template{createTestTemplate("Test", usecases.Modern, usecases.Bold)}
		panoramicImages := map[uuid.UUID]url.URL{}

		assert.Panics(t, func() {
			generator.GenerateDesignsFromTemplates(ctx, roomLayout, templates, panoramicImages, nil)
		})
	})

	t.Run("should apply shower glass based on wet area glass type", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		// Create room layout with sliding shower glass
		roomLayout := createTestRoomLayoutWithShowerGlass(entities.SlidingShowerEnclosure)
		template := createTestTemplateWithProductOptions()
		templates := []usecases.Template{template}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromTemplates(ctx, roomLayout, templates, panoramicImages, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		assert.Equal(t, &template.ShowerGlassSliding, design.ShowerGlass)
	})

	t.Run("should apply tub door based on alcove tub door type", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		// Create room layout with fixed tub door
		roomLayout := createTestRoomLayoutWithAlcoveTub(entities.FixedShowerEnclosure)
		template := createTestTemplateWithProductOptions()
		templates := []usecases.Template{template}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromTemplates(ctx, roomLayout, templates, panoramicImages, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		assert.Equal(t, &template.AlcoveTub, design.Tub)
		assert.Equal(t, &template.TubDoorFixed, design.TubDoor)
	})

	t.Run("should apply vanity scaling based on max length", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		// Create room layout with vanity max length
		maxLength := 48.0
		roomLayout := createTestRoomLayoutWithVanity(&maxLength)
		template := createTestTemplateWithVanityScaling()
		templates := []usecases.Template{template}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromTemplates(ctx, roomLayout, templates, panoramicImages, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		scalingOption := template.VanityScalingOptions[48]
		assert.Equal(t, &scalingOption.VanityProductID, design.Vanity)
		assert.Equal(t, &scalingOption.FaucetProductID, design.Faucet)
	})

	t.Run("should select largest vanity that fits within max length", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		// Create room layout with vanity max length of 50 inches
		maxLength := 50.0
		roomLayout := createTestRoomLayoutWithVanity(&maxLength)

		// Create template with multiple vanity options: 36", 48", 60"
		template := createTestTemplateWithMultipleVanityScaling()
		templates := []usecases.Template{template}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromTemplates(ctx, roomLayout, templates, panoramicImages, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		// Should select the 48" option (largest that fits in 50")
		scalingOption := template.VanityScalingOptions[48]
		assert.Equal(t, &scalingOption.VanityProductID, design.Vanity)
		assert.Equal(t, &scalingOption.FaucetProductID, design.Faucet)
	})

	t.Run("should not select vanity when none fit within max length", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		// Create room layout with very small vanity max length
		maxLength := 30.0
		roomLayout := createTestRoomLayoutWithVanity(&maxLength)

		// Create template with vanity options that are all too large: 36", 48", 60"
		template := createTestTemplateWithMultipleVanityScaling()
		templates := []usecases.Template{template}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromTemplates(ctx, roomLayout, templates, panoramicImages, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		// Should not select any vanity or faucet
		assert.Nil(t, design.Vanity)
		assert.Nil(t, design.Faucet)
	})

	t.Run("should set niches visible when walls have niches", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		roomLayout := createTestRoomLayoutWithNiches()
		template := createTestTemplateWithProductOptions()
		templates := []usecases.Template{template}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromTemplates(ctx, roomLayout, templates, panoramicImages, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		assert.Equal(t, template.ShowerWallTile, design.NicheTile)
	})
}

func TestDesignGenerator_GenerateDesignViaAI(t *testing.T) {
	ctx := context.Background()

	t.Run("should generate design with products from all categories", func(t *testing.T) {
		// Setup
		templateRepo := &FakeTemplateRepo{}
		monolith := NewFakeMonolith()
		rooms := gateways.NewRooms([]byte(`{}`))
		productSearch := gateways.NewFakeProductSearch()
		genAI := NewFakeGenAI()
		presenter := NewFakeDesignsPresenter()

		generator := usecases.NewDesignGenerator(templateRepo, monolith, rooms, productSearch, genAI, nil)

		// Setup product search results for each category
		toiletId := uuid.New()
		productSearch.AddResults(string(usecases.CategoryToilet), []uuid.UUID{toiletId})

		mirrorId := uuid.New()
		productSearch.AddResults(string(usecases.CategoryMirror), []uuid.UUID{mirrorId})

		lightingId := uuid.New()
		productSearch.AddResults(string(usecases.CategoryLighting), []uuid.UUID{lightingId})

		paintId := uuid.New()
		productSearch.AddResults(string(usecases.CategoryPaint), []uuid.UUID{paintId})

		shelvingId := uuid.New()
		productSearch.AddResults(string(usecases.CategoryShelving), []uuid.UUID{shelvingId})

		floorTileId := uuid.New()
		productSearch.AddResults(string(usecases.CategoryTile), []uuid.UUID{floorTileId})

		faucetId := uuid.New()
		productSearch.AddResults(string(usecases.CategoryFaucet), []uuid.UUID{faucetId})

		vanityId := uuid.New()
		productSearch.AddResults(string(usecases.CategoryVanity), []uuid.UUID{vanityId})

		wallpaperId := uuid.New()
		productSearch.AddResults(string(usecases.CategoryWallpaper), []uuid.UUID{wallpaperId})

		// Create a test room layout
		roomLayout := createTestRoomLayout()

		// Execute
		generator.GenerateDesignViaAI(ctx, roomLayout, "Something fancy", presenter)

		// Verify
		require.Len(t, presenter.PresentDesignCalls, 1)
		design := presenter.PresentDesignCalls[0].Design
		assert.NotEqual(t, uuid.Nil, design.ID, "Should have generated ID")

		// Verify all products are populated
		assert.Equal(t, &mirrorId, design.Mirror, "Should have mirror")
		assert.Equal(t, &lightingId, design.Lighting, "Should have lighting")
		assert.Equal(t, &paintId, design.Paint, "Should have paint")
		assert.Equal(t, &floorTileId, design.FloorTile, "Should have floor tile")
		assert.Equal(t, &faucetId, design.Faucet, "Should have faucet")
		assert.Equal(t, &vanityId, design.Vanity, "Should have vanity")
		assert.Equal(t, &wallpaperId, design.Wallpaper, "Should have wallpaper")
	})

	t.Run("should handle product search errors gracefully", func(t *testing.T) {
		// Setup
		templateRepo := &FakeTemplateRepo{}
		monolith := NewFakeMonolith()
		rooms := gateways.NewRooms([]byte(`{}`))
		productSearch := gateways.NewFakeProductSearch()
		genAI := NewFakeGenAI()
		presenter := NewFakeDesignsPresenter()

		generator := usecases.NewDesignGenerator(templateRepo, monolith, rooms, productSearch, genAI, nil)

		// Setup an error for one category
		productSearch.AddError(string(usecases.CategoryVanity), assert.AnError)

		// Create a test room layout
		roomLayout := createTestRoomLayout()

		// Execute
		generator.GenerateDesignViaAI(ctx, roomLayout, "boring", presenter)

		// Verify
		require.Len(t, presenter.PresentErrorCalls, 1)
		assert.Empty(t, presenter.PresentDesignCalls, "Should not present design")
	})

	t.Run("should panic with nil presenter", func(t *testing.T) {
		generator := createTestDesignGenerator()
		roomLayout := createTestRoomLayout()

		assert.Panics(t, func() {
			generator.GenerateDesignViaAI(ctx, roomLayout, "", nil)
		})
	})
}

func TestDesignGenerator_GenerateDesignsForProjectFromTemplates(t *testing.T) {
	ctx := context.Background()

	t.Run("should generate designs for project from template IDs successfully", func(t *testing.T) {
		fakeRepo := &FakeTemplateRepo{}
		fakeMonolith := NewFakeMonolith()
		rooms := gateways.NewRooms([]byte(`{}`))
		ai := gateways.NewFakeLLM()
		productSearch := gateways.NewFakeProductSearch()
		generator := usecases.NewDesignGenerator(fakeRepo, fakeMonolith, rooms, productSearch, ai, nil)
		presenter := NewFakeDesignsPresenter()

		projectId := entities.ProjectId("TEST-PROJECT")
		templateIds := []uuid.UUID{uuid.New(), uuid.New()}

		// Set up the fake repo to return templates
		fakeRepo.templatesToReturn = []usecases.Template{
			createTestTemplate("Template 1", usecases.Modern, usecases.Bold),
			createTestTemplate("Template 2", usecases.Traditional, usecases.Neutral),
		}

		// Set up the fake monolith to return a layout
		testLayout := createTestRoomLayout()
		fakeMonolith.SetLayoutToReturn(testLayout)

		generator.GenerateDesignsForProjectFromTemplates(ctx, projectId, templateIds, presenter)

		// Verify the monolith was called to get the layout
		layoutCalls := fakeMonolith.GetLayoutCalls()
		require.Len(t, layoutCalls, 1)
		assert.Equal(t, projectId, layoutCalls[0].ProjectId)

		// Verify designs were generated and presented
		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		assert.Len(t, designs, 2)
	})

	t.Run("should handle empty template IDs list", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		projectId := entities.ProjectId("TEST-PROJECT")
		templateIds := []uuid.UUID{}

		generator.GenerateDesignsForProjectFromTemplates(ctx, projectId, templateIds, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		assert.Empty(t, designs)
	})

	t.Run("should present error when layout fetch fails", func(t *testing.T) {
		fakeRepo := &FakeTemplateRepo{}
		fakeMonolith := NewFakeMonolith()
		rooms := gateways.NewRooms([]byte(`{}`))
		ai := gateways.NewFakeLLM()
		productSearch := gateways.NewFakeProductSearch()
		generator := usecases.NewDesignGenerator(fakeRepo, fakeMonolith, rooms, productSearch, ai, nil)
		presenter := NewFakeDesignsPresenter()

		projectId := entities.ProjectId("TEST-PROJECT")
		templateIds := []uuid.UUID{uuid.New()}

		// Set up the monolith to fail
		fakeMonolith.SetShouldFailGetLayout(true, assert.AnError)

		generator.GenerateDesignsForProjectFromTemplates(ctx, projectId, templateIds, presenter)

		// Verify error was presented
		require.Len(t, presenter.PresentErrorCalls, 1)
		assert.Equal(t, assert.AnError, presenter.PresentErrorCalls[0])
	})

	t.Run("should present error when template fetch fails", func(t *testing.T) {
		fakeRepo := &FakeTemplateRepo{}
		fakeMonolith := NewFakeMonolith()
		rooms := gateways.NewRooms([]byte(`{}`))
		ai := gateways.NewFakeLLM()
		productSearch := gateways.NewFakeProductSearch()
		generator := usecases.NewDesignGenerator(fakeRepo, fakeMonolith, rooms, productSearch, ai, nil)
		presenter := NewFakeDesignsPresenter()

		projectId := entities.ProjectId("TEST-PROJECT")
		templateIds := []uuid.UUID{uuid.New()}

		// Set up the repo to fail
		fakeRepo.shouldFail = true
		fakeRepo.errorToReturn = assert.AnError

		generator.GenerateDesignsForProjectFromTemplates(ctx, projectId, templateIds, presenter)

		// Verify error was presented
		require.Len(t, presenter.PresentErrorCalls, 1)
		assert.Equal(t, assert.AnError, presenter.PresentErrorCalls[0])
	})

	t.Run("should panic with nil presenter", func(t *testing.T) {
		generator := createTestDesignGenerator()
		projectId := entities.ProjectId("TEST-PROJECT")
		templateIds := []uuid.UUID{uuid.New()}

		assert.Panics(t, func() {
			generator.GenerateDesignsForProjectFromTemplates(ctx, projectId, templateIds, nil)
		})
	})
}

// Helper functions for creating test data

func createTestRoomLayout() entities.RoomLayout {
	return entities.RoomLayout{
		Id:        uuid.New(),
		FloorIds:  []uuid.UUID{uuid.New()},
		Walls:     []entities.Wall{{LayoutId: uuid.New(), NicheIds: []uuid.UUID{}}},
		WetAreas:  []entities.WetArea{},
		ToiletIds: []uuid.UUID{uuid.New()},
		Vanities:  []entities.Vanity{{LayoutId: uuid.New()}},
	}
}

func createTestRoomLayoutWithShowerGlass(glassType entities.ShowerEnclosureType) entities.RoomLayout {
	layout := createTestRoomLayout()
	layout.WetAreas = []entities.WetArea{
		{
			LayoutId:  uuid.New(),
			GlassType: glassType,
			ShowerIds: []uuid.UUID{uuid.New()},
		},
	}
	return layout
}

func createTestRoomLayoutWithAlcoveTub(doorType entities.ShowerEnclosureType) entities.RoomLayout {
	layout := createTestRoomLayout()
	layout.WetAreas = []entities.WetArea{
		{
			LayoutId:  uuid.New(),
			GlassType: entities.NoShowerEnclosure,
			AlcoveTubs: []entities.AlcoveTub{
				{
					LayoutId: uuid.New(),
					DoorType: doorType,
				},
			},
		},
	}
	return layout
}

func createTestRoomLayoutWithVanity(maxLength *float64) entities.RoomLayout {
	layout := createTestRoomLayout()
	layout.Vanities = []entities.Vanity{
		{
			LayoutId:  uuid.New(),
			MaxLength: maxLength,
		},
	}
	return layout
}

func createTestRoomLayoutWithNiches() entities.RoomLayout {
	layout := createTestRoomLayout()
	layout.Walls = []entities.Wall{
		{
			LayoutId: uuid.New(),
			NicheIds: []uuid.UUID{uuid.New()},
		},
	}
	return layout
}

func createTestTemplate(name string, style usecases.Style, colorScheme usecases.ColorScheme) usecases.Template {
	floorTileId := uuid.New()
	lightingId := uuid.New()
	mirrorId := uuid.New()
	paintId := uuid.New()
	shelvingId := uuid.New()
	toiletId := uuid.New()

	return usecases.Template{
		ID: uuid.New(),
		Tagged: usecases.Tagged{
			ColorScheme: colorScheme,
			Style:       style,
		},
		Name:        name,
		Description: "Test template description",
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile: &floorTileId,
			Lighting:  &lightingId,
			Mirror:    &mirrorId,
			Paint:     &paintId,
			Shelving:  &shelvingId,
			Toilet:    &toiletId,
		},
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
	}
}

func createTestTemplateWithProductOptions() usecases.Template {
	template := createTestTemplate("Test Template", usecases.Modern, usecases.Bold)
	showerWallTileId := uuid.New()
	template.ShowerWallTile = &showerWallTileId
	template.ProductSelectionOptions = usecases.ProductSelectionOptions{
		AlcoveTub:          uuid.New(),
		FreestandingTub:    uuid.New(),
		ShowerGlassFixed:   uuid.New(),
		ShowerGlassSliding: uuid.New(),
		ShowerSystemCombo:  uuid.New(),
		ShowerSystemSolo:   uuid.New(),
		TubDoorFixed:       uuid.New(),
		TubDoorSliding:     uuid.New(),
	}
	return template
}

func createTestTemplateWithVanityScaling() usecases.Template {
	template := createTestTemplateWithProductOptions()
	template.VanityScalingOptions = map[int]usecases.VanityScalingOption{
		48: {
			VanityProductID: uuid.New(),
			FaucetProductID: uuid.New(),
		},
	}
	return template
}

func createTestTemplateWithMultipleVanityScaling() usecases.Template {
	template := createTestTemplateWithProductOptions()
	template.VanityScalingOptions = map[int]usecases.VanityScalingOption{
		36: {
			VanityProductID: uuid.New(),
			FaucetProductID: uuid.New(),
		},
		48: {
			VanityProductID: uuid.New(),
			FaucetProductID: uuid.New(),
		},
		60: {
			VanityProductID: uuid.New(),
			FaucetProductID: uuid.New(),
		},
	}
	return template
}
