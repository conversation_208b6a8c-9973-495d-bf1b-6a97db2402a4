package usecases_test

import (
	"context"
	"errors"
	"log/slog"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// TestPresenter captures results for verification in integration tests
type TestPresenter struct {
	LastError     error
	LastDesign    usecases.Design
	LastStatus    usecases.Status
	SuccessCalled bool
}

func (p *TestPresenter) PresentError(err error) {
	p.LastError = err
}

func (p *TestPresenter) ConveySuccess() {
	p.SuccessCalled = true
}

func (p *TestPresenter) ConveySuccessWithResource(design usecases.Design, status usecases.Status) {
	p.LastDesign = design
	p.LastStatus = status
	p.SuccessCalled = true
}

// FailingRepository wraps FakeRelDb and can simulate failures
type FailingRepository struct {
	*gateways.FakeRelDb
	ShouldFail bool
}

func (r *FailingRepository) UpsertDesign(ctx context.Context, design usecases.Design) (uuid.UUID, error) {
	if r.ShouldFail {
		return uuid.UUID{}, errors.New("simulated failure")
	}
	return r.FakeRelDb.UpsertDesign(ctx, design)
}

func TestNewDesignSaver_Integration(t *testing.T) {
	t.Run("should create saver with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		ai := gateways.NewFakeLLM()
		logger := slog.Default()
		saver := usecases.NewDesignSaver(repo, catalog, ai, logger)
		assert.NotNil(t, saver)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewDesignSaver(nil, gateways.NewFakeCatalog(), gateways.NewFakeLLM(), nil)
		})
	})
}

func TestDesignSaver_SaveDesign_Integration(t *testing.T) {
	ctx := context.Background()
	projectId := entities.ProjectId("TEST-PROJECT")

	testDesign := usecases.Design{
		ProjectID:          projectId,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
	}

	t.Run("should save new design successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		presenter := &TestPresenter{}
		ai := gateways.NewFakeLLM()
		logger := slog.Default()
		saver := usecases.NewDesignSaver(repo, catalog, ai, logger)

		designToSave := testDesign
		designToSave.ID = uuid.UUID{} // No ID initially

		saver.SaveDesign(ctx, presenter, designToSave)

		// Verify success was called
		assert.True(t, presenter.SuccessCalled, "Success should have been called")
		assert.Nil(t, presenter.LastError, "No error should have occurred")

		// Verify the design was saved with a new ID
		assert.NotEqual(t, uuid.UUID{}, presenter.LastDesign.ID, "Design should have been assigned an ID")
		assert.Equal(t, usecases.Updated, presenter.LastStatus, "Status should be Updated")
		assert.Equal(t, projectId, presenter.LastDesign.ProjectID, "Project ID should match")

		// Verify the design can be retrieved from the repository
		savedDesign, err := repo.ReadDesign(ctx, presenter.LastDesign.ID)
		assert.NoError(t, err, "Should be able to retrieve saved design")
		assert.Equal(t, presenter.LastDesign.ID, savedDesign.ID, "Retrieved design should have same ID")
		assert.Equal(t, projectId, savedDesign.ProjectID, "Retrieved design should have same project ID")
	})

	t.Run("should save existing design successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		presenter := &TestPresenter{}
		ai := gateways.NewFakeLLM()
		logger := slog.Default()
		saver := usecases.NewDesignSaver(repo, catalog, ai, logger)

		// First, create a design in the repository
		existingDesign := testDesign
		existingDesign.ID = uuid.New()
		savedId, err := repo.UpsertDesign(ctx, existingDesign)
		assert.NoError(t, err, "Should be able to create initial design")
		existingDesign.ID = savedId

		// Now update the design
		designToSave := existingDesign
		designToSave.Status = usecases.Fave // Change something

		saver.SaveDesign(ctx, presenter, designToSave)

		// Verify success was called
		assert.True(t, presenter.SuccessCalled, "Success should have been called")
		assert.Nil(t, presenter.LastError, "No error should have occurred")
		assert.Equal(t, usecases.Updated, presenter.LastStatus, "Status should be Updated")
		assert.Equal(t, savedId, presenter.LastDesign.ID, "Design ID should remain the same")

		// Verify the design was actually updated in the repository
		updatedDesign, err := repo.ReadDesign(ctx, savedId)
		assert.NoError(t, err, "Should be able to retrieve updated design")
		assert.Equal(t, usecases.Fave, updatedDesign.Status, "Design status should be updated")
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		// Create a custom repository that will fail on UpsertDesign
		repo := &FailingRepository{
			FakeRelDb:  gateways.NewFakeRelDb(),
			ShouldFail: true,
		}
		catalog := gateways.NewFakeCatalog()
		presenter := &TestPresenter{}
		ai := gateways.NewFakeLLM()
		logger := slog.Default()
		saver := usecases.NewDesignSaver(repo, catalog, ai, logger)

		saver.SaveDesign(ctx, presenter, testDesign)

		// Verify error was presented
		assert.False(t, presenter.SuccessCalled, "Success should not have been called")
		assert.NotNil(t, presenter.LastError, "Error should have been presented")
		assert.Contains(t, presenter.LastError.Error(), "simulated failure", "Error should contain expected message")
	})

	t.Run("should handle design with all fields populated", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		presenter := &TestPresenter{}
		ai := gateways.NewFakeLLM()
		logger := slog.Default()
		saver := usecases.NewDesignSaver(repo, catalog, ai, logger)

		// Create a comprehensive design with all optional fields
		designId := uuid.New()
		faucetId := uuid.New()
		floorTileId := uuid.New()
		lightingId := uuid.New()
		mirrorId := uuid.New()
		paintId := uuid.New()
		shelvingId := uuid.New()
		showerFloorTileId := uuid.New()
		showerSystemId := uuid.New()
		showerWallTileId := uuid.New()
		showerShortWallTileId := uuid.New()
		showerGlassId := uuid.New()
		toiletId := uuid.New()
		tubId := uuid.New()
		tubDoorId := uuid.New()
		tubFillerId := uuid.New()
		vanityId := uuid.New()
		wallpaperId := uuid.New()
		wallTileId := uuid.New()
		nicheTileId := uuid.New()

		fullDesign := usecases.Design{
			ID:                 designId,
			ProjectID:          projectId,
			Status:             usecases.Fave,
			WallpaperPlacement: usecases.AllWalls,
			WallTilePlacement:  usecases.FullWall,
			DesignOptions: usecases.DesignOptions{
				ColorScheme:            &[]usecases.ColorScheme{usecases.Bold}[0],
				Style:                  &[]usecases.Style{usecases.Modern}[0],
				FloorTilePattern:       &[]usecases.TilePattern{usecases.Herringbone}[0],
				ShowerFloorTilePattern: &[]usecases.TilePattern{usecases.HorizontalStacked}[0],
				ShowerWallTilePattern:  &[]usecases.TilePattern{usecases.VerticalStacked}[0],
				WallTilePattern:        &[]usecases.TilePattern{usecases.HalfOffset}[0],
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile:       &floorTileId,
					Lighting:        &lightingId,
					Mirror:          &mirrorId,
					Paint:           &paintId,
					Shelving:        &shelvingId,
					ShowerFloorTile: &showerFloorTileId,
					ShowerWallTile:  &showerWallTileId,
					Toilet:          &toiletId,
					TubFiller:       &tubFillerId,
					Wallpaper:       &wallpaperId,
					WallTile:        &wallTileId,
				},
				Faucet:              &faucetId,
				NicheTile:           &nicheTileId,
				ShowerGlass:         &showerGlassId,
				ShowerShortWallTile: &showerShortWallTileId,
				ShowerSystem:        &showerSystemId,
				Tub:                 &tubId,
				TubDoor:             &tubDoorId,
				Vanity:              &vanityId,
			},
			ShowerGlassVisible: true,
			TubDoorVisible:     true,
			NichesVisible:      true,
		}

		saver.SaveDesign(ctx, presenter, fullDesign)

		// Verify success and that all fields are preserved
		assert.True(t, presenter.SuccessCalled, "Success should have been called")
		assert.Nil(t, presenter.LastError, "No error should have occurred")
		assert.Equal(t, usecases.Updated, presenter.LastStatus, "Status should be Updated")

		// Verify the design was saved with all fields intact
		savedDesign, err := repo.ReadDesign(ctx, presenter.LastDesign.ID)
		assert.NoError(t, err, "Should be able to retrieve saved design")
		assert.Equal(t, usecases.Fave, savedDesign.Status, "Status should be preserved")
		assert.Equal(t, usecases.AllWalls, savedDesign.WallpaperPlacement, "WallpaperPlacement should be preserved")
		assert.Equal(t, usecases.FullWall, savedDesign.WallTilePlacement, "WallTilePlacement should be preserved")
		assert.True(t, savedDesign.ShowerGlassVisible, "ShowerGlassVisible should be preserved")
		assert.True(t, savedDesign.TubDoorVisible, "TubDoorVisible should be preserved")
		assert.True(t, savedDesign.NichesVisible, "NichesVisible should be preserved")
	})

	t.Run("should handle different design statuses", func(t *testing.T) {
		statuses := []usecases.DesignStatus{
			usecases.Preview,
			usecases.Fave,
			usecases.Archived,
		}

		for _, status := range statuses {
			t.Run("status_"+string(status), func(t *testing.T) {
				repo := gateways.NewFakeRelDb()
				catalog := gateways.NewFakeCatalog()
				presenter := &TestPresenter{}
				ai := gateways.NewFakeLLM()
				logger := slog.Default()
				saver := usecases.NewDesignSaver(repo, catalog, ai, logger)

				design := testDesign
				design.Status = status
				design.ID = uuid.New()

				saver.SaveDesign(ctx, presenter, design)

				// Verify success and status preservation
				assert.True(t, presenter.SuccessCalled, "Success should have been called")
				assert.Nil(t, presenter.LastError, "No error should have occurred")
				assert.Equal(t, status, presenter.LastDesign.Status, "Status should be preserved")

				// Verify the design was saved with correct status
				savedDesign, err := repo.ReadDesign(ctx, presenter.LastDesign.ID)
				assert.NoError(t, err, "Should be able to retrieve saved design")
				assert.Equal(t, status, savedDesign.Status, "Saved design should have correct status")
			})
		}
	})

	t.Run("should handle different wallpaper placements", func(t *testing.T) {
		placements := []usecases.WallpaperPlacement{
			usecases.NoWallpaper,
			usecases.AllWalls,
			usecases.VanityWall,
		}

		for _, placement := range placements {
			t.Run("wallpaper_"+string(placement), func(t *testing.T) {
				repo := gateways.NewFakeRelDb()
				catalog := gateways.NewFakeCatalog()
				presenter := &TestPresenter{}
				ai := gateways.NewFakeLLM()
				logger := slog.Default()
				saver := usecases.NewDesignSaver(repo, catalog, ai, logger)

				design := testDesign
				design.WallpaperPlacement = placement
				design.ID = uuid.New()

				saver.SaveDesign(ctx, presenter, design)

				// Verify success and wallpaper placement preservation
				assert.True(t, presenter.SuccessCalled, "Success should have been called")
				assert.Nil(t, presenter.LastError, "No error should have occurred")
				assert.Equal(t, placement, presenter.LastDesign.WallpaperPlacement, "WallpaperPlacement should be preserved")

				// Verify the design was saved with correct wallpaper placement
				savedDesign, err := repo.ReadDesign(ctx, presenter.LastDesign.ID)
				assert.NoError(t, err, "Should be able to retrieve saved design")
				assert.Equal(t, placement, savedDesign.WallpaperPlacement, "Saved design should have correct wallpaper placement")
			})
		}
	})

	t.Run("should handle different wall tile placements", func(t *testing.T) {
		placements := []usecases.WallTilePlacement{
			usecases.NoWallTile,
			usecases.FullWall,
			usecases.HalfWall,
			usecases.VanityFullWall,
			usecases.VanityHalfWall,
		}

		for _, placement := range placements {
			t.Run("wall_tile_"+string(placement), func(t *testing.T) {
				repo := gateways.NewFakeRelDb()
				catalog := gateways.NewFakeCatalog()
				presenter := &TestPresenter{}
				ai := gateways.NewFakeLLM()
				logger := slog.Default()
				saver := usecases.NewDesignSaver(repo, catalog, ai, logger)

				design := testDesign
				design.WallTilePlacement = placement
				design.ID = uuid.New()

				saver.SaveDesign(ctx, presenter, design)

				// Verify success and wall tile placement preservation
				assert.True(t, presenter.SuccessCalled, "Success should have been called")
				assert.Nil(t, presenter.LastError, "No error should have occurred")
				assert.Equal(t, placement, presenter.LastDesign.WallTilePlacement, "WallTilePlacement should be preserved")

				// Verify the design was saved with correct wall tile placement
				savedDesign, err := repo.ReadDesign(ctx, presenter.LastDesign.ID)
				assert.NoError(t, err, "Should be able to retrieve saved design")
				assert.Equal(t, placement, savedDesign.WallTilePlacement, "Saved design should have correct wall tile placement")
			})
		}
	})
}
