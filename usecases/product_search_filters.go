package usecases

import "golang.org/x/exp/constraints"

// This file contains lexicographically sorted Go equivalents of the Python enums defined in
// https://gitlab.com/arc-studio-ai/services/product-search/-/blob/main/data_models/filter_enums.py

type Area string

const (
	AreaAccessories   Area = "Accessories"
	AreaCombo         Area = "Shower/Tub Combo"
	AreaShower        Area = "Shower"
	AreaStorage       Area = "Storage"
	AreaToilet        Area = "Toilet"
	AreaTub           Area = "Bathtub"
	AreaVanity        Area = "Vanity"
	AreaWallsAndFloor Area = "Walls & Floor"
)

type Availability string

const (
	BackOrdered  Availability = "BACK_ORDERED"
	Discontinued Availability = "DISCONTINUED"
	InStock      Availability = "IN_STOCK"
	OutOfStock   Availability = "OUT_OF_STOCK"
)

type Category string

const (
	CategoryFaucet      Category = "Faucets"
	CategoryLighting    Category = "Decorative Lighting"
	CategoryMirror      Category = "Mirror"
	CategoryPaint       Category = "Paint"
	CategoryShelving    Category = "Shelves"
	CategoryShower      Category = "Shower Systems"
	CategoryShowerGlass Category = "Shower Glass"
	CategoryTile        Category = "Tile"
	CategoryToilet      Category = "Toilet"
	CategoryTub         Category = "Tubs"
	CategoryTubDoor     Category = "Tub Doors"
	CategoryTubFiller   Category = "Tub Filler"
	CategoryVanity      Category = "Vanities"
	CategoryWallpaper   Category = "Wallpaper"
)

type ColorGroup string

const (
	Black  ColorGroup = "Black"
	Blue   ColorGroup = "Blue"
	Bronze ColorGroup = "Bronze"
	Brown  ColorGroup = "Brown"
	Cream  ColorGroup = "Cream"
	Gold   ColorGroup = "Gold"
	Green  ColorGroup = "Green"
	Grey   ColorGroup = "Grey"
	Orange ColorGroup = "Orange"
	Pink   ColorGroup = "Pink"
	Silver ColorGroup = "Silver"
	White  ColorGroup = "White"
	Wood   ColorGroup = "Wood"
	Yellow ColorGroup = "Yellow"
)

type FaucetHandleType string

const (
	Cross FaucetHandleType = "Cross"
	Knob  FaucetHandleType = "Knob"
	Lever FaucetHandleType = "Lever"
	Wheel FaucetHandleType = "Wheel"
)

type FaucetHoleSpacing string

const (
	CenterSet  FaucetHoleSpacing = "Centerset"
	SingleHole FaucetHoleSpacing = "Single Hole"
	WideSpread FaucetHoleSpacing = "Widespread"
)

type LightingType string

const (
	Barn         LightingType = "Barn Light"
	BathBar      LightingType = "Bath Bar"
	Globe        LightingType = "Globe Light"
	Sconce       LightingType = "Sconce"
	ShadedSconce LightingType = "Shaded Sconce"
)

type MirrorShape string

const (
	MirrorShapeArched      MirrorShape = "Arched"
	MirrorShapeOval        MirrorShape = "Oval"
	MirrorShapeRectangular MirrorShape = "Rectangular"
	MirrorShapeRound       MirrorShape = "Round"
	MirrorShapeRoundedRect MirrorShape = "Rounded Rectangle"
	MirrorShapeSculptural  MirrorShape = "Sculptural"
)

type ShowerEnclosureFraming string

const (
	Framed        ShowerEnclosureFraming = "Framed"
	Frameless     ShowerEnclosureFraming = "Frameless"
	SemiFrameless ShowerEnclosureFraming = "Semi-frameless"
)

type ShowerEnclosureGlass string

const (
	Cast     ShowerEnclosureGlass = "Cast"
	Clear    ShowerEnclosureGlass = "Clear"
	Frosted  ShowerEnclosureGlass = "Frosted"
	Heavy    ShowerEnclosureGlass = "Heavy"
	LowIron  ShowerEnclosureGlass = "Low-iron"
	Stained  ShowerEnclosureGlass = "Stained"
	Tempered ShowerEnclosureGlass = "Tempered"
	Textured ShowerEnclosureGlass = "Textured"
	Tinted   ShowerEnclosureGlass = "Tinted"
)

type ShowerEnclosureType string

const (
	Fixed          ShowerEnclosureType = "Fixed"
	Folding        ShowerEnclosureType = "Folding"
	Rolling        ShowerEnclosureType = "Rolling"
	Sliding        ShowerEnclosureType = "Sliding"
	SwingingHinged ShowerEnclosureType = "Hinged"
	SwingingPivot  ShowerEnclosureType = "Pivot"
)

type Style string

const (
	MidCentury   Style = "Mid-century"
	Modern       Style = "Modern"
	Traditional  Style = "Traditional"
	Transitional Style = "Transitional"
)

type TileFinish string

const (
	TileFinishBrushed TileFinish = "Brushed"
	TileFinishGauged  TileFinish = "Gauged"
	TileFinishGlossy  TileFinish = "Glossy"
	TileFinishHoned   TileFinish = "Honed"
	TileFinishMatte   TileFinish = "Matte"
	TileFinishSatin   TileFinish = "Satin"
)

type TileLocation string

const (
	TileLocationFloor       TileLocation = "floor"
	TileLocationShowerFloor TileLocation = "shower_floor"
	TileLocationShowerWall  TileLocation = "shower_wall"
	TileLocationWall        TileLocation = "wall"
)

type TilePattern string

const (
	HalfOffset        TilePattern = "HalfOffset"
	Herringbone       TilePattern = "Herringbone"
	HorizontalStacked TilePattern = "Horizontal"
	ThirdOffset       TilePattern = "ThirdOffset"
	VerticalStacked   TilePattern = "Vertical"
)

type TileShape string

const (
	TileShapeBasketweave       TileShape = "Basketweave"
	TileShapeChevronMosaic     TileShape = "Chevron Mosaic"
	TileShapeFishscale         TileShape = "Fishscale"
	TileShapeHerringboneMosaic TileShape = "Herringbone Mosaic"
	TileShapeHex               TileShape = "Hex"
	TileShapeHexMosaic         TileShape = "Hex Mosaic"
	TileShapeMosaic            TileShape = "Mosaic"
	TileShapePenny             TileShape = "Penny"
	TileShapePickett           TileShape = "Pickett"
	TileShapePlank             TileShape = "Plank"
	TileShapeRectangle         TileShape = "Rectangle"
	TileShapeRectangleMosaic   TileShape = "Rectangle Mosaic"
	TileShapeRound             TileShape = "Round"
	TileShapeSquare            TileShape = "Square"
	TileShapeSquareMosaic      TileShape = "Square Mosaic"
	TileShapeSubway            TileShape = "Subway"
	TileShapeTrapezoid         TileShape = "Trapezoid"
)

type TileType string

const (
	TileTypeCeramic      TileType = "Ceramic"
	TileTypeConcreteLook TileType = "Concrete-look"
	TileTypeGlass        TileType = "Glass"
	TileTypeHandmade     TileType = "Handmade"
	TileTypeMatte        TileType = "Matte"
	TileTypePenny        TileType = "Penny"
	TileTypePorcelain    TileType = "Porcelain"
	TileTypeStone        TileType = "Stone"
	TileTypeStoneLook    TileType = "Stone-look"
	TileTypeSubway       TileType = "Subway"
	TileTypeTerrazzoLook TileType = "Terrazzo-look"
	TileTypeWoodLook     TileType = "Wood-look"
	TileTypeZellige      TileType = "Zellige"
)

type ToiletMountingPosition string

const (
	ToiletMountingPositionFloor ToiletMountingPosition = "FLOOR"
	ToiletMountingPositionWall  ToiletMountingPosition = "WALL"
)

type TubFillerMountingPosition string

const (
	TubFillerMountingPositionDeck  TubFillerMountingPosition = "DECK"
	TubFillerMountingPositionFloor TubFillerMountingPosition = "FLOOR"
	TubFillerMountingPositionWall  TubFillerMountingPosition = "WALL"
)

type TubShape string

const (
	TubShapeCircle    TubShape = "Circular"
	TubShapeOval      TubShape = "Oval"
	TubShapeRectangle TubShape = "Rectangular"
)

type TubType string

const (
	Alcove       TubType = "ALCOVE"
	Freestanding TubType = "FREESTANDING"
)

type VanityType string

const (
	VanityTypeConsole      VanityType = "Console"
	VanityTypeFloating     VanityType = "Wall Mounted"
	VanityTypeFreestanding VanityType = "Freestanding"
)

type WallpaperApplication string

const (
	WallpaperApplicationPrepasted   WallpaperApplication = "Pre-pasted"
	WallpaperApplicationRemovable   WallpaperApplication = "Peel & Stick"
	WallpaperApplicationTraditional WallpaperApplication = "Non-Pasted"
)

type WallpaperPattern string

const (
	Abstract  WallpaperPattern = "Abstract"
	Floral    WallpaperPattern = "Floral"
	Geometric WallpaperPattern = "Geometric"
	Plaid     WallpaperPattern = "Plaid"
	Terrazzo  WallpaperPattern = "Terrazzo"
	Toile     WallpaperPattern = "Toile"
	Wildlife  WallpaperPattern = "Wildlife"
)

// Range represents a numeric range with a min & optional max.
// Min defaults to the 0 value & must be <= max.
type Range[T constraints.Integer | constraints.Float] struct {
	Min T
	Max *T
}

type UIntRange = Range[uint]

// FloatRange values must be >= 0.
type FloatRange = Range[float64]

type ProductSearchFilters struct {
	Category Category
	Query    *string

	// Price range (in US cents)
	PriceRange UIntRange
	// Max lead time in days. Should be >= 2 if set.
	MaxLeadTime *uint

	// List of acceptable colors & styles.
	Colors []ColorGroup
	Styles []Style

	// List of acceptable product family names.
	Collections []string

	// Length & Height ranges (in inches).
	Length FloatRange
	Height FloatRange

	// Faucet filters
	FaucetHoleSpacingCompatibility *FaucetHoleSpacing
	FaucetHandleTypes              []FaucetHandleType

	// Decorative lighting filters
	LightingTypes    []LightingType
	LightingNumBulbs UIntRange

	// Mirror filters
	MirrorIsMedicineCabinet *bool
	MirrorIsLighted         *bool
	MirrorIsFramed          *bool
	MirrorShapes            []MirrorShape

	// Shelf count for shelving units; values must be >= 1.
	NumShelves UIntRange

	// Shower system filters
	ShowerHasTubSpout           *bool
	ShowerHasHandheldShowerhead *bool
	ShowerHasRainShowerhead     *bool
	ShowerHasLowFlowShowerhead  *bool
	ShowerHasFilteredShowerhead *bool

	// Shower enclosure filters
	ShowerEnclosureTypes       []ShowerEnclosureType
	ShowerEnclosureGlassOpts   []ShowerEnclosureGlass
	ShowerEnclosureFramingOpts []ShowerEnclosureFraming

	// Tile filters
	TileLocation         *TileLocation
	TileTypes            []TileType
	TileShapes           []TileShape
	TileFinishes         []TileFinish
	TilePatterns         []TilePattern
	TileAreaCoverageSqFt FloatRange

	// Toilet filters
	ToiletMountingPosition *ToiletMountingPosition
	ToiletMustHaveBidet    *bool

	// Tub filters
	TubType   *TubType
	TubShapes []TubShape

	TubFillerMountingPosition *TubFillerMountingPosition

	// Vanity filters
	VanityTypes []VanityType
	// Number of sinks for vanities. Values should be <= 3.
	VanityNumSinks UIntRange

	// Wallpaper filters
	WallpaperApplications []WallpaperApplication
	WallpaperPatterns     []WallpaperPattern
}
