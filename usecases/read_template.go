package usecases

import (
	"context"

	"github.com/google/uuid"
)

type TemplateRetriever struct {
	templateRepo templateRepositoryReplica
}

func NewTemplateRetriever(templateRepo templateRepositoryReplica) *TemplateRetriever {
	if IsNil(templateRepo) {
		panic("templateRepo cannot be nil")
	}
	return &TemplateRetriever{templateRepo: templateRepo}
}

func (tr *TemplateRetriever) RetrieveTemplate(ctx context.Context, presenter TemplatesPresenter, templateId uuid.UUID) {
	template, err := tr.templateRepo.ReadTemplate(ctx, templateId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentTemplate(ctx, template)
}

func (tr *TemplateRetriever) RetrieveTemplateByLegacyId(ctx context.Context, presenter TemplatesPresenter, legacyId string) {
	template, err := tr.templateRepo.ReadTemplateByLegacyId(ctx, legacyId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentTemplate(ctx, template)
}

func (tr *TemplateRetriever) RetrieveAllTemplates(ctx context.Context, presenter TemplatesPresenter) {
	templates, err := tr.templateRepo.ReadAllTemplates(ctx)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentTemplates(ctx, templates)
}
