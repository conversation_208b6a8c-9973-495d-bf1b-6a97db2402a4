package usecases

import (
	"context"
	"errors"
	"fmt"
	"log"
	"log/slog"
	"math"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type DesignEvolver struct {
	catalog       catalog
	productSearch productSearch
	designRepo    designRepository
	logger        *slog.Logger
}

func NewDesignEvolver(catalog catalog, productSearch productSearch, designRepo designRepository, logger *slog.Logger) *DesignEvolver {
	if IsNil(catalog) {
		panic("catalog cannot be nil")
	}
	if IsNil(productSearch) {
		panic("productSearch cannot be nil")
	}
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignEvolver{productSearch: productSearch, catalog: catalog, designRepo: designRepo, logger: logger}
}

func (de *DesignEvolver) EvolveDesignForLayout(ctx context.Context,
	design Design, layout entities.RoomLayout) (Design, error) {
	var err error
	if len(layout.ToiletIds) == 0 {
		design.Toilet = nil
		design.Shelving = nil
	} else {
		if design.Toilet == nil {
			toiletUUIDs, err := de.productSearch.FindProducts(ctx, "category=Toilet")
			if err != nil {
				return design, err
			}
			if len(toiletUUIDs) == 0 {
				return design, errors.New("no toilets found via product search")
			}
			design.Toilet = &toiletUUIDs[0]
		}

		if design.Shelving == nil {
			shelvingUUIDs, err := de.productSearch.FindProducts(ctx, "category=Shelves")
			if err != nil {
				return design, err
			}
			if len(shelvingUUIDs) == 0 {
				return design, errors.New("no shelving found via product search")
			}
			design.Shelving = &shelvingUUIDs[0]
		}
	}

	if design, err = de.evolveVanityProducts(ctx, design, layout); err != nil {
		return design, err
	}

	// Assume only one wet area for now. TODO: Handle multiple wet areas.
	hasWalkinShower := len(layout.WetAreas) > 0 && len(layout.WetAreas[0].ShowerIds) > 0
	if !hasWalkinShower {
		design.ShowerFloorTile = nil
		design.ShowerGlass = nil
	}
	hasAlcoveTub := len(layout.WetAreas) > 0 && len(layout.WetAreas[0].AlcoveTubs) > 0
	maxShowerGlassLength := 0.0
	if len(layout.WetAreas) > 0 && layout.WetAreas[0].MaxShowerGlassLength != nil {
		maxShowerGlassLength = *layout.WetAreas[0].MaxShowerGlassLength
	}
	if design, err = de.evolveShowerProducts(ctx,
		design, hasAlcoveTub, hasWalkinShower, maxShowerGlassLength); err != nil {
		return design, err
	}

	hasFreestandingTub := len(layout.WetAreas) > 0 && len(layout.WetAreas[0].FreestandingTubIds) > 0
	maxTubDoorLength := 0.0
	if len(layout.WetAreas) > 0 && layout.WetAreas[0].MaxTubLength != nil {
		maxTubDoorLength = *layout.WetAreas[0].MaxTubLength
	}
	if design, err = de.evolveTubProducts(ctx,
		design, hasAlcoveTub, hasFreestandingTub, maxTubDoorLength); err != nil {
		return design, err
	}

	design.LastUpdated = time.Now()
	return design, nil
}

func (de *DesignEvolver) evolveVanityProducts(ctx context.Context,
	design Design, layout entities.RoomLayout) (Design, error) {
	var err error

	if len(layout.Vanities) == 0 || layout.Vanities[0].MaxLength == nil || *layout.Vanities[0].MaxLength == 0 {
		design.Vanity = nil
		design.Faucet = nil
		design.Mirror = nil
		design.Lighting = nil
		return design, nil
	}

	maxVanityLengthInches := int(*layout.Vanities[0].MaxLength)
	if design.Vanity == nil {
		if design.Vanity, err = de.findVanity(ctx, maxVanityLengthInches, nil, nil); err != nil {
			return design, err
		}
	}
	vanity, err := de.catalog.ProductInfo(ctx, "vanities", *design.Vanity)
	if err != nil {
		return design, err
	}
	if vanity.category() != CategoryVanity {
		return design, fmt.Errorf("expected vanity type for %s but got %s", design.Vanity.String(), vanity.category())
	}
	currentVanity := vanity.(Vanity)
	if currentVanity.Length == 0 {
		return design, fmt.Errorf("no length found for vanity %s", design.Vanity.String())
	}
	if currentVanity.Length > float64(maxVanityLengthInches) {
		// TODO: try to preserve the number of sinks.
		fam := currentVanity.ProductFamilyName
		if design.Vanity, err = de.findVanity(ctx, maxVanityLengthInches,
			&fam, currentVanity.Color); err != nil {
			return design, err
		}
		vanity, err = de.catalog.ProductInfo(ctx, "vanities", *design.Vanity)
		if err != nil {
			return design, err
		}
		currentVanity = vanity.(Vanity)
	}

	if currentVanity.FaucetHoleSpacing == "" {
		return design, fmt.Errorf("no faucet hole spacing found for vanity %s", design.Vanity.String())
	}
	if design.Faucet == nil {
		holeSpacing := currentVanity.FaucetHoleSpacing
		faucetUUIDs, err := de.productSearch.FindProducts(ctx,
			fmt.Sprintf("category=Faucets&faucet_hole_spacing_compatibility=%s", holeSpacing))
		if err != nil {
			return design, err
		}
		if len(faucetUUIDs) == 0 {
			return design, fmt.Errorf("no faucets found with %s hole spacing", holeSpacing)
		}
		design.Faucet = &faucetUUIDs[0]
	}

	if currentVanity.NumberOfSinks == 0 {
		return design, fmt.Errorf("number of sinks not provided for vanity %s", design.Vanity.String())
	}
	if currentVanity.NumberOfSinks > 1 && currentVanity.SinkOffset < 0.001 {
		log.Printf("unrealistic sink offset found for vanity %s", design.Vanity.String())
		return design, fmt.Errorf("unrealistic sink offset found for vanity %s", design.Vanity.String())
	}
	maxRelativeProductLength := calculateMaxRelativeProductLength(
		currentVanity.NumberOfSinks, currentVanity.SinkOffset, currentVanity.Length)

	if design.Mirror == nil {
		if design.Mirror, err = de.findMirror(ctx, int(maxRelativeProductLength), nil, nil); err != nil {
			return design, err
		}
	} else {
		mirror, err := de.catalog.ProductInfo(ctx, "mirrors", *design.Mirror)
		if err != nil {
			return design, err
		}
		currentMirror := mirror.(Mirror)
		if currentMirror.Length > maxRelativeProductLength {
			fam := currentMirror.ProductFamilyName
			if design.Mirror, err = de.findMirror(ctx, int(maxRelativeProductLength),
				&fam, currentMirror.Color); err != nil {
				return design, err
			}
		}
	}
	if design.Lighting == nil {
		if design.Lighting, err = de.findLighting(ctx, int(maxRelativeProductLength), nil, nil); err != nil {
			return design, err
		}
	} else {
		lighting, err := de.catalog.ProductInfo(ctx, "lightings", *design.Lighting)
		if err != nil {
			return design, err
		}
		currentLighting := lighting.(Lighting)
		if currentLighting.Length > maxRelativeProductLength {
			fam := currentLighting.ProductFamilyName
			if design.Lighting, err = de.findLighting(ctx, int(maxRelativeProductLength),
				&fam, currentLighting.Color); err != nil {
				return design, err
			}
		}
	}

	return design, nil
}

func (de *DesignEvolver) evolveShowerProducts(ctx context.Context,
	design Design, hasAlcoveTub, hasWalkinShower bool, maxShowerGlassLength float64) (Design, error) {

	if !hasAlcoveTub && !hasWalkinShower {
		design.ShowerWallTile = nil
		design.ShowerShortWallTile = nil
		design.NicheTile = nil
		design.ShowerSystem = nil
		return design, nil
	}
	if hasAlcoveTub && hasWalkinShower {
		return design, fmt.Errorf("both alcove & walk-in shower found in layout")
	}

	if design.FloorTile == nil {
		return design, fmt.Errorf("no floor tile found in design %s", design.ID.String())
	}
	floorTile, err := de.catalog.ProductInfo(ctx, "tiles", *design.FloorTile)
	if err != nil {
		return design, err
	}
	if floorTile.category() != CategoryTile {
		return design, fmt.Errorf("expected tile type for %s but got %s", design.FloorTile.String(), floorTile.category())
	}
	currentFloorTile := floorTile.(Tile)

	if hasAlcoveTub {
		if design.ShowerSystem, err = de.addOrReplaceShowerSystem(ctx, design.ShowerSystem, true); err != nil {
			return design, err
		}
	} else if hasWalkinShower {
		if design.ShowerSystem, err = de.addOrReplaceShowerSystem(ctx, design.ShowerSystem, false); err != nil {
			return design, err
		}
		if design.ShowerFloorTile == nil {
			if currentFloorTile.AvailableForShowerFloor != nil && *currentFloorTile.AvailableForShowerFloor {
				design.ShowerFloorTile = design.FloorTile
			} else if design.ShowerFloorTile, err = de.findTile(ctx,
				"shower_floor", &currentFloorTile.ProductFamilyName, currentFloorTile.Color); err != nil {
				return design, err
			}
		}
		if design.ShowerGlass == nil && maxShowerGlassLength >= 56 {
			if design.ShowerGlass, err = de.findShowerEnclosure(ctx, "Shower%%20Glass", maxShowerGlassLength); err != nil {
				return design, err
			}
		}
	} else {
		return design, fmt.Errorf("layout has seemingly impossible wet area configuration")
	}

	if design.ShowerWallTile == nil {
		if design.WallTile != nil {
			wallTile, err := de.catalog.ProductInfo(ctx, "tiles", *design.WallTile)
			if err != nil {
				return design, err
			}
			if wallTile.category() != CategoryTile {
				return design, fmt.Errorf("expected tile type for %s but got %s", design.WallTile.String(), wallTile.category())
			}
			currentWallTile := wallTile.(Tile)
			if currentWallTile.AvailableForShowerWall != nil && *currentWallTile.AvailableForShowerWall {
				design.ShowerWallTile = design.WallTile
			} else if design.ShowerWallTile, err = de.findTile(ctx,
				"shower_wall", &currentWallTile.ProductFamilyName, currentWallTile.Color); err != nil {
				return design, err
			}
		} else if currentFloorTile.AvailableForShowerWall != nil && *currentFloorTile.AvailableForShowerWall {
			design.ShowerWallTile = design.FloorTile
		} else if design.ShowerWallTile, err = de.findTile(ctx,
			"shower_wall", &currentFloorTile.ProductFamilyName, currentFloorTile.Color); err != nil {
			return design, err
		}
	}

	return design, nil
}

func (de *DesignEvolver) evolveTubProducts(ctx context.Context,
	design Design, hasAlcoveTub, hasFreestandingTub bool, maxTubDoorLength float64) (Design, error) {

	if !hasAlcoveTub && !hasFreestandingTub {
		design.Tub = nil
		design.TubDoor = nil
		design.TubFiller = nil
		return design, nil
	}

	if hasAlcoveTub && hasFreestandingTub {
		return design, fmt.Errorf("both alcove & freestanding tubs found in layout")
	}

	var err error
	var tubType TubType
	if hasAlcoveTub {
		tubType = Alcove
		design.TubFiller = nil
		if maxTubDoorLength < 56 {
			design.TubDoor = nil
		} else if design.TubDoor == nil {
			if design.TubDoor, err = de.findShowerEnclosure(ctx, "Tub%20Doors", maxTubDoorLength); err != nil {
				return design, err
			}
		}
	} else if hasFreestandingTub {
		tubType = Freestanding
		design.TubDoor = nil
		if design.TubFiller == nil {
			var color *ColorGroup
			if design.ShowerSystem != nil {
				currentShowerSystemInfo, err := de.catalog.ProductInfo(ctx, "shower-systems", *design.ShowerSystem)
				if err != nil {
					return design, err
				}
				currentShowerSystem := currentShowerSystemInfo.(Shower)
				if currentShowerSystem.Color != nil {
					color = currentShowerSystem.Color
				}
			} else if design.Faucet != nil {
				currentFaucetInfo, err := de.catalog.ProductInfo(ctx, "faucets", *design.Faucet)
				if err != nil {
					return design, err
				}
				currentFaucet := currentFaucetInfo.(Faucet)
				if currentFaucet.Color != nil {
					color = currentFaucet.Color
				}
			}
			if design.TubFiller, err = de.findProduct(ctx, "floor-mounted tub fillers",
				"category=Tub%%20Filler&tub_filler_mounting_position=FLOOR", nil, color); err != nil {
				return design, err
			}
		}
	} else {
		return design, fmt.Errorf("seemingly impossible tub configuration in layout")
	}

	var currentTub Tub
	if design.Tub != nil {
		tub, err := de.catalog.ProductInfo(ctx, "tubs", *design.Tub)
		if err != nil {
			return design, err
		}
		currentTub = tub.(Tub)
		if currentTub.Type == "" {
			return design, fmt.Errorf("no type found for tub %s", design.Tub.String())
		}
	}
	if design.Tub == nil || currentTub.Type != tubType {
		if design.Tub, err = de.findProduct(ctx,
			string(tubType)+" tubs", fmt.Sprintf("category=Tubs&tub_type=%s", tubType), nil, nil); err != nil {
			return design, err
		}
	}

	return design, nil
}

func (de *DesignEvolver) addOrReplaceShowerSystem(ctx context.Context,
	ssId *uuid.UUID, needTubSpout bool) (*uuid.UUID, error) {

	var familyName *string
	var color *ColorGroup
	needHandShower := false
	if ssId != nil {
		shower, err := de.catalog.ProductInfo(ctx, "shower-systems", *ssId)
		if err != nil {
			return nil, err
		}
		if shower.category() != CategoryShower {
			return nil, fmt.Errorf("expected shower system type for %s but got %s", ssId.String(), shower.category())
		}
		currentShowerSystem := shower.(Shower)
		if currentShowerSystem.HasTubSpout == needTubSpout {
			return ssId, nil
		}
		familyName = &currentShowerSystem.ProductFamilyName
		color = currentShowerSystem.Color
		needHandShower = *currentShowerSystem.HasHandheldShowerhead
	}
	// We shouldn't have to compromise on handshower kit inclusion.
	urlQueryParams := fmt.Sprintf("category=Shower%%20Systems&shower_has_tub_spout=%s&handshower_kit_included=%s",
		strconv.FormatBool(needTubSpout), strconv.FormatBool(needHandShower))
	return de.findProduct(ctx, "suitable shower systems", urlQueryParams, familyName, color)
}

func (de *DesignEvolver) findShowerEnclosure(ctx context.Context,
	enclosureCategory string, maxLen float64) (*uuid.UUID, error) {

	var enclosureTypeFilter string
	if maxLen > 59.625 {
		enclosureTypeFilter = "&shower_enclosure_type=Fixed"
	}
	urlQueryParams := fmt.Sprintf("category=%s%s", enclosureCategory, enclosureTypeFilter)
	return de.findProduct(ctx, enclosureCategory, urlQueryParams, nil, nil)
}

func (de *DesignEvolver) findTile(ctx context.Context,
	location string, productFamilyName *string, color *ColorGroup) (*uuid.UUID, error) {

	urlQueryParams := fmt.Sprintf("category=Tile&tile_location=%s", location)
	target := fmt.Sprintf("tile for the %s", location)
	return de.findProduct(ctx, target, urlQueryParams, productFamilyName, color)
}

func (de *DesignEvolver) findVanity(ctx context.Context,
	maxVanityLengthInches int, productFamilyName *string, color *ColorGroup) (*uuid.UUID, error) {

	urlQueryParams := fmt.Sprintf("category=Vanities&vanity_length_lte=%d", maxVanityLengthInches)
	target := fmt.Sprintf("vanity <= %d inches", maxVanityLengthInches)
	return de.findProduct(ctx, target, urlQueryParams, productFamilyName, color)
}

func (de *DesignEvolver) findMirror(ctx context.Context,
	maxMirrorWidthInches int, productFamilyName *string, color *ColorGroup) (*uuid.UUID, error) {

	urlQueryParams := fmt.Sprintf("category=Mirror&mirror_width_lte=%d", maxMirrorWidthInches)
	target := fmt.Sprintf("mirror <= %d inches", maxMirrorWidthInches)
	return de.findProduct(ctx, target, urlQueryParams, productFamilyName, color)
}

func (de *DesignEvolver) findLighting(ctx context.Context,
	maxLightingLengthInches int, productFamilyName *string, color *ColorGroup) (*uuid.UUID, error) {

	urlQueryParams := fmt.Sprintf("category=Decorative%%20Lighting&lighting_length_lte=%d", maxLightingLengthInches)
	target := fmt.Sprintf("decorative lighting <= %d inches", maxLightingLengthInches)
	return de.findProduct(ctx, target, urlQueryParams, productFamilyName, color)
}

func (de *DesignEvolver) findProduct(ctx context.Context, target, baseQueryParams string,
	productFamilyName *string, color *ColorGroup) (*uuid.UUID, error) {

	urlQueryParams := baseQueryParams
	if productFamilyName != nil {
		fam := url.QueryEscape(*productFamilyName)
		urlQueryParams += fmt.Sprintf("&collection=%s", fam)
	}
	if color != nil {
		urlQueryParams += fmt.Sprintf("&colors=%s", *color)
	}
	vanityUUIDs, err := de.productSearch.FindProducts(ctx, urlQueryParams)
	if err != nil {
		return nil, err
	}
	if len(vanityUUIDs) == 0 {
		var c ColorGroup
		if color != nil {
			c = *color
		}
		errMsg := fmt.Sprintf("no %s %s found", c, target)
		if productFamilyName == nil {
			return nil, errors.New(errMsg)
		}
		errMsg += fmt.Sprintf(" in the %s collection so trying again without the collection filter", *productFamilyName)
		log.Println(errMsg)
		return de.findProduct(ctx, target, baseQueryParams, nil, color)
	}
	return &vanityUUIDs[0], nil
}

func calculateMaxRelativeProductLength(numberOfSinks uint8, sinkOffset float64, vanityLength float64) float64 {
	sinkOffsetAbs := math.Abs(sinkOffset)
	if numberOfSinks == 1 {
		return vanityLength
	}
	return math.Min(sinkOffsetAbs, (vanityLength/2)-sinkOffsetAbs) * 2
}

// EvolveProjectDesignsForLayout evolves multiple designs concurrently for a given layout.
// It presents the successfully evolved designs via the presenter's PresentDesigns method,
// or calls PresentError if any failures occur during the evolution process.
func (de *DesignEvolver) EvolveProjectDesignsForLayout(ctx context.Context,
	projectId entities.ProjectId, layout entities.RoomLayout, presenter DesignsPresenter) {

	if IsNil(presenter) {
		panic("presenter cannot be nil")
	}

	designs, err := de.designRepo.DesignsForProject(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	if len(designs) == 0 {
		de.logger.DebugContext(ctx, "No designs found for project", slog.String("projectId", string(projectId)))
		presenter.PresentDesigns(ctx, []Design{})
		return
	}

	// Structure to hold results with their original index
	type result struct {
		design Design
		err    error
		index  int
	}

	results := make([]result, len(designs))
	var wg sync.WaitGroup
	start := time.Now()

	// Process each design concurrently
	for i, design := range designs {
		wg.Add(1)
		go func(index int, d Design) {
			defer wg.Done()

			evolvedDesign, err := de.EvolveDesignForLayout(ctx, d, layout)
			results[index] = result{
				design: evolvedDesign,
				err:    err,
				index:  index,
			}
		}(i, design)
	}
	wg.Wait()

	// Collect successful results and errors
	var successfulDesigns []Design
	var errors []error
	for _, res := range results {
		if res.err != nil {
			errors = append(errors, res.err)
			de.logger.ErrorContext(ctx, "Failed to evolve design for layout", slog.String("error", res.err.Error()),
				slog.String("designId", res.design.ID.String()),
				slog.String("projectId", string(projectId)),
				slog.String("layoutId", layout.Id.String()),
			)
		} else {
			successfulDesigns = append(successfulDesigns, res.design)
		}
	}
	duration := time.Since(start)
	de.logger.InfoContext(ctx, "Evolved designs for layout",
		slog.String("projectId", string(projectId)),
		slog.Int("successful", len(successfulDesigns)),
		slog.Int("failed", len(errors)),
		slog.String("latency", duration.String()),
	)

	if len(errors) > 0 {
		// Merge all errors into a single error message
		var errorMessages []string
		for _, err := range errors {
			errorMessages = append(errorMessages, err.Error())
		}
		mergedError := fmt.Errorf("failed to evolve %d design(s): %s",
			len(errors), strings.Join(errorMessages, "; "))
		presenter.PresentError(mergedError)
		return
	}

	// TODO: actually save the new designs to the repo for the project.
	// We can leave the UUIDs as-is & update them in-place for now.
	// Once layout versioning is a thing then we can create new designs with new UUIDs.
	// When we do that, we may need to update the active design for the project.
	presenter.PresentDesigns(ctx, successfulDesigns)
}
