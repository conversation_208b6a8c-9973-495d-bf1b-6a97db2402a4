package usecases

import (
	"context"
	"log/slog"

	"github.com/google/uuid"
)

// CartInclusionRetriever handles reading cart inclusions
type CartInclusionRetriever struct {
	cartInclusionRepository cartInclusionRepositoryReplica
	logger                  *slog.Logger
}

// NewCartInclusionRetriever creates a new CartInclusionRetriever
func NewCartInclusionRetriever(cartInclusionRepository cartInclusionRepositoryReplica, logger *slog.Logger) *CartInclusionRetriever {
	if IsNil(cartInclusionRepository) {
		panic("cartInclusionRepository cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &CartInclusionRetriever{cartInclusionRepository: cartInclusionRepository, logger: logger}
}

// RetrieveCartInclusions retrieves all cart inclusions for a design
func (r *CartInclusionRetriever) RetrieveCartInclusions(ctx context.Context, presenter <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Presenter, designId uuid.UUID) {
	r.logger.DebugContext(ctx, "Starting cart inclusions retrieval",
		slog.String("operation", "retrieve_cart_inclusions"),
		slog.String("designId", designId.String()))

	cartInclusions, err := r.cartInclusionRepository.CartInclusionsForDesign(ctx, designId)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to retrieve cart inclusions",
			slog.String("operation", "retrieve_cart_inclusions"),
			slog.String("designId", designId.String()),
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}

	r.logger.DebugContext(ctx, "Successfully retrieved cart inclusions",
		slog.String("operation", "retrieve_cart_inclusions"),
		slog.String("designId", designId.String()),
		slog.Int("count", len(cartInclusions)),
		slog.String("status", "success"))

	presenter.PresentCartInclusions(ctx, cartInclusions)
}
