package usecases

import (
	"context"
	"database/sql"
	"log/slog"
)

type DesignSaver struct {
	designRepo designRepository
	catalog    catalog
	genAI      genAI
	logger     *slog.Logger
}

func NewDesignSaver(designRepo designRepository, catalog catalog, genAI genAI, logger *slog.Logger) *DesignSaver {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	if IsNil(catalog) {
		panic("catalog cannot be nil")
	}
	if IsNil(genAI) {
		panic("genAI cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignSaver{designRepo: designRepo, catalog: catalog, genAI: genAI, logger: logger}
}

func (dc *DesignSaver) SaveDesign(ctx context.Context, presenter DesignMutationOutcomePresenter, design Design) {
	dc.logger.InfoContext(ctx, "Starting design save operation",
		slog.String("operation", "save_design"),
		slog.String("projectId", design.ProjectID.String()),
		slog.String("designId", design.ID.String()),
		slog.Bool("has_description", design.Description.Valid),
		slog.Bool("has_title", design.Title.Valid))

	if !design.Description.Valid {
		dc.logger.InfoContext(ctx, "Generating title & description for design...",
			slog.String("operation", "generate_ai_content"),
			slog.String("projectId", design.ProjectID.String()),
			slog.String("designId", design.ID.String()))
		productDescriptions, err := dc.catalog.ProductDescriptionsForDesign(ctx, design)
		if err != nil {
			dc.logger.ErrorContext(ctx, "Failed to build product descriptions for design",
				slog.String("designId", design.ID.String()), slog.String("projectId", design.ProjectID.String()),
				slog.String("error", err.Error()))
		} else {
			title, description, err := dc.genAI.GenerateDesignTitleAndDescription(ctx, productDescriptions)
			if err != nil {
				// Log the error but continue to save the design without the generated content.
				dc.logger.ErrorContext(ctx, "Failed to generate title & description for design",
					slog.String("operation", "generate_ai_content"),
					slog.String("designId", design.ID.String()),
					slog.String("projectId", design.ProjectID.String()),
					slog.String("error", err.Error()))
			} else {
				dc.logger.InfoContext(ctx, "Successfully generated AI content",
					slog.String("operation", "generate_ai_content"),
					slog.String("designId", design.ID.String()),
					slog.String("projectId", design.ProjectID.String()),
					slog.Int("title_length", len(title)),
					slog.Int("description_length", len(description)))
				design.Title = sql.NullString{String: title, Valid: true}
				design.Description = sql.NullString{String: description, Valid: true}
			}
		}
	} else if !design.Title.Valid {
		design.Title = sql.NullString{String: "Custom design", Valid: true}
		dc.logger.DebugContext(ctx, "Set default title for design",
			slog.String("designId", design.ID.String()))
	}

	var err error
	if design.ID, err = dc.designRepo.UpsertDesign(ctx, design); err != nil {
		dc.logger.ErrorContext(ctx, "Failed to save design to repository",
			slog.String("operation", "save_design"),
			slog.String("designId", design.ID.String()),
			slog.String("projectId", design.ProjectID.String()),
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}
	dc.logger.InfoContext(ctx, "Successfully saved design",
		slog.String("operation", "save_design"),
		slog.String("designId", design.ID.String()),
		slog.String("projectId", design.ProjectID.String()),
		slog.String("status", "success"))
	presenter.ConveySuccessWithResource(design, Updated)
}
