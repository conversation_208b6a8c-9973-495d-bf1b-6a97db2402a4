package usecases_test

import (
	"context"
	"log/slog"
	"net/url"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type FakeRenditionCreationOutcomePresenter struct {
	PresentErrorCalls              []error
	ConveySuccessWithResourceCalls []entities.Rendition
}

func NewFakeRenditionCreationOutcomePresenter() *FakeRenditionCreationOutcomePresenter {
	return &FakeRenditionCreationOutcomePresenter{
		PresentErrorCalls:              make([]error, 0),
		ConveySuccessWithResourceCalls: make([]entities.Rendition, 0),
	}
}

func (f *FakeRenditionCreationOutcomePresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

func (f *FakeRenditionCreationOutcomePresenter) ConveySuccessWithResource(rendition entities.Rendition) {
	f.ConveySuccessWithResourceCalls = append(f.ConveySuccessWithResourceCalls, rendition)
}

func TestNewRenditionCreator(t *testing.T) {
	t.Run("should create creator with valid repository and logger", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		logger := slog.Default()
		creator := usecases.NewRenditionCreator(repo, logger)
		assert.NotNil(t, creator)
	})

	t.Run("should create creator with valid repository and nil logger", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		creator := usecases.NewRenditionCreator(repo, nil)
		assert.NotNil(t, creator)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewRenditionCreator(nil, slog.Default())
		})
	})
}

func TestRenditionCreator_Create(t *testing.T) {
	ctx := context.Background()

	createTestRendition := func() entities.Rendition {
		testURL, _ := url.Parse("https://example.com/test-rendition.jpg")
		return entities.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    entities.RenditionPending,
			URL:       testURL,
		}
	}

	createTestDesign := func(t *testing.T, repo *gateways.FakeRelDb) uuid.UUID {
		t.Helper()
		designId := uuid.New()
		design := usecases.Design{
			ID:        designId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
		}
		_, err := repo.UpsertDesign(ctx, design)
		require.NoError(t, err)
		return designId
	}

	t.Run("should create rendition successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		creator := usecases.NewRenditionCreator(repo, slog.Default())
		presenter := NewFakeRenditionCreationOutcomePresenter()

		designId := createTestDesign(t, repo)
		rendition := createTestRendition()

		creator.Create(ctx, designId, rendition, presenter)

		// Verify success was called
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Len(t, presenter.ConveySuccessWithResourceCalls, 1, "Expected ConveySuccessWithResource to be called once")

		// Verify the rendition was returned with an ID
		returnedRendition := presenter.ConveySuccessWithResourceCalls[0]
		assert.NotEqual(t, uuid.UUID{}, returnedRendition.Id, "Expected rendition to have a valid ID")
		assert.Equal(t, rendition.Status, returnedRendition.Status)
		assert.Equal(t, rendition.URL, returnedRendition.URL)

		// Verify the rendition was actually stored in the repository
		storedRenditions, err := repo.RenditionsForDesign(ctx, designId)
		require.NoError(t, err)
		assert.Len(t, storedRenditions, 1, "Expected one rendition to be stored")
		assert.Equal(t, returnedRendition.Id, storedRenditions[0].Id)
	})

	t.Run("should handle rendition without URL", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		creator := usecases.NewRenditionCreator(repo, slog.Default())
		presenter := NewFakeRenditionCreationOutcomePresenter()

		designId := createTestDesign(t, repo)
		rendition := entities.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    entities.RenditionPending,
			URL:       nil, // No URL
		}

		creator.Create(ctx, designId, rendition, presenter)

		// Should still succeed since URL is optional for non-completed renditions
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Len(t, presenter.ConveySuccessWithResourceCalls, 1, "Expected ConveySuccessWithResource to be called once")

		returnedRendition := presenter.ConveySuccessWithResourceCalls[0]
		assert.Nil(t, returnedRendition.URL, "Expected URL to remain nil")
	})
}
