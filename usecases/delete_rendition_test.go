package usecases_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestNewRenditionDeleter(t *testing.T) {
	t.Run("should create deleter with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		deleter := usecases.NewRenditionDeleter(repo)
		assert.NotNil(t, deleter)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewRenditionDeleter(nil)
		})
	})
}

func TestRenditionDeleter_DeleteRendition(t *testing.T) {
	ctx := context.Background()

	t.Run("should delete rendition successfully with valid UUID", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestOutcomePresenter()
		deleter := usecases.NewRenditionDeleter(repo)

		// First, create a design to associate the rendition with
		designId := uuid.New()
		design := usecases.Design{
			ID:        designId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
		}
		_, err := repo.UpsertDesign(ctx, design)
		require.NoError(t, err)

		// Create a rendition to delete
		rendition := entities.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    entities.RenditionPending,
		}
		renditionId, err := repo.InsertRendition(ctx, designId, rendition)
		require.NoError(t, err)

		// Verify the rendition exists before deletion
		renditions, err := repo.Renditions(ctx, []uuid.UUID{renditionId})
		require.NoError(t, err)
		require.Len(t, renditions, 1)

		// Delete the rendition
		deleter.DeleteRendition(ctx, presenter, renditionId)

		// Verify the operation was successful
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed")
		assert.False(t, presenter.WasError(), "Expected no error")

		// Verify the rendition was actually deleted
		renditions, err = repo.Renditions(ctx, []uuid.UUID{renditionId})
		require.NoError(t, err)
		assert.Empty(t, renditions, "Expected rendition to be deleted")
	})

	t.Run("should present error when rendition ID is zero UUID", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestOutcomePresenter()
		deleter := usecases.NewRenditionDeleter(repo)

		var zeroUUID uuid.UUID
		deleter.DeleteRendition(ctx, presenter, zeroUUID)

		// Verify the operation failed with the expected error
		assert.True(t, presenter.WasError(), "Expected operation to fail")
		assert.False(t, presenter.WasSuccessful(), "Expected no success")
		assert.Equal(t, usecases.ErrInvalidPayload, presenter.GetLastError())
	})

	t.Run("should present error when rendition ID is nil UUID", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestOutcomePresenter()
		deleter := usecases.NewRenditionDeleter(repo)

		deleter.DeleteRendition(ctx, presenter, uuid.Nil)

		// Verify the operation failed with the expected error
		assert.True(t, presenter.WasError(), "Expected operation to fail")
		assert.False(t, presenter.WasSuccessful(), "Expected no success")
		assert.Equal(t, usecases.ErrInvalidPayload, presenter.GetLastError())
	})

	t.Run("should present error when rendition does not exist", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestOutcomePresenter()
		deleter := usecases.NewRenditionDeleter(repo)

		// Try to delete a rendition that doesn't exist
		nonExistentId := uuid.New()
		deleter.DeleteRendition(ctx, presenter, nonExistentId)

		// Verify the operation failed with the expected error
		assert.True(t, presenter.WasError(), "Expected operation to fail")
		assert.False(t, presenter.WasSuccessful(), "Expected no success")
		assert.Equal(t, usecases.ErrNotFound, presenter.GetLastError())
	})

	t.Run("should remove rendition from design index when deleted", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewTestOutcomePresenter()
		deleter := usecases.NewRenditionDeleter(repo)

		// First, create a design to associate the rendition with
		designId := uuid.New()
		design := usecases.Design{
			ID:        designId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
		}
		_, err := repo.UpsertDesign(ctx, design)
		require.NoError(t, err)

		// Create multiple renditions for the same design
		rendition1 := entities.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    entities.RenditionPending,
		}
		rendition2 := entities.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    entities.RenditionCompleted,
		}

		rendition1Id, err := repo.InsertRendition(ctx, designId, rendition1)
		require.NoError(t, err)
		rendition2Id, err := repo.InsertRendition(ctx, designId, rendition2)
		require.NoError(t, err)

		// Verify both renditions exist for the design
		renditionsForDesign, err := repo.RenditionsForDesign(ctx, designId)
		require.NoError(t, err)
		assert.Len(t, renditionsForDesign, 2)

		// Delete one rendition
		deleter.DeleteRendition(ctx, presenter, rendition1Id)

		// Verify the operation was successful
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed")
		assert.False(t, presenter.WasError(), "Expected no error")

		// Verify only one rendition remains for the design
		renditionsForDesign, err = repo.RenditionsForDesign(ctx, designId)
		require.NoError(t, err)
		assert.Len(t, renditionsForDesign, 1)
		assert.Equal(t, rendition2Id, renditionsForDesign[0].Id)

		// Verify the deleted rendition is not accessible by ID
		renditions, err := repo.Renditions(ctx, []uuid.UUID{rendition1Id})
		require.NoError(t, err)
		assert.Empty(t, renditions, "Expected deleted rendition to not be found")
	})

	t.Run("should handle deletion of rendition with different statuses", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()

		// First, create a design to associate the rendition with
		designId := uuid.New()
		design := usecases.Design{
			ID:        designId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
		}
		_, err := repo.UpsertDesign(ctx, design)
		require.NoError(t, err)

		// Test deletion of renditions with different statuses
		statuses := []entities.RenditionStatus{
			entities.RenditionPending,
			entities.RenditionStarted,
			entities.RenditionCompleted,
			entities.RenditionOutdated,
			entities.RenditionArchived,
		}

		for _, status := range statuses {
			rendition := entities.Rendition{
				Id:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
				Status:    status,
			}

			renditionId, err := repo.InsertRendition(ctx, designId, rendition)
			require.NoError(t, err)

			// Reset presenter for each test
			presenter := NewTestOutcomePresenter()
			deleter := usecases.NewRenditionDeleter(repo)

			// Delete the rendition
			deleter.DeleteRendition(ctx, presenter, renditionId)

			// Verify the operation was successful regardless of status
			assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed for status %s", status)
			assert.False(t, presenter.WasError(), "Expected no error for status %s", status)

			// Verify the rendition was actually deleted
			renditions, err := repo.Renditions(ctx, []uuid.UUID{renditionId})
			require.NoError(t, err)
			assert.Empty(t, renditions, "Expected rendition with status %s to be deleted", status)
		}
	})
}
