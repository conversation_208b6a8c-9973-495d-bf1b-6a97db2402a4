package usecases

import (
	"context"
	"log"

	"github.com/google/uuid"
)

type DesignUpdater struct {
	designRepo designRepository
}

func NewDesignUpdater(designRepo designRepository) *DesignUpdater {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	return &DesignUpdater{designRepo: designRepo}
}

func (du *DesignUpdater) UpdateDesign(ctx context.Context, presenter DesignMutationOutcomePresenter, diff DesignDiff) {
	var zeroUUID uuid.UUID
	if diff.ID == zeroUUID || diff.ID == uuid.Nil {
		log.Println("Design ID is nil when updating design.")
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	design, err := du.designRepo.ReadDesign(ctx, diff.ID)
	if err != nil {
		log.Printf("Failed to read design when updating design: %v", err)
		presenter.PresentError(ErrNotFound)
		return
	}
	design = MergeDesigns(design, diff)
	if _, err := du.designRepo.UpsertDesign(ctx, design); err != nil {
		presenter.PresentError(err)
		return
	}
	if err := du.designRepo.MarkRenditionsOutdatedForDesign(ctx, diff.ID); err != nil {
		log.Printf("Failed to mark renditions as outdated for design %s: %v", diff.ID, err)
	}
	presenter.ConveySuccessWithResource(design, Updated)
}
