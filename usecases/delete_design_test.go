package usecases_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Fake implementations are in test_helpers_test.go

func TestNewDesignDeleter(t *testing.T) {
	t.Run("should create deleter with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		deleter := usecases.NewDesignDeleter(repo)
		assert.NotNil(t, deleter)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewDesignDeleter(nil)
		})
	})
}

func TestDesignDeleter_DeleteDesign(t *testing.T) {
	ctx := context.Background()

	t.Run("should delete design successfully with valid UUID", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeDesignMutationOutcomePresenter()
		deleter := usecases.NewDesignDeleter(repo)

		// First, create a design to delete
		validId := uuid.New()
		testDesign := usecases.Design{
			ID:        validId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
		}
		_, err := repo.UpsertDesign(ctx, testDesign)
		require.NoError(t, err)

		deleter.DeleteDesign(ctx, presenter, validId)

		// Verify success was conveyed
		AssertConveySuccessCalled(t, presenter)
		AssertPresentErrorNotCalled(t, presenter)
	})

	t.Run("should present error when design ID is zero UUID", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeDesignMutationOutcomePresenter()
		deleter := usecases.NewDesignDeleter(repo)

		var zeroUUID uuid.UUID // This creates a zero UUID (00000000-0000-0000-0000-000000000000)
		deleter.DeleteDesign(ctx, presenter, zeroUUID)

		// Verify error was presented
		AssertPresentErrorCalled(t, presenter, usecases.ErrInvalidPayload)
		assert.Empty(t, presenter.ConveySuccessCalls, "Expected ConveySuccess not to be called")
	})

	t.Run("should present error when design ID is uuid.Nil", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeDesignMutationOutcomePresenter()
		deleter := usecases.NewDesignDeleter(repo)

		deleter.DeleteDesign(ctx, presenter, uuid.Nil)

		// Verify error was presented
		AssertPresentErrorCalled(t, presenter, usecases.ErrInvalidPayload)
		assert.Empty(t, presenter.ConveySuccessCalls, "Expected ConveySuccess not to be called")
	})

	t.Run("should handle repository error during deletion", func(t *testing.T) {
		// For integration testing, we test with a design that doesn't exist
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeDesignMutationOutcomePresenter()
		deleter := usecases.NewDesignDeleter(repo)

		// Try to delete a design that doesn't exist
		nonExistentId := uuid.New()
		deleter.DeleteDesign(ctx, presenter, nonExistentId)

		// Verify error was presented (design not found)
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Empty(t, presenter.ConveySuccessCalls, "Expected ConveySuccess not to be called")
	})

	t.Run("should handle different valid UUID formats", func(t *testing.T) {
		// Test with various valid UUIDs to ensure they all work
		validUUIDs := []uuid.UUID{
			uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			uuid.MustParse("ffffffff-ffff-ffff-ffff-ffffffffffff"),
			uuid.New(),
		}

		for i, validId := range validUUIDs {
			t.Run(fmt.Sprintf("valid_uuid_%d", i), func(t *testing.T) {
				repo := gateways.NewFakeRelDb()
				presenter := NewFakeDesignMutationOutcomePresenter()
				deleter := usecases.NewDesignDeleter(repo)

				// First, create a design to delete
				testDesign := usecases.Design{
					ID:        validId,
					ProjectID: "TEST-PROJECT",
					Status:    usecases.Preview,
				}
				_, err := repo.UpsertDesign(ctx, testDesign)
				require.NoError(t, err)

				deleter.DeleteDesign(ctx, presenter, validId)

				// Verify success was conveyed
				AssertConveySuccessCalled(t, presenter)
				AssertPresentErrorNotCalled(t, presenter)
			})
		}
	})
}

// Test to verify the zero UUID detection logic specifically
func TestZeroUUIDDetection(t *testing.T) {
	var zeroUUID uuid.UUID

	t.Run("zero UUID should be detected correctly", func(t *testing.T) {
		// Verify that a zero-value UUID is indeed all zeros
		expected := "00000000-0000-0000-0000-000000000000"
		assert.Equal(t, expected, zeroUUID.String())
	})

	t.Run("uuid.Nil comparison", func(t *testing.T) {
		// Test the relationship between zero UUID and uuid.Nil
		// Based on the memory, these might not be equal in this codebase
		t.Logf("Zero UUID: %s", zeroUUID.String())
		t.Logf("uuid.Nil: %s", uuid.Nil.String())

		// The test should verify the actual behavior in the codebase
		// This helps document the UUID handling quirk mentioned in the memory
	})
}
