package usecases

import (
	"context"
	"log/slog"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type RenditionCreator struct {
	renditionRepo renditionRepository
	logger        *slog.Logger
}

func NewRenditionCreator(renditionRepo renditionRepository, logger *slog.Logger) *RenditionCreator {
	if IsNil(renditionRepo) {
		panic("renditionRepo cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &RenditionCreator{renditionRepo: renditionRepo, logger: logger}
}

func (r *RenditionCreator) Create(ctx context.Context,
	designId uuid.UUID, rendition entities.Rendition, presenter RenditionCreationOutcomePresenter) {

	id, err := r.renditionRepo.InsertRendition(ctx, designId, rendition)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	rendition.Id = id
	presenter.ConveySuccessWithResource(rendition)
}
