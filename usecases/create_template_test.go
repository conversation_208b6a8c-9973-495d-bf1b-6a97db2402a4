package usecases_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Fake implementations are in test_helpers_test.go

// FailingTemplateRepository wraps FakeRelDb and can simulate failures for template operations
type FailingTemplateRepository struct {
	*gateways.FakeRelDb
	ShouldFail bool
}

func (r *FailingTemplateRepository) InsertTemplate(ctx context.Context,
	template usecases.Template, legacyId string) (uuid.UUID, error) {

	if r.ShouldFail {
		return uuid.UUID{}, errors.New("database constraint violation")
	}
	return r.FakeRelDb.InsertTemplate(ctx, template, legacyId)
}

func (r *FailingTemplateRepository) TemplatesById(ctx context.Context, templateIds []uuid.UUID) ([]usecases.Template, error) {
	if r.ShouldFail {
		return nil, errors.New("database read failure")
	}
	return r.FakeRelDb.TemplatesById(ctx, templateIds)
}

func TestNewTemplateCreater(t *testing.T) {
	t.Run("should create creater with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		creater := usecases.NewTemplateCreater(repo, nil)
		assert.NotNil(t, creater)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewTemplateCreater(nil, nil)
		})
	})
}

func TestTemplateCreater_CreateTemplate(t *testing.T) {
	ctx := context.Background()

	testTemplate := usecases.Template{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Name:            "Test Template",
		Description:     "A test template",
		Inspiration:     "Modern design",
		Atmosphere:      []string{"calm", "modern"},
		ColorPalette:    []string{"white", "gray"},
		MaterialPalette: []string{"marble", "wood"},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile: &[]uuid.UUID{uuid.New()}[0],
			Lighting:  &[]uuid.UUID{uuid.New()}[0],
			Mirror:    &[]uuid.UUID{uuid.New()}[0],
			Paint:     &[]uuid.UUID{uuid.New()}[0],
			Shelving:  &[]uuid.UUID{uuid.New()}[0],
			Toilet:    &[]uuid.UUID{uuid.New()}[0],
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.VanityWall,
	}

	t.Run("should create template successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := &FakeTemplateCreationOutcomePresenter{}
		creater := usecases.NewTemplateCreater(repo, nil)

		creater.CreateTemplate(ctx, presenter, testTemplate, "00")

		// Verify template was created successfully
		assert.Len(t, presenter.ConveySuccessWithNewResourceCalls, 1, "Should have called ConveySuccessWithNewResource once")
		assert.Empty(t, presenter.PresentErrorCalls, "Should not have presented any errors")

		createdTemplate := presenter.ConveySuccessWithNewResourceCalls[0]
		assert.Equal(t, testTemplate.Name, createdTemplate.Name)
		assert.Equal(t, testTemplate.Description, createdTemplate.Description)
		assert.NotEqual(t, uuid.UUID{}, createdTemplate.ID, "Created template should have a valid ID")
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		// Create a failing repository
		repo := &FailingTemplateRepository{
			FakeRelDb:  gateways.NewFakeRelDb(),
			ShouldFail: true,
		}
		presenter := &FakeTemplateCreationOutcomePresenter{}
		creater := usecases.NewTemplateCreater(repo, nil)

		creater.CreateTemplate(ctx, presenter, testTemplate, "00")

		// Verify error was presented
		assert.Len(t, presenter.PresentErrorCalls, 1, "Should have presented an error")
		assert.Empty(t, presenter.ConveySuccessWithNewResourceCalls, "Should not have called ConveySuccessWithNewResource")
		assert.Contains(t, presenter.PresentErrorCalls[0].Error(),
			"database constraint violation", "Error should contain expected message")
	})

	t.Run("should present error when template name is empty", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := &FakeTemplateCreationOutcomePresenter{}
		creater := usecases.NewTemplateCreater(repo, nil)

		emptyNameTemplate := testTemplate
		emptyNameTemplate.Name = ""

		creater.CreateTemplate(ctx, presenter, emptyNameTemplate, "00")

		// Verify error was presented and success was not called
		assert.Len(t, presenter.PresentErrorCalls, 1, "Should have presented an error")
		assert.Empty(t, presenter.ConveySuccessWithNewResourceCalls, "Should not have called ConveySuccessWithNewResource")
		assert.Equal(t, usecases.ErrInvalidPayload, presenter.PresentErrorCalls[0], "Should present invalid payload error")
	})

	t.Run("should handle template with vanity scaling options", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := &FakeTemplateCreationOutcomePresenter{}
		creater := usecases.NewTemplateCreater(repo, nil)

		templateWithVanityOptions := testTemplate
		templateWithVanityOptions.VanityScalingOptions = map[int]usecases.VanityScalingOption{
			36: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
			48: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		}

		creater.CreateTemplate(ctx, presenter, templateWithVanityOptions, "00")

		// Verify template was created successfully
		assert.Len(t, presenter.ConveySuccessWithNewResourceCalls, 1, "Should have called ConveySuccessWithNewResource once")
		assert.Empty(t, presenter.PresentErrorCalls, "Should not have presented any errors")

		createdTemplate := presenter.ConveySuccessWithNewResourceCalls[0]
		assert.Equal(t, templateWithVanityOptions.Name, createdTemplate.Name)
		assert.Equal(t, templateWithVanityOptions.VanityScalingOptions, createdTemplate.VanityScalingOptions)
		assert.NotEqual(t, uuid.UUID{}, createdTemplate.ID, "Created template should have a valid ID")
	})

	t.Run("should handle template with provenance data", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := &FakeTemplateCreationOutcomePresenter{}
		creater := usecases.NewTemplateCreater(repo, nil)

		templateWithProvenance := testTemplate
		templateWithProvenance.TemplateProvenance = usecases.TemplateProvenance{
			LightingBrand: &[]string{"Test Lighting Brand"}[0],
			PlumbingBrand: &[]string{"Test Plumbing Brand"}[0],
			ToiletBrand:   &[]string{"Test Toilet Brand"}[0],
			VanityBrand:   &[]string{"Test Vanity Brand"}[0],
			VanityStorage: &[]string{"Test Storage"}[0],
		}

		creater.CreateTemplate(ctx, presenter, templateWithProvenance, "00")

		// Verify template was created successfully
		assert.Len(t, presenter.ConveySuccessWithNewResourceCalls, 1, "Should have called ConveySuccessWithNewResource once")
		assert.Empty(t, presenter.PresentErrorCalls, "Should not have presented any errors")

		createdTemplate := presenter.ConveySuccessWithNewResourceCalls[0]
		assert.Equal(t, templateWithProvenance.Name, createdTemplate.Name)
		assert.Equal(t, templateWithProvenance.TemplateProvenance, createdTemplate.TemplateProvenance)
		assert.NotEqual(t, uuid.UUID{}, createdTemplate.ID, "Created template should have a valid ID")
	})
}
