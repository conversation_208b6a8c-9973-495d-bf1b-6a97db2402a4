package usecases

import (
	"database/sql"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type WallpaperPlacement string

const (
	NoWallpaper WallpaperPlacement = "None"
	AllWalls    WallpaperPlacement = "AllWalls"
	VanityWall  WallpaperPlacement = "VanityWall"
)

type WallTilePlacement string

const (
	NoWallTile     WallTilePlacement = "None"
	FullWall       WallTilePlacement = "FullWall"
	HalfWall       WallTilePlacement = "HalfWall"
	VanityFullWall WallTilePlacement = "VanityFullWall"
	VanityHalfWall WallTilePlacement = "VanityHalfWall"
)

type DesignStatus string

const (
	Preview  DesignStatus = "Preview"
	Fave     DesignStatus = "Fave"
	Archived DesignStatus = "Archived"
)

// ColorScheme represents the pallette of a design.
type ColorScheme string

const (
	Neutral ColorScheme = "Neutral"
	Bold    ColorScheme = "Bold"
)

type FixedProductSelections struct {
	Toilet          *uuid.UUID `json:"toilet,omitempty"`
	Mirror          *uuid.UUID `json:"mirror,omitempty"`
	Lighting        *uuid.UUID `json:"lighting,omitempty"`
	Paint           *uuid.UUID `json:"paint,omitempty"`
	Shelving        *uuid.UUID `json:"shelving,omitempty"`
	FloorTile       *uuid.UUID `json:"floorTile,omitempty"`
	ShowerFloorTile *uuid.UUID `json:"showerFloorTile,omitempty"`
	ShowerWallTile  *uuid.UUID `json:"showerWallTile,omitempty"`
	TubFiller       *uuid.UUID `json:"tubFiller,omitempty"`
	Wallpaper       *uuid.UUID `json:"wallpaper,omitempty"`
	WallTile        *uuid.UUID `json:"wallTile,omitempty"`
}

type DesignOptions struct {
	ColorScheme *ColorScheme
	Style       *Style
	Title       sql.NullString
	Description sql.NullString
	FixedProductSelections
	Faucet                 *uuid.UUID
	FloorTilePattern       *TilePattern
	ShowerFloorTilePattern *TilePattern
	ShowerSystem           *uuid.UUID
	ShowerWallTilePattern  *TilePattern
	ShowerShortWallTile    *uuid.UUID
	ShowerGlass            *uuid.UUID
	Tub                    *uuid.UUID
	TubDoor                *uuid.UUID
	Vanity                 *uuid.UUID
	WallTilePattern        *TilePattern
	NicheTile              *uuid.UUID

	NumSKUs           sql.NullInt32
	TotalPriceInCents sql.NullInt32
	LeadTimeDays      sql.NullInt32
}
type Design struct {
	ID          uuid.UUID
	ProjectID   entities.ProjectId
	Created     time.Time
	LastUpdated time.Time
	Status      DesignStatus

	WallpaperPlacement WallpaperPlacement
	WallTilePlacement  WallTilePlacement
	DesignOptions
	ShowerGlassVisible bool
	TubDoorVisible     bool
	NichesVisible      bool

	CartInclusions CartInclusions

	Renditions []entities.Rendition
}

// NewDesign creates a new Design with properly initialized properties.
// Both parameters are optional:
//   - id: Design UUID (if nil, generates a new one)
//   - projectId: Project ID (if nil, remains empty)
func NewDesign(id *uuid.UUID, projectId *entities.ProjectId) Design {
	var designID uuid.UUID
	if id != nil {
		designID = *id
	} else {
		designID = uuid.New()
	}

	var projectID entities.ProjectId
	if projectId != nil {
		projectID = *projectId
	}

	return Design{
		ID:             designID,
		ProjectID:      projectID,
		CartInclusions: make(CartInclusions),
	}
}

// NewEmptyDesign creates a new Design with auto-generated ID and empty ProjectID.
// This is a convenience function equivalent to NewDesign(nil, nil).
func NewEmptyDesign() Design {
	return NewDesign(nil, nil)
}

// ClearCartInclusions removes all cart inclusions.
func (d *Design) ClearCartInclusions() {
	d.CartInclusions = make(CartInclusions)
}

type DesignDiff struct {
	ID                 uuid.UUID
	Status             *DesignStatus
	WallpaperPlacement *WallpaperPlacement
	WallTilePlacement  *WallTilePlacement
	DesignOptions
	ShowerGlassVisible sql.NullBool
	TubDoorVisible     sql.NullBool
	NichesVisible      sql.NullBool

	Removals map[string]any
}

func (d Design) CategoriesByProductUUID() map[uuid.UUID]string {
	result := make(map[uuid.UUID]string)
	if d.FloorTile != nil {
		result[*d.FloorTile] = "floor tile"
	}
	if d.Toilet != nil {
		result[*d.Toilet] = "toilet"
	}
	if d.Vanity != nil {
		result[*d.Vanity] = "vanity"
	}
	if d.Faucet != nil {
		result[*d.Faucet] = "faucet"
	}
	if d.Mirror != nil {
		result[*d.Mirror] = "mirror"
	}
	if d.Paint != nil {
		result[*d.Paint] = "paint"
	}
	if d.WallTile != nil {
		result[*d.WallTile] = "wall tile"
	}
	if d.ShowerWallTile != nil {
		result[*d.ShowerWallTile] = "shower wall tile"
	}
	if d.ShowerFloorTile != nil {
		result[*d.ShowerFloorTile] = "shower floor tile"
	}
	if d.ShowerSystem != nil {
		result[*d.ShowerSystem] = "shower system"
	}
	if d.ShowerShortWallTile != nil {
		result[*d.ShowerShortWallTile] = "shower short wall tile"
	}
	if d.ShowerGlass != nil {
		result[*d.ShowerGlass] = "shower glass"
	}
	if d.Tub != nil {
		result[*d.Tub] = "tub"
	}
	if d.TubDoor != nil {
		result[*d.TubDoor] = "tub door"
	}
	if d.NicheTile != nil {
		result[*d.NicheTile] = "niche tile"
	}
	if d.Wallpaper != nil {
		result[*d.Wallpaper] = "wallpaper"
	}
	if d.Shelving != nil {
		result[*d.Shelving] = "shelving"
	}
	if d.Lighting != nil {
		result[*d.Lighting] = "lighting"
	}
	return result
}

func MergeDesigns(existing Design, incoming DesignDiff) Design {
	if incoming.Status != nil {
		existing.Status = *incoming.Status
	}
	if incoming.ColorScheme != nil {
		existing.ColorScheme = incoming.ColorScheme
	}
	if incoming.Style != nil {
		existing.Style = incoming.Style
	}
	if incoming.Title.Valid {
		existing.Title = incoming.Title
	}
	if incoming.Description.Valid {
		existing.Description = incoming.Description
	}
	if incoming.FloorTile != nil {
		existing.FloorTile = incoming.FloorTile
	}
	if incoming.Toilet != nil {
		existing.Toilet = incoming.Toilet
	}
	if incoming.Vanity != nil {
		existing.Vanity = incoming.Vanity
	}
	if incoming.Faucet != nil {
		existing.Faucet = incoming.Faucet
	}
	if incoming.Mirror != nil {
		existing.Mirror = incoming.Mirror
	}
	if incoming.FloorTilePattern != nil {
		existing.FloorTilePattern = incoming.FloorTilePattern
	}
	if incoming.Lighting != nil {
		existing.Lighting = incoming.Lighting
	}
	if incoming.NicheTile != nil {
		existing.NicheTile = incoming.NicheTile
	}
	if incoming.Paint != nil {
		existing.Paint = incoming.Paint
	}
	if incoming.Shelving != nil {
		existing.Shelving = incoming.Shelving
	}
	if incoming.ShowerFloorTile != nil {
		existing.ShowerFloorTile = incoming.ShowerFloorTile
	}
	if incoming.ShowerFloorTilePattern != nil {
		existing.ShowerFloorTilePattern = incoming.ShowerFloorTilePattern
	}
	if incoming.ShowerSystem != nil {
		existing.ShowerSystem = incoming.ShowerSystem
	}
	if incoming.ShowerWallTile != nil {
		existing.ShowerWallTile = incoming.ShowerWallTile
	}
	if incoming.ShowerWallTilePattern != nil {
		existing.ShowerWallTilePattern = incoming.ShowerWallTilePattern
	}
	if incoming.ShowerShortWallTile != nil {
		existing.ShowerShortWallTile = incoming.ShowerShortWallTile
	}
	if incoming.ShowerGlass != nil {
		existing.ShowerGlass = incoming.ShowerGlass
	}
	if incoming.Tub != nil {
		existing.Tub = incoming.Tub
	}
	if incoming.TubDoor != nil {
		existing.TubDoor = incoming.TubDoor
	}
	if incoming.TubFiller != nil {
		existing.TubFiller = incoming.TubFiller
	}
	if incoming.WallpaperPlacement != nil {
		existing.WallpaperPlacement = *incoming.WallpaperPlacement
	}
	if incoming.Wallpaper != nil {
		existing.Wallpaper = incoming.Wallpaper
	} else if _, ok := incoming.Removals["wallpaper"]; ok {
		existing.Wallpaper = nil
	}
	if incoming.WallTilePlacement != nil {
		existing.WallTilePlacement = *incoming.WallTilePlacement
	}
	if incoming.WallTile != nil {
		existing.WallTile = incoming.WallTile
	} else if _, ok := incoming.Removals["wallTile"]; ok {
		existing.WallTile = nil
	}
	if incoming.WallTilePattern != nil {
		existing.WallTilePattern = incoming.WallTilePattern
	}
	if incoming.ShowerGlassVisible.Valid {
		existing.ShowerGlassVisible = incoming.ShowerGlassVisible.Bool
	}
	if incoming.TubDoorVisible.Valid {
		existing.TubDoorVisible = incoming.TubDoorVisible.Bool
	}
	if incoming.NichesVisible.Valid {
		existing.NichesVisible = incoming.NichesVisible.Bool
	}
	if incoming.NumSKUs.Valid {
		existing.NumSKUs = incoming.NumSKUs
	}
	if incoming.TotalPriceInCents.Valid {
		existing.TotalPriceInCents = incoming.TotalPriceInCents
	}
	if incoming.LeadTimeDays.Valid {
		existing.LeadTimeDays = incoming.LeadTimeDays
	}
	existing.LastUpdated = time.Now()
	return existing
}
