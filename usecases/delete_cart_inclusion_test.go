package usecases_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestCartInclusionDeleter_DeleteCartInclusion(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	deleter := usecases.NewCartInclusionDeleter(repo, nil)
	presenter := NewFakeOutcomePresenter()

	// Create a test design first
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productId := uuid.New()
	location := usecases.LocationFloor
	inclusion := usecases.CartInclusion{
		ProductID:    productId,
		Location:     location,
		Include:      true,
		QuantityDiff: 2,
	}

	// First save an inclusion
	err = repo.UpsertCartInclusion(ctx, designId, inclusion)
	require.NoError(t, err)

	// Delete the inclusion
	deleter.DeleteCartInclusion(ctx, presenter, designId, productId, location)

	// Verify success
	assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
	assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected success to be called once")

	// Verify inclusion was deleted
	inclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	assert.Len(t, inclusions, 0, "Expected no inclusions after deletion")
}

func TestCartInclusionDeleter_DeleteCartInclusion_NotFound(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	deleter := usecases.NewCartInclusionDeleter(repo, nil)
	presenter := NewFakeOutcomePresenter()

	designId := uuid.New()
	productId := uuid.New()
	location := usecases.LocationWall

	// Try to delete non-existent inclusion
	deleter.DeleteCartInclusion(ctx, presenter, designId, productId, location)

	// Verify error
	assert.Len(t, presenter.PresentErrorCalls, 1, "Expected error for non-existent inclusion")
	assert.Equal(t, usecases.ErrNotFound, presenter.PresentErrorCalls[0])
	assert.Equal(t, 0, presenter.ConveySuccessCalls, "Expected no success calls")
}

func TestCartInclusionDeleter_ClearCartInclusions(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	deleter := usecases.NewCartInclusionDeleter(repo, nil)
	presenter := NewFakeOutcomePresenter()

	// Create a test design first
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Add multiple inclusions
	locations := []usecases.Location{usecases.LocationFloor, usecases.LocationWall, usecases.LocationShowerFloor}
	for i := 0; i < 3; i++ {
		inclusion := usecases.CartInclusion{
			ProductID:    uuid.New(),
			Location:     locations[i],
			Include:      i%2 == 0,
			QuantityDiff: i,
		}
		err := repo.UpsertCartInclusion(ctx, designId, inclusion)
		require.NoError(t, err)
	}

	// Verify inclusions exist
	inclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, inclusions, 3, "Expected 3 inclusions before clearing")

	// Clear all inclusions
	deleter.ClearCartInclusions(ctx, presenter, designId)

	// Verify success
	assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
	assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected success to be called once")

	// Verify all inclusions were deleted
	inclusions, err = repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	assert.Len(t, inclusions, 0, "Expected no inclusions after clearing")
}

func TestCartInclusionDeleter_Constructor(t *testing.T) {
	t.Run("panics with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewCartInclusionDeleter(nil, nil)
		}, "Expected panic with nil repository")
	})

	t.Run("works with nil logger", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		assert.NotPanics(t, func() {
			usecases.NewCartInclusionDeleter(repo, nil)
		}, "Should not panic with nil logger")
	})
}
