package usecases_test

import (
	"context"
	"net/url"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type FakeRenditionsPresenter struct {
	PresentErrorCalls      []error
	PresentRenditionsCalls []PresentRenditionsCall
}

type PresentRenditionsCall struct {
	Ctx        context.Context
	Renditions []entities.Rendition
}

func NewFakeRenditionsPresenter() *FakeRenditionsPresenter {
	return &FakeRenditionsPresenter{
		PresentErrorCalls:      make([]error, 0),
		PresentRenditionsCalls: make([]PresentRenditionsCall, 0),
	}
}

func (f *FakeRenditionsPresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

func (f *FakeRenditionsPresenter) PresentRenditions(ctx context.Context, renditions []entities.Rendition) {
	f.PresentRenditionsCalls = append(f.PresentRenditionsCalls, PresentRenditionsCall{
		Ctx:        ctx,
		Renditions: renditions,
	})
}

func TestNewRenditionRetriever(t *testing.T) {
	t.Run("should create retriever with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		retriever := usecases.NewRenditionRetriever(repo)
		assert.NotNil(t, retriever)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewRenditionRetriever(nil)
		})
	})
}

func TestRenditionRetriever_Renditions(t *testing.T) {
	ctx := context.Background()

	createTestRendition := func(status entities.RenditionStatus) entities.Rendition {
		testURL, _ := url.Parse("https://example.com/test-rendition.jpg")
		return entities.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    status,
			URL:       testURL,
		}
	}

	createTestDesign := func(t *testing.T, repo *gateways.FakeRelDb) uuid.UUID {
		t.Helper()
		designId := uuid.New()
		design := usecases.Design{
			ID:        designId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
		}
		_, err := repo.UpsertDesign(ctx, design)
		require.NoError(t, err)
		return designId
	}

	t.Run("should retrieve renditions by IDs successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		retriever := usecases.NewRenditionRetriever(repo)
		presenter := NewFakeRenditionsPresenter()

		designId := createTestDesign(t, repo)

		// Create and store test renditions
		rendition1 := createTestRendition(entities.RenditionPending)
		rendition2 := createTestRendition(entities.RenditionCompleted)

		id1, err := repo.InsertRendition(ctx, designId, rendition1)
		require.NoError(t, err)
		rendition1.Id = id1

		id2, err := repo.InsertRendition(ctx, designId, rendition2)
		require.NoError(t, err)
		rendition2.Id = id2

		// Retrieve renditions by IDs
		ids := []uuid.UUID{id1, id2}
		retriever.Renditions(ctx, ids, presenter)

		// Verify success
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Len(t, presenter.PresentRenditionsCalls, 1, "Expected PresentRenditions to be called once")

		// Verify the correct renditions were presented
		presentedRenditions := presenter.PresentRenditionsCalls[0].Renditions
		assert.Len(t, presentedRenditions, 2, "Expected two renditions to be presented")

		// Check that both renditions are present (order may vary)
		renditionIds := make([]uuid.UUID, len(presentedRenditions))
		for i, r := range presentedRenditions {
			renditionIds[i] = r.Id
		}
		assert.Contains(t, renditionIds, id1)
		assert.Contains(t, renditionIds, id2)
	})

	t.Run("should present empty list when no renditions found", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		retriever := usecases.NewRenditionRetriever(repo)
		presenter := NewFakeRenditionsPresenter()

		// Try to retrieve non-existent renditions
		nonExistentIds := []uuid.UUID{uuid.New(), uuid.New()}
		retriever.Renditions(ctx, nonExistentIds, presenter)

		// Should present empty list, not error
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Len(t, presenter.PresentRenditionsCalls, 1, "Expected PresentRenditions to be called once")

		presentedRenditions := presenter.PresentRenditionsCalls[0].Renditions
		assert.Empty(t, presentedRenditions, "Expected empty renditions list")
	})

	t.Run("should retrieve single rendition", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		retriever := usecases.NewRenditionRetriever(repo)
		presenter := NewFakeRenditionsPresenter()

		designId := createTestDesign(t, repo)
		rendition := createTestRendition(entities.RenditionStarted)

		id, err := repo.InsertRendition(ctx, designId, rendition)
		require.NoError(t, err)
		rendition.Id = id

		// Retrieve single rendition
		ids := []uuid.UUID{id}
		retriever.Renditions(ctx, ids, presenter)

		// Verify success
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Len(t, presenter.PresentRenditionsCalls, 1, "Expected PresentRenditions to be called once")

		presentedRenditions := presenter.PresentRenditionsCalls[0].Renditions
		assert.Len(t, presentedRenditions, 1, "Expected one rendition to be presented")
		assert.Equal(t, id, presentedRenditions[0].Id)
		assert.Equal(t, entities.RenditionStarted, presentedRenditions[0].Status)
	})
}

func TestRenditionRetriever_RenditionsForDesign(t *testing.T) {
	ctx := context.Background()

	createTestRendition := func(status entities.RenditionStatus) entities.Rendition {
		testURL, _ := url.Parse("https://example.com/test-rendition.jpg")
		return entities.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    status,
			URL:       testURL,
		}
	}

	createTestDesign := func(t *testing.T, repo *gateways.FakeRelDb) uuid.UUID {
		t.Helper()
		designId := uuid.New()
		design := usecases.Design{
			ID:        designId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
		}
		_, err := repo.UpsertDesign(ctx, design)
		require.NoError(t, err)
		return designId
	}

	t.Run("should retrieve renditions for design successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		retriever := usecases.NewRenditionRetriever(repo)
		presenter := NewFakeRenditionsPresenter()

		designId := createTestDesign(t, repo)

		// Create and store multiple renditions for the design
		rendition1 := createTestRendition(entities.RenditionPending)
		rendition2 := createTestRendition(entities.RenditionCompleted)
		rendition3 := createTestRendition(entities.RenditionOutdated)

		_, err := repo.InsertRendition(ctx, designId, rendition1)
		require.NoError(t, err)
		_, err = repo.InsertRendition(ctx, designId, rendition2)
		require.NoError(t, err)
		_, err = repo.InsertRendition(ctx, designId, rendition3)
		require.NoError(t, err)

		// Retrieve renditions for the design
		retriever.RenditionsForDesign(ctx, designId, presenter)

		// Verify success
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Len(t, presenter.PresentRenditionsCalls, 1, "Expected PresentRenditions to be called once")

		presentedRenditions := presenter.PresentRenditionsCalls[0].Renditions
		assert.Len(t, presentedRenditions, 3, "Expected three renditions to be presented")

		// Verify all different statuses are present
		statuses := make([]entities.RenditionStatus, len(presentedRenditions))
		for i, r := range presentedRenditions {
			statuses[i] = r.Status
		}
		assert.Contains(t, statuses, entities.RenditionPending)
		assert.Contains(t, statuses, entities.RenditionCompleted)
		assert.Contains(t, statuses, entities.RenditionOutdated)
	})

	t.Run("should present empty list when design has no renditions", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		retriever := usecases.NewRenditionRetriever(repo)
		presenter := NewFakeRenditionsPresenter()

		designId := createTestDesign(t, repo)

		// Don't add any renditions to the design
		retriever.RenditionsForDesign(ctx, designId, presenter)

		// Should present empty list
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Len(t, presenter.PresentRenditionsCalls, 1, "Expected PresentRenditions to be called once")

		presentedRenditions := presenter.PresentRenditionsCalls[0].Renditions
		assert.Empty(t, presentedRenditions, "Expected empty renditions list")
	})
}
