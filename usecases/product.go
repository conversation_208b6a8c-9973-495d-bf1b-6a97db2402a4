package usecases

import "github.com/google/uuid"

type ProductInfo struct {
	Id                uuid.UUID
	Category          Category
	Name              string
	Length            float64
	ProductFamilyName string
	Color             *ColorGroup
	Description       string
}

func (p ProductInfo) category() Category {
	return p.Category
}

func (p ProductInfo) GetDescription() string {
	return p.Description
}

type RenderableProduct interface {
	category() Category
	GetDescription() string
}

type Faucet struct {
	ProductInfo
	HoleSpacingCompatibility []FaucetHoleSpacing
}

type Lighting struct {
	ProductInfo
	NumberOfBulbs uint8
}

type Mirror struct {
	ProductInfo
	IsMedicineCabinet bool
	IsLighted         bool
	Shape             *MirrorShape
}

type Shelving struct {
	ProductInfo
	ShelfCount uint8
}

type Shower struct {
	ProductInfo
	HasTubSpout           bool
	HasHandheldShowerhead *bool
}

type Tile struct {
	ProductInfo
	AvailableForShowerWall  *bool
	AvailableForShowerFloor *bool
	AvailableForFloor       *bool
}

type Toilet struct {
	ProductInfo
	MountingPosition *ToiletMountingPosition
	HasBidet         bool
}

type Tub struct {
	ProductInfo
	Type  TubType
	Shape *TubShape
}

type TubFiller struct {
	ProductInfo
	MountingPosition *TubFillerMountingPosition
}

type Vanity struct {
	ProductInfo
	FaucetHoleSpacing FaucetHoleSpacing
	NumberOfSinks     uint8
	SinkOffset        float64
}

type Wallpaper struct {
	ProductInfo
	Application WallpaperApplication
	Pattern     WallpaperPattern
}
