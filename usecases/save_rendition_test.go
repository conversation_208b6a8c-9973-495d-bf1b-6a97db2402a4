package usecases_test

import (
	"context"
	"net/url"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestNewRenditionSaver(t *testing.T) {
	t.Run("should create saver with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		saver := usecases.NewRenditionSaver(repo)
		assert.NotNil(t, saver)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewRenditionSaver(nil)
		})
	})
}

func TestRenditionSaver_Save(t *testing.T) {
	ctx := context.Background()

	createTestRendition := func() entities.Rendition {
		testURL, _ := url.Parse("https://example.com/test-rendition.jpg")
		return entities.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    entities.RenditionPending,
			URL:       testURL,
		}
	}

	createTestDesign := func(t *testing.T, repo *gateways.FakeRelDb) uuid.UUID {
		t.Helper()
		designId := uuid.New()
		design := usecases.Design{
			ID:        designId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
		}
		_, err := repo.UpsertDesign(ctx, design)
		require.NoError(t, err)
		return designId
	}

	t.Run("should save rendition successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		saver := usecases.NewRenditionSaver(repo)
		presenter := NewFakeOutcomePresenter()

		designId := createTestDesign(t, repo)
		rendition := createTestRendition()

		saver.Save(ctx, designId, rendition, presenter)

		// Verify success was called
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected ConveySuccess to be called once")

		// Verify the rendition was actually stored in the repository
		storedRenditions, err := repo.RenditionsForDesign(ctx, designId)
		require.NoError(t, err)
		assert.Len(t, storedRenditions, 1, "Expected one rendition to be stored")
		assert.Equal(t, rendition.Status, storedRenditions[0].Status)
		assert.Equal(t, rendition.URL, storedRenditions[0].URL)
	})

	t.Run("should handle rendition without URL", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		saver := usecases.NewRenditionSaver(repo)
		presenter := NewFakeOutcomePresenter()

		designId := createTestDesign(t, repo)
		rendition := entities.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    entities.RenditionPending,
			URL:       nil, // No URL
		}

		saver.Save(ctx, designId, rendition, presenter)

		// Should still succeed since URL is optional for non-completed renditions
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected ConveySuccess to be called once")

		// Verify the rendition was stored
		storedRenditions, err := repo.RenditionsForDesign(ctx, designId)
		require.NoError(t, err)
		assert.Len(t, storedRenditions, 1, "Expected one rendition to be stored")
		assert.Nil(t, storedRenditions[0].URL, "Expected URL to remain nil")
	})

	t.Run("should handle completed rendition with URL", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		saver := usecases.NewRenditionSaver(repo)
		presenter := NewFakeOutcomePresenter()

		designId := createTestDesign(t, repo)
		testURL, _ := url.Parse("https://example.com/completed-rendition.jpg")
		rendition := entities.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    entities.RenditionCompleted,
			URL:       testURL,
		}

		saver.Save(ctx, designId, rendition, presenter)

		// Should succeed
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected ConveySuccess to be called once")

		// Verify the rendition was stored with completed status
		storedRenditions, err := repo.RenditionsForDesign(ctx, designId)
		require.NoError(t, err)
		assert.Len(t, storedRenditions, 1, "Expected one rendition to be stored")
		assert.Equal(t, entities.RenditionCompleted, storedRenditions[0].Status)
		assert.Equal(t, testURL, storedRenditions[0].URL)
	})

	t.Run("should handle multiple renditions for same design", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		saver := usecases.NewRenditionSaver(repo)
		presenter := NewFakeOutcomePresenter()

		designId := createTestDesign(t, repo)

		// Save first rendition
		rendition1 := createTestRendition()
		saver.Save(ctx, designId, rendition1, presenter)

		// Save second rendition
		rendition2 := createTestRendition()
		saver.Save(ctx, designId, rendition2, presenter)

		// Both should succeed
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Equal(t, 2, presenter.ConveySuccessCalls, "Expected ConveySuccess to be called twice")

		// Verify both renditions were stored
		storedRenditions, err := repo.RenditionsForDesign(ctx, designId)
		require.NoError(t, err)
		assert.Len(t, storedRenditions, 2, "Expected two renditions to be stored")
	})
}
