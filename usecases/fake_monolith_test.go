package usecases_test

import (
	"context"
	"sync"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

// FakeMonolith is a shared fake implementation of the monolith interface for testing
type FakeMonolith struct {
	mu                  sync.Mutex
	updateCalls         []MonolithUpdateCall
	getLayoutCalls      []GetLayoutCall
	shouldFailUpdate    bool
	shouldFailGetLayout bool
	updateError         error
	getLayoutError      error
	layoutToReturn      entities.RoomLayout
}

type MonolithUpdateCall struct {
	ProjectId entities.ProjectId
	DesignId  uuid.UUID
}

type GetLayoutCall struct {
	ProjectId entities.ProjectId
}

func NewFakeMonolith() *FakeMonolith {
	return &FakeMonolith{
		updateCalls:    make([]MonolithUpdateCall, 0),
		getLayoutCalls: make([]GetLayoutCall, 0),
		layoutToReturn: entities.RoomLayout{
			Id:        uuid.New(),
			Hash:      12345,
			RawData:   []byte(`{"fake": "layout"}`),
			FloorIds:  []uuid.UUID{uuid.New()},
			Walls:     []entities.Wall{},
			WetAreas:  []entities.WetArea{},
			ToiletIds: []uuid.UUID{},
			Vanities:  []entities.Vanity{},
		},
	}
}

func (m *FakeMonolith) UpdateCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.updateCalls = append(m.updateCalls, MonolithUpdateCall{
		ProjectId: projectId,
		DesignId:  designId,
	})

	if m.shouldFailUpdate {
		return m.updateError
	}
	return nil
}

func (m *FakeMonolith) GetLayoutForProject(ctx context.Context, projectId entities.ProjectId) (entities.RoomLayout, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.getLayoutCalls = append(m.getLayoutCalls, GetLayoutCall{
		ProjectId: projectId,
	})

	if m.shouldFailGetLayout {
		return entities.RoomLayout{}, m.getLayoutError
	}
	return m.layoutToReturn, nil
}

// Helper methods for testing

func (m *FakeMonolith) GetUpdateCalls() []MonolithUpdateCall {
	m.mu.Lock()
	defer m.mu.Unlock()
	return append([]MonolithUpdateCall(nil), m.updateCalls...)
}

func (m *FakeMonolith) GetLayoutCalls() []GetLayoutCall {
	m.mu.Lock()
	defer m.mu.Unlock()
	return append([]GetLayoutCall(nil), m.getLayoutCalls...)
}

func (m *FakeMonolith) SetShouldFailUpdate(shouldFail bool, err error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.shouldFailUpdate = shouldFail
	m.updateError = err
}

func (m *FakeMonolith) SetShouldFailGetLayout(shouldFail bool, err error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.shouldFailGetLayout = shouldFail
	m.getLayoutError = err
}

func (m *FakeMonolith) SetLayoutToReturn(layout entities.RoomLayout) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.layoutToReturn = layout
}

func (m *FakeMonolith) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.updateCalls = make([]MonolithUpdateCall, 0)
	m.getLayoutCalls = make([]GetLayoutCall, 0)
	m.shouldFailUpdate = false
	m.shouldFailGetLayout = false
	m.updateError = nil
	m.getLayoutError = nil
	m.layoutToReturn = entities.RoomLayout{
		Id:        uuid.New(),
		Hash:      12345,
		RawData:   []byte(`{"fake": "layout"}`),
		FloorIds:  []uuid.UUID{uuid.New()},
		Walls:     []entities.Wall{},
		WetAreas:  []entities.WetArea{},
		ToiletIds: []uuid.UUID{},
		Vanities:  []entities.Vanity{},
	}
}
