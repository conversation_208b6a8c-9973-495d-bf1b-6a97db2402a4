package usecases_test

import (
	"context"
	"database/sql"
	"log/slog"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Uses the existing FakeDesignMutationOutcomePresenter from test_helpers_test.go

func TestNewDesignProseRegenerator(t *testing.T) {
	t.Run("should create regenerator with valid dependencies", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		logger := slog.Default()
		regenerator := usecases.NewDesignProseRegenerator(repo, catalog, genAI, logger)
		assert.NotNil(t, regenerator)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		assert.Panics(t, func() {
			usecases.NewDesignProseRegenerator(nil, catalog, genAI, slog.Default())
		})
	})

	t.Run("should panic with nil genAI", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		assert.Panics(t, func() {
			usecases.NewDesignProseRegenerator(repo, catalog, nil, slog.Default())
		})
	})

	t.Run("should create regenerator with nil logger", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		regenerator := usecases.NewDesignProseRegenerator(repo, catalog, genAI, nil)
		assert.NotNil(t, regenerator)
	})
}

func TestDesignProseRegenerator_RegenerateDesignProse(t *testing.T) {
	ctx := context.Background()

	createTestDesign := func(t *testing.T, repo *gateways.FakeRelDb, catalog *gateways.FakeCatalog) uuid.UUID {
		t.Helper()
		designId := uuid.New()

		// Create some test products
		vanityId := uuid.New()
		vanityInfo := usecases.ProductInfo{
			Id:          vanityId,
			Category:    "vanities",
			Name:        "Test Vanity",
			Description: "A beautiful test vanity",
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		toiletId := uuid.New()
		toiletInfo := usecases.ProductInfo{
			Id:          toiletId,
			Category:    "toilets",
			Name:        "Test Toilet",
			Description: "A comfortable test toilet",
		}
		catalog.AddProduct("toilets", toiletId, toiletInfo)

		design := usecases.Design{
			ID:        designId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
			Created:   time.Now(),
			DesignOptions: usecases.DesignOptions{
				Title:       sql.NullString{String: "Original Title", Valid: true},
				Description: sql.NullString{String: "Original Description", Valid: true},
				Vanity:      &vanityId,
				FixedProductSelections: usecases.FixedProductSelections{
					Toilet: &toiletId,
				},
			},
		}
		_, err := repo.UpsertDesign(ctx, design)
		require.NoError(t, err)
		return designId
	}

	t.Run("should regenerate design prose successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		regenerator := usecases.NewDesignProseRegenerator(repo, catalog, genAI, slog.Default())
		presenter := NewFakeDesignMutationOutcomePresenter()

		designId := createTestDesign(t, repo, catalog)
		regenerator.RegenerateDesignProse(ctx, presenter, designId)

		// Verify success was called
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Len(t, presenter.ConveySuccessWithResourceCalls, 1, "Expected ConveySuccessWithResource to be called once")

		// Verify AI was called to generate prose
		assert.Len(t, genAI.GenerateCalls, 1, "Expected AI to be called once for prose generation")

		// Verify the design was updated with generated prose
		design, err := repo.ReadDesign(ctx, designId)
		require.NoError(t, err)
		assert.True(t, design.Title.Valid, "Expected design title to be updated")
		assert.True(t, design.Description.Valid, "Expected design description to be updated")
		assert.Equal(t, "Fake Generated Title", design.Title.String)
		assert.Equal(t, "Fake Generated Description", design.Description.String)
	})

	t.Run("should present error for zero UUID", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		regenerator := usecases.NewDesignProseRegenerator(repo, catalog, genAI, slog.Default())
		presenter := NewFakeDesignMutationOutcomePresenter()

		var zeroUUID uuid.UUID
		regenerator.RegenerateDesignProse(ctx, presenter, zeroUUID)

		// Should present error
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Contains(t, presenter.PresentErrorCalls, usecases.ErrInvalidPayload)
		assert.Empty(t, presenter.ConveySuccessWithResourceCalls, "Expected ConveySuccessWithResource not to be called")

		// AI should not be called
		assert.Empty(t, genAI.GenerateCalls, "Expected AI not to be called")
	})

	t.Run("should present error for uuid.Nil", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		regenerator := usecases.NewDesignProseRegenerator(repo, catalog, genAI, slog.Default())
		presenter := NewFakeDesignMutationOutcomePresenter()

		regenerator.RegenerateDesignProse(ctx, presenter, uuid.Nil)

		// Should present error
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Contains(t, presenter.PresentErrorCalls, usecases.ErrInvalidPayload)
		assert.Empty(t, presenter.ConveySuccessWithResourceCalls, "Expected ConveySuccessWithResource not to be called")

		// AI should not be called
		assert.Empty(t, genAI.GenerateCalls, "Expected AI not to be called")
	})

	t.Run("should present error when design does not exist", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		regenerator := usecases.NewDesignProseRegenerator(repo, catalog, genAI, slog.Default())
		presenter := NewFakeDesignMutationOutcomePresenter()

		// Use a design ID that doesn't exist
		nonExistentDesignId := uuid.New()
		regenerator.RegenerateDesignProse(ctx, presenter, nonExistentDesignId)

		// Should present error
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Empty(t, presenter.ConveySuccessWithResourceCalls, "Expected ConveySuccessWithResource not to be called")

		// AI should not be called if design doesn't exist
		assert.Empty(t, genAI.GenerateCalls, "Expected AI not to be called")
	})

	t.Run("should handle AI generation failure gracefully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		genAI.ShouldFail = true // Make AI fail
		regenerator := usecases.NewDesignProseRegenerator(repo, catalog, genAI, slog.Default())
		presenter := NewFakeDesignMutationOutcomePresenter()

		designId := createTestDesign(t, repo, catalog)

		regenerator.RegenerateDesignProse(ctx, presenter, designId)

		// Should present error when AI fails
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Empty(t, presenter.ConveySuccessWithResourceCalls, "Expected ConveySuccessWithResource not to be called")

		// AI should have been called (but failed)
		assert.Len(t, genAI.GenerateCalls, 1, "Expected AI to be called once")
	})

	t.Run("should handle design without existing prose", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		regenerator := usecases.NewDesignProseRegenerator(repo, catalog, genAI, slog.Default())
		presenter := NewFakeDesignMutationOutcomePresenter()

		// Create design without title and description
		designId := uuid.New()

		// Create some test products
		vanityId := uuid.New()
		vanityInfo := usecases.ProductInfo{
			Id:          vanityId,
			Category:    "vanities",
			Name:        "Test Vanity",
			Description: "A beautiful test vanity",
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		design := usecases.Design{
			ID:        designId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
			Created:   time.Now(),
			DesignOptions: usecases.DesignOptions{
				// No Title or Description set (NullString defaults to invalid)
				Vanity: &vanityId,
			},
		}
		_, err := repo.UpsertDesign(ctx, design)
		require.NoError(t, err)

		regenerator.RegenerateDesignProse(ctx, presenter, designId)

		// Should still succeed and generate new prose
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Len(t, presenter.ConveySuccessWithResourceCalls, 1, "Expected ConveySuccessWithResource to be called once")

		// Verify AI was called
		assert.Len(t, genAI.GenerateCalls, 1, "Expected AI to be called once")

		// Verify the design was updated with generated prose
		updatedDesign, err := repo.ReadDesign(ctx, designId)
		require.NoError(t, err)
		assert.True(t, updatedDesign.Title.Valid, "Expected design title to be generated")
		assert.True(t, updatedDesign.Description.Valid, "Expected design description to be generated")
		assert.Equal(t, "Fake Generated Title", updatedDesign.Title.String)
		assert.Equal(t, "Fake Generated Description", updatedDesign.Description.String)
	})

	t.Run("should handle design with partial prose", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		regenerator := usecases.NewDesignProseRegenerator(repo, catalog, genAI, slog.Default())
		presenter := NewFakeDesignMutationOutcomePresenter()

		// Create design with only title, no description
		designId := uuid.New()

		// Create some test products
		vanityId := uuid.New()
		vanityInfo := usecases.ProductInfo{
			Id:          vanityId,
			Category:    "vanities",
			Name:        "Test Vanity",
			Description: "A beautiful test vanity",
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		design := usecases.Design{
			ID:        designId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
			Created:   time.Now(),
			DesignOptions: usecases.DesignOptions{
				Title: sql.NullString{String: "Existing Title", Valid: true},
				// No Description set
				Vanity: &vanityId,
			},
		}
		_, err := repo.UpsertDesign(ctx, design)
		require.NoError(t, err)

		regenerator.RegenerateDesignProse(ctx, presenter, designId)

		// Should succeed and regenerate both title and description
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Len(t, presenter.ConveySuccessWithResourceCalls, 1, "Expected ConveySuccessWithResource to be called once")

		// Verify AI was called
		assert.Len(t, genAI.GenerateCalls, 1, "Expected AI to be called once")

		// Verify both title and description were updated
		updatedDesign, err := repo.ReadDesign(ctx, designId)
		require.NoError(t, err)
		assert.True(t, updatedDesign.Title.Valid, "Expected design title to be updated")
		assert.True(t, updatedDesign.Description.Valid, "Expected design description to be updated")
		assert.Equal(t, "Fake Generated Title", updatedDesign.Title.String)
		assert.Equal(t, "Fake Generated Description", updatedDesign.Description.String)
	})
}
