package usecases

import (
	"context"
)

type PresetRetriever struct {
	presetRepo presetRepositoryReplica
}

func NewPresetRetriever(presetRepo presetRepositoryReplica) *PresetRetriever {
	if IsNil(presetRepo) {
		panic("presetRepo cannot be nil")
	}
	return &PresetRetriever{presetRepo: presetRepo}
}

func (dr *PresetRetriever) FindPresetByLegacyId(ctx context.Context, presenter PresetPresenter, templateId string) {
	preset, err := dr.presetRepo.FindPresetByLegacyId(ctx, templateId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentPreset(ctx, preset)
}
