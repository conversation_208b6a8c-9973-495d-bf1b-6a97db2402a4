package usecases_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestCartInclusionReplacer_Constructor(t *testing.T) {
	t.Run("panics with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewCartInclusionReplacer(nil, nil)
		}, "Expected panic with nil repository")
	})

	t.Run("works with nil logger", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		assert.NotPanics(t, func() {
			usecases.NewCartInclusionReplacer(repo, nil)
		}, "Should not panic with nil logger")
	})
}

func TestCartInclusionReplacer_ReplaceCartInclusions(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	replacer := usecases.NewCartInclusionReplacer(repo, nil)
	presenter := NewFakeOutcomePresenter()

	// Create a test design first
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Create multiple cart inclusions
	productId1 := uuid.New()
	productId2 := uuid.New()
	key1 := usecases.CartInclusionKey{ProductID: productId1, Location: usecases.LocationFloor}
	key2 := usecases.CartInclusionKey{ProductID: productId2, Location: usecases.LocationWall}
	inclusions := usecases.CartInclusions{
		key1: {
			ProductID:    productId1,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 2,
		},
		key2: {
			ProductID:    productId2,
			Location:     usecases.LocationWall,
			Include:      false,
			QuantityDiff: -1,
		},
	}

	// Bulk replacement update
	replacer.ReplaceCartInclusions(ctx, presenter, designId, inclusions)

	// Verify success
	assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
	assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected success to be called once")

	// Verify all inclusions were saved
	savedInclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, savedInclusions, 2)

	// Verify inclusion 1
	inclusion1, exists := savedInclusions[key1]
	require.True(t, exists, "Expected inclusion for productId1")
	assert.Equal(t, productId1, inclusion1.ProductID)
	assert.Equal(t, usecases.LocationFloor, inclusion1.Location)
	assert.True(t, inclusion1.Include)
	assert.Equal(t, 2, inclusion1.QuantityDiff)

	// Verify inclusion 2
	inclusion2, exists := savedInclusions[key2]
	require.True(t, exists, "Expected inclusion for productId2")
	assert.Equal(t, productId2, inclusion2.ProductID)
	assert.Equal(t, usecases.LocationWall, inclusion2.Location)
	assert.False(t, inclusion2.Include)
	assert.Equal(t, -1, inclusion2.QuantityDiff)
}

func TestCartInclusionReplacer_ReplaceCartInclusions_ValidationError(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	replacer := usecases.NewCartInclusionReplacer(repo, nil)
	presenter := NewFakeOutcomePresenter()

	designId := uuid.New()

	// Create inclusion with invalid product ID
	invalidKey := usecases.CartInclusionKey{ProductID: uuid.Nil, Location: usecases.LocationFloor}
	inclusions := usecases.CartInclusions{
		invalidKey: {
			ProductID:    uuid.Nil, // Invalid
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 1,
		},
	}

	// Attempt bulk replacement update
	replacer.ReplaceCartInclusions(ctx, presenter, designId, inclusions)

	// Verify error was presented
	assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected validation error")
	assert.Equal(t, 0, presenter.ConveySuccessCalls, "Expected no success calls")
	assert.Equal(t, usecases.ErrInvalidPayload, presenter.PresentErrorCalls[0])
}

func TestCartInclusionReplacer_ReplaceCartInclusions_ReplacesExisting(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	replacer := usecases.NewCartInclusionReplacer(repo, nil)
	presenter := NewFakeOutcomePresenter()

	// Create a test design first
	design := usecases.NewEmptyDesign()
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productId1 := uuid.New()
	productId2 := uuid.New()

	// Save initial inclusion using ReplaceCartInclusions
	initialInclusion := usecases.CartInclusion{
		ProductID:    productId1,
		Location:     usecases.LocationFloor,
		Include:      true,
		QuantityDiff: 5,
	}
	key1 := usecases.CartInclusionKey{ProductID: productId1, Location: usecases.LocationFloor}
	initialInclusions := usecases.CartInclusions{
		key1: initialInclusion,
	}
	replacer.ReplaceCartInclusions(ctx, presenter, designId, initialInclusions)

	// Create new presenter for the bulk replacement update test
	presenter = NewFakeOutcomePresenter()

	// Replace with completely different inclusions
	key2 := usecases.CartInclusionKey{ProductID: productId2, Location: usecases.LocationWall}
	newInclusions := usecases.CartInclusions{
		key2: {
			ProductID:    productId2,
			Location:     usecases.LocationWall,
			Include:      false,
			QuantityDiff: -2,
		},
	}

	// Bulk replacement update (should replace, not add)
	replacer.ReplaceCartInclusions(ctx, presenter, designId, newInclusions)

	// Verify success
	assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
	assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected success to be called once")

	// Verify only new inclusion exists (old one should be gone)
	savedInclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, savedInclusions, 1, "Expected only 1 inclusion after replacement")

	// Verify the new inclusion
	inclusion, exists := savedInclusions[key2]
	require.True(t, exists, "Expected inclusion for productId2")
	assert.Equal(t, productId2, inclusion.ProductID)
	assert.Equal(t, usecases.LocationWall, inclusion.Location)
	assert.False(t, inclusion.Include)
	assert.Equal(t, -2, inclusion.QuantityDiff)

	// Verify old inclusion is gone
	// key1 is already declared above
	_, exists = savedInclusions[key1]
	assert.False(t, exists, "Expected old inclusion to be removed")
}
