package usecases

import (
	"context"
	"database/sql"
	"log/slog"

	"github.com/google/uuid"
)

type DesignProseRegenerator struct {
	designRepo designRepository
	catalog    catalog
	genAI      genAI
	logger     *slog.Logger
}

func NewDesignProseRegenerator(designRepo designRepository, catalog catalog, genAI genAI, logger *slog.Logger) *DesignProseRegenerator {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	if IsNil(catalog) {
		panic("catalog cannot be nil")
	}
	if IsNil(genAI) {
		panic("genAI cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignProseRegenerator{designRepo: designRepo, catalog: catalog, genAI: genAI, logger: logger}
}

func (dpr *DesignProseRegenerator) RegenerateDesignProse(ctx context.Context, presenter DesignMutationOutcomePresenter, designId uuid.UUID) {
	var zeroUUID uuid.UUID
	if designId == zeroUUID || designId == uuid.Nil {
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	design, err := dpr.designRepo.ReadDesign(ctx, designId)
	if err != nil {
		presenter.PresentError(ErrNotFound)
		return
	}

	// TODO: this should be abstracted into a function that can be shared with design creation/modification.
	dpr.logger.InfoContext(ctx, "Generating title & description for design...",
		slog.String("projectId", design.ProjectID.String()), slog.String("designId", design.ID.String()))
	productDescriptions, err := dpr.catalog.ProductDescriptionsForDesign(ctx, design)
	if err != nil {
		dpr.logger.ErrorContext(ctx, "Failed to build product descriptions for design",
			slog.String("designId", design.ID.String()), slog.String("projectId", design.ProjectID.String()),
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}
	title, description, err := dpr.genAI.GenerateDesignTitleAndDescription(ctx, productDescriptions)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	design.Title = sql.NullString{String: title, Valid: true}
	design.Description = sql.NullString{String: description, Valid: true}

	if _, err := dpr.designRepo.UpsertDesign(ctx, design); err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.ConveySuccessWithResource(design, Updated)
}
