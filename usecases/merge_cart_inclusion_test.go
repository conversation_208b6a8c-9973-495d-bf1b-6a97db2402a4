package usecases_test

import (
	"context"
	"log/slog"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestCartInclusionMerger_MergeCartInclusions(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	logger := slog.Default()
	merger := usecases.NewCartInclusionMerger(repo, logger)

	// Create a test design
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Set up existing cart inclusions
	productId1 := uuid.New()
	productId2 := uuid.New()
	productId3 := uuid.New()

	key1 := usecases.CartInclusionKey{ProductID: productId1, Location: usecases.LocationFloor}
	key2 := usecases.CartInclusionKey{ProductID: productId2, Location: usecases.LocationWall}
	key3 := usecases.CartInclusionKey{ProductID: productId3, Location: usecases.LocationShowerFloor}

	existingInclusions := usecases.CartInclusions{
		key1: {
			ProductID:    productId1,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 1,
		},
		key2: {
			ProductID:    productId2,
			Location:     usecases.LocationWall,
			Include:      false,
			QuantityDiff: -2,
		},
	}

	// Save existing inclusions
	err = repo.UpsertCartInclusionsForDesign(ctx, designId, existingInclusions)
	require.NoError(t, err)

	// Create new inclusions to merge (modify productId1, add productId3)
	newInclusions := usecases.CartInclusions{
		key1: {
			ProductID:    productId1,
			Location:     usecases.LocationFloor,
			Include:      false, // Changed from true
			QuantityDiff: 5,     // Changed from 1
		},
		key3: {
			ProductID:    productId3,
			Location:     usecases.LocationShowerFloor,
			Include:      true,
			QuantityDiff: 3,
		},
	}

	// Test successful merge
	presenter := NewFakeOutcomePresenter()
	merger.MergeCartInclusions(ctx, presenter, designId, newInclusions)

	// Verify success
	assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected ConveySuccess to be called")
	assert.Empty(t, presenter.PresentErrorCalls, "Expected no error")

	// Verify merged results
	finalInclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, finalInclusions, 3, "Expected 3 inclusions after merge")

	// Verify productId1 was updated
	inclusion1, exists := finalInclusions[key1]
	require.True(t, exists, "Expected productId1 to exist")
	assert.Equal(t, productId1, inclusion1.ProductID)
	assert.Equal(t, usecases.LocationFloor, inclusion1.Location)
	assert.False(t, inclusion1.Include, "Expected productId1 Include to be updated to false")
	assert.Equal(t, 5, inclusion1.QuantityDiff, "Expected productId1 QuantityDiff to be updated to 5")

	// Verify productId2 was preserved (not in new inclusions)
	inclusion2, exists := finalInclusions[key2]
	require.True(t, exists, "Expected productId2 to be preserved")
	assert.Equal(t, productId2, inclusion2.ProductID)
	assert.Equal(t, usecases.LocationWall, inclusion2.Location)
	assert.False(t, inclusion2.Include, "Expected productId2 Include to be preserved")
	assert.Equal(t, -2, inclusion2.QuantityDiff, "Expected productId2 QuantityDiff to be preserved")

	// Verify productId3 was added
	inclusion3, exists := finalInclusions[key3]
	require.True(t, exists, "Expected productId3 to be added")
	assert.Equal(t, productId3, inclusion3.ProductID)
	assert.Equal(t, usecases.LocationShowerFloor, inclusion3.Location)
	assert.True(t, inclusion3.Include, "Expected productId3 Include to be true")
	assert.Equal(t, 3, inclusion3.QuantityDiff, "Expected productId3 QuantityDiff to be 3")
}

func TestCartInclusionMerger_MergeCartInclusions_EmptyExisting(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	logger := slog.Default()
	merger := usecases.NewCartInclusionMerger(repo, logger)

	// Create a test design with no existing inclusions
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Create new inclusions to merge
	productId1 := uuid.New()
	key1 := usecases.CartInclusionKey{ProductID: productId1, Location: usecases.LocationFloor}
	newInclusions := usecases.CartInclusions{
		key1: {
			ProductID:    productId1,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 2,
		},
	}

	// Test merge with empty existing inclusions
	presenter := NewFakeOutcomePresenter()
	merger.MergeCartInclusions(ctx, presenter, designId, newInclusions)

	// Verify success
	assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected ConveySuccess to be called")
	assert.Empty(t, presenter.PresentErrorCalls, "Expected no error")

	// Verify results
	finalInclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, finalInclusions, 1, "Expected 1 inclusion after merge")

	inclusion1, exists := finalInclusions[key1]
	require.True(t, exists, "Expected productId1 to exist")
	assert.Equal(t, productId1, inclusion1.ProductID)
	assert.Equal(t, usecases.LocationFloor, inclusion1.Location)
	assert.True(t, inclusion1.Include)
	assert.Equal(t, 2, inclusion1.QuantityDiff)
}

func TestCartInclusionMerger_MergeCartInclusions_InvalidInclusion(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	logger := slog.Default()
	merger := usecases.NewCartInclusionMerger(repo, logger)

	// Create a test design
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Create invalid inclusion (invalid product ID)
	productId1 := uuid.Nil // Invalid: nil UUID
	invalidKey := usecases.CartInclusionKey{ProductID: productId1, Location: usecases.LocationFloor}
	invalidInclusions := usecases.CartInclusions{
		invalidKey: {
			ProductID:    productId1,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 2,
		},
	}

	// Test merge with invalid inclusion
	presenter := NewFakeOutcomePresenter()
	merger.MergeCartInclusions(ctx, presenter, designId, invalidInclusions)

	// Verify error
	assert.Equal(t, 0, presenter.ConveySuccessCalls, "Expected ConveySuccess not to be called")
	assert.Len(t, presenter.PresentErrorCalls, 1, "Expected error to be presented")
	assert.Equal(t, usecases.ErrInvalidPayload, presenter.PresentErrorCalls[0], "Expected ErrInvalidPayload")
}

func TestCartInclusionMerger_MergeCartInclusions_EmptyNewInclusions(t *testing.T) {
	ctx := context.Background()
	repo := gateways.NewFakeRelDb()
	logger := slog.Default()
	merger := usecases.NewCartInclusionMerger(repo, logger)

	// Create a test design with existing inclusions
	projectId := entities.NewProjectId("TEST-PROJECT")
	design := usecases.NewDesign(nil, &projectId)
	designId, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productId1 := uuid.New()
	key1 := usecases.CartInclusionKey{ProductID: productId1, Location: usecases.LocationFloor}
	existingInclusions := usecases.CartInclusions{
		key1: {
			ProductID:    productId1,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 1,
		},
	}

	err = repo.UpsertCartInclusionsForDesign(ctx, designId, existingInclusions)
	require.NoError(t, err)

	// Test merge with empty new inclusions
	emptyInclusions := usecases.CartInclusions{}
	presenter := NewFakeOutcomePresenter()
	merger.MergeCartInclusions(ctx, presenter, designId, emptyInclusions)

	// Verify success (no-op)
	assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected ConveySuccess to be called")
	assert.Empty(t, presenter.PresentErrorCalls, "Expected no error")

	// Verify existing inclusions are preserved
	finalInclusions, err := repo.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, finalInclusions, 1, "Expected existing inclusion to be preserved")

	inclusion1, exists := finalInclusions[key1]
	require.True(t, exists, "Expected productId1 to exist")
	assert.Equal(t, productId1, inclusion1.ProductID)
	assert.Equal(t, usecases.LocationFloor, inclusion1.Location)
	assert.True(t, inclusion1.Include)
	assert.Equal(t, 1, inclusion1.QuantityDiff)
}
