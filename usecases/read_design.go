package usecases

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type designRepoReplicaWithLegacyIdMapping interface {
	designRepositoryReplica
	LegacyIdMapping(ctx context.Context) (map[uuid.UUID]string, error)
}
type DesignRetriever struct {
	designRepo designRepoReplicaWithLegacyIdMapping
}

func NewDesignRetriever(designRepo designRepoReplicaWithLegacyIdMapping) *DesignRetriever {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	return &DesignRetriever{designRepo: designRepo}
}

func (dr *DesignRetriever) RetrieveDesign(ctx context.Context, presenter DesignPresenter, designId uuid.UUID) {
	design, err := dr.designRepo.ReadDesign(ctx, designId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentDesign(ctx, design)
}

func (dr *DesignRetriever) RetrieveAllDesignsForProject(ctx context.Context,
	presenter DesignsPresenter, projectId entities.ProjectId, useLegacyIds bool) {
	designs, err := dr.designRepo.DesignsForProject(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	if useLegacyIds {
		legacyIdMapping, err := dr.designRepo.LegacyIdMapping(ctx)
		if err != nil {
			presenter.PresentError(err)
			return
		}
		presenter.SetLegacyIdMapping(legacyIdMapping)
	}
	presenter.PresentDesigns(ctx, designs)
}

func (dr *DesignRetriever) RetrieveDesignsForMultipleProjects(ctx context.Context,
	presenter DesignsPresenter, projectIds []entities.ProjectId) {
	designsByProject, errors, err := dr.designRepo.DesignsByProject(ctx, projectIds)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentDesignsByProject(ctx, designsByProject, errors)
}
