package usecases

import (
	"context"
	"log"

	"github.com/google/uuid"
)

type DesignDeleter struct {
	designRepo designRepository
}

func NewDesignDeleter(designRepo designRepository) *DesignDeleter {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	return &DesignDeleter{designRepo: designRepo}
}

func (du *DesignDeleter) DeleteDesign(ctx context.Context, presenter OutcomePresenter, designId uuid.UUID) {
	var zeroUUID uuid.UUID
	if designId == zeroUUID || designId == uuid.Nil {
		log.Println("Received request to delete design with unspecified ID.")
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	if err := du.designRepo.DeleteDesign(ctx, designId); err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.ConveySuccess()
}
