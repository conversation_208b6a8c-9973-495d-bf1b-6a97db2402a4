package usecases_test

import (
	"context"
	"log/slog"
	"net/url"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestNewRenditionUpdater(t *testing.T) {
	t.Run("should create updater with valid dependencies", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		logger := slog.Default()
		updater := usecases.NewRenditionUpdater(repo, genAI, repo, catalog, logger)
		assert.NotNil(t, updater)
	})

	t.Run("should panic with nil rendition repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		assert.Panics(t, func() {
			usecases.NewRenditionUpdater(nil, genAI, repo, catalog, slog.Default())
		})
	})

	t.Run("should panic with nil genAI", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		assert.Panics(t, func() {
			usecases.NewRenditionUpdater(repo, nil, repo, catalog, slog.Default())
		})
	})

	t.Run("should panic with nil design repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		assert.Panics(t, func() {
			usecases.NewRenditionUpdater(repo, genAI, nil, catalog, slog.Default())
		})
	})

	t.Run("should create updater with nil logger", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		updater := usecases.NewRenditionUpdater(repo, genAI, repo, catalog, nil)
		assert.NotNil(t, updater)
	})
}

func TestRenditionUpdater_UpdateRendition(t *testing.T) {
	ctx := context.Background()

	createTestDesign := func(t *testing.T, repo *gateways.FakeRelDb, catalog *gateways.FakeCatalog) uuid.UUID {
		t.Helper()
		designId := uuid.New()

		// Create some test products
		vanityId := uuid.New()
		vanityInfo := usecases.ProductInfo{
			Id:          vanityId,
			Category:    "vanities",
			Name:        "Test Vanity",
			Description: "A beautiful test vanity",
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		toiletId := uuid.New()
		toiletInfo := usecases.ProductInfo{
			Id:          toiletId,
			Category:    "toilets",
			Name:        "Test Toilet",
			Description: "A comfortable test toilet",
		}
		catalog.AddProduct("toilets", toiletId, toiletInfo)

		design := usecases.Design{
			ID:        designId,
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
			DesignOptions: usecases.DesignOptions{
				Vanity: &vanityId,
				FixedProductSelections: usecases.FixedProductSelections{
					Toilet: &toiletId,
				},
			},
		}
		_, err := repo.UpsertDesign(ctx, design)
		require.NoError(t, err)
		return designId
	}

	createTestRendition := func(t *testing.T, repo *gateways.FakeRelDb, designId uuid.UUID) uuid.UUID {
		t.Helper()
		testURL, _ := url.Parse("https://example.com/test-rendition.jpg")
		rendition := entities.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    entities.RenditionPending,
			URL:       testURL,
		}
		id, err := repo.InsertRendition(ctx, designId, rendition)
		require.NoError(t, err)
		return id
	}

	t.Run("should update rendition status successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		updater := usecases.NewRenditionUpdater(repo, genAI, repo, catalog, slog.Default())
		presenter := NewFakeOutcomePresenter()

		designId := createTestDesign(t, repo, catalog)
		renditionId := createTestRendition(t, repo, designId)

		// Update rendition to Started status
		diff := entities.RenditionDiff{
			Id:     renditionId,
			Status: entities.RenditionStarted,
		}

		updater.UpdateRendition(ctx, presenter, diff)

		// Verify success
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected ConveySuccess to be called once")

		// Verify the rendition was updated in the repository
		renditions, err := repo.Renditions(ctx, []uuid.UUID{renditionId})
		require.NoError(t, err)
		assert.Len(t, renditions, 1, "Expected one rendition")
		assert.Equal(t, entities.RenditionStarted, renditions[0].Status)

		// AI should not have been called for non-completed status
		assert.Empty(t, genAI.GenerateCalls, "Expected AI not to be called for non-completed status")
	})

	t.Run("should update rendition to completed and regenerate prose", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		updater := usecases.NewRenditionUpdater(repo, genAI, repo, catalog, slog.Default())
		presenter := NewFakeOutcomePresenter()

		designId := createTestDesign(t, repo, catalog)
		renditionId := createTestRendition(t, repo, designId)

		// Update rendition to Completed status with URL
		completedURL, _ := url.Parse("https://example.com/completed-rendition.jpg")
		diff := entities.RenditionDiff{
			Id:     renditionId,
			Status: entities.RenditionCompleted,
			URL:    completedURL,
		}

		updater.UpdateRendition(ctx, presenter, diff)

		// Verify success
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected ConveySuccess to be called once")

		// Verify the rendition was updated
		renditions, err := repo.Renditions(ctx, []uuid.UUID{renditionId})
		require.NoError(t, err)
		assert.Len(t, renditions, 1, "Expected one rendition")
		assert.Equal(t, entities.RenditionCompleted, renditions[0].Status)
		assert.Equal(t, completedURL, renditions[0].URL)

		// AI should have been called to regenerate prose
		assert.Len(t, genAI.GenerateCalls, 1, "Expected AI to be called once for prose generation")

		// Verify the design was updated with generated prose
		design, err := repo.ReadDesign(ctx, designId)
		require.NoError(t, err)
		assert.True(t, design.Title.Valid, "Expected design title to be updated")
		assert.True(t, design.Description.Valid, "Expected design description to be updated")
		assert.Equal(t, "Fake Generated Title", design.Title.String)
		assert.Equal(t, "Fake Generated Description", design.Description.String)
	})

	t.Run("should present error for zero UUID", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		updater := usecases.NewRenditionUpdater(repo, genAI, repo, catalog, slog.Default())
		presenter := NewFakeOutcomePresenter()

		var zeroUUID uuid.UUID
		diff := entities.RenditionDiff{
			Id:     zeroUUID,
			Status: entities.RenditionStarted,
		}

		updater.UpdateRendition(ctx, presenter, diff)

		// Should present error
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Contains(t, presenter.PresentErrorCalls, usecases.ErrInvalidPayload)
		assert.Equal(t, 0, presenter.ConveySuccessCalls, "Expected ConveySuccess not to be called")
	})

	t.Run("should present error for uuid.Nil", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		updater := usecases.NewRenditionUpdater(repo, genAI, repo, catalog, slog.Default())
		presenter := NewFakeOutcomePresenter()

		diff := entities.RenditionDiff{
			Id:     uuid.Nil,
			Status: entities.RenditionStarted,
		}

		updater.UpdateRendition(ctx, presenter, diff)

		// Should present error
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Contains(t, presenter.PresentErrorCalls, usecases.ErrInvalidPayload)
		assert.Equal(t, 0, presenter.ConveySuccessCalls, "Expected ConveySuccess not to be called")
	})

	t.Run("should present error when completed status has no URL", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		updater := usecases.NewRenditionUpdater(repo, genAI, repo, catalog, slog.Default())
		presenter := NewFakeOutcomePresenter()

		designId := createTestDesign(t, repo, catalog)
		renditionId := createTestRendition(t, repo, designId)

		// Try to update to Completed status without URL
		diff := entities.RenditionDiff{
			Id:     renditionId,
			Status: entities.RenditionCompleted,
			URL:    nil, // No URL provided
		}

		updater.UpdateRendition(ctx, presenter, diff)

		// Should present error
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Contains(t, presenter.PresentErrorCalls, usecases.ErrInvalidPayload)
		assert.Equal(t, 0, presenter.ConveySuccessCalls, "Expected ConveySuccess not to be called")
	})

	t.Run("should handle AI generation failure gracefully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		genAI.ShouldFail = true // Make AI fail
		updater := usecases.NewRenditionUpdater(repo, genAI, repo, catalog, slog.Default())
		presenter := NewFakeOutcomePresenter()

		designId := createTestDesign(t, repo, catalog)
		renditionId := createTestRendition(t, repo, designId)

		// Update rendition to Completed status
		completedURL, _ := url.Parse("https://example.com/completed-rendition.jpg")
		diff := entities.RenditionDiff{
			Id:     renditionId,
			Status: entities.RenditionCompleted,
			URL:    completedURL,
		}

		updater.UpdateRendition(ctx, presenter, diff)

		// Should still succeed even if AI fails (AI failure is logged but not fatal)
		assert.Empty(t, presenter.PresentErrorCalls, "Expected no errors")
		assert.Equal(t, 1, presenter.ConveySuccessCalls, "Expected ConveySuccess to be called once")

		// Verify the rendition was still updated
		renditions, err := repo.Renditions(ctx, []uuid.UUID{renditionId})
		require.NoError(t, err)
		assert.Len(t, renditions, 1, "Expected one rendition")
		assert.Equal(t, entities.RenditionCompleted, renditions[0].Status)
	})

	t.Run("should present error when repository update fails", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		catalog := gateways.NewFakeCatalog()
		genAI := NewFakeGenAI()
		updater := usecases.NewRenditionUpdater(repo, genAI, repo, catalog, slog.Default())
		presenter := NewFakeOutcomePresenter()

		// Use a rendition ID that doesn't exist
		nonExistentId := uuid.New()
		diff := entities.RenditionDiff{
			Id:     nonExistentId,
			Status: entities.RenditionStarted,
		}

		updater.UpdateRendition(ctx, presenter, diff)

		// Should present error
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Equal(t, 0, presenter.ConveySuccessCalls, "Expected ConveySuccess not to be called")
	})
}
