package usecases

import (
	"context"
	"log/slog"

	"github.com/google/uuid"
)

// CartInclusionDeleter handles cart inclusion deletion operations.
type CartInclusionDeleter struct {
	cartInclusionRepo cartInclusionRepository
	logger            *slog.Logger
}

// NewCartInclusionDeleter creates a new CartInclusionDeleter
func NewCartInclusionDeleter(cartInclusionRepo cartInclusionRepository, logger *slog.Logger) *CartInclusionDeleter {
	if IsNil(cartInclusionRepo) {
		panic("cartInclusionRepo cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &CartInclusionDeleter{cartInclusionRepo: cartInclusionRepo, logger: logger}
}

// DeleteCartInclusion removes a specific cart inclusion
func (d *CartInclusionDeleter) DeleteCartInclusion(ctx context.Context, presenter OutcomePresenter, designId uuid.UUID, productId uuid.UUID, location Location) {
	d.logger.DebugContext(ctx, "Starting cart inclusion delete operation",
		slog.String("operation", "delete_cart_inclusion"),
		slog.String("designId", designId.String()),
		slog.String("productId", productId.String()),
		slog.String("location", string(location)))

	if err := d.cartInclusionRepo.DeleteCartInclusion(ctx, designId, productId, location); err != nil {
		d.logger.ErrorContext(ctx, "Failed to delete cart inclusion",
			slog.String("operation", "delete_cart_inclusion"),
			slog.String("designId", designId.String()),
			slog.String("productId", productId.String()),
			slog.String("location", string(location)),
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}

	d.logger.DebugContext(ctx, "Successfully deleted cart inclusion",
		slog.String("operation", "delete_cart_inclusion"),
		slog.String("designId", designId.String()),
		slog.String("productId", productId.String()),
		slog.String("location", string(location)),
		slog.String("status", "success"))
	presenter.ConveySuccess()
}

// ClearCartInclusions removes all cart inclusions for a design
func (d *CartInclusionDeleter) ClearCartInclusions(ctx context.Context, presenter OutcomePresenter, designId uuid.UUID) {
	d.logger.DebugContext(ctx, "Starting clear cart inclusions operation",
		slog.String("operation", "clear_cart_inclusions"),
		slog.String("designId", designId.String()))

	if err := d.cartInclusionRepo.DeleteCartInclusionsForDesign(ctx, designId); err != nil {
		d.logger.ErrorContext(ctx, "Failed to clear cart inclusions",
			slog.String("operation", "clear_cart_inclusions"),
			slog.String("designId", designId.String()),
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}

	d.logger.DebugContext(ctx, "Successfully cleared cart inclusions",
		slog.String("operation", "clear_cart_inclusions"),
		slog.String("designId", designId.String()),
		slog.String("status", "success"))
	presenter.ConveySuccess()
}
