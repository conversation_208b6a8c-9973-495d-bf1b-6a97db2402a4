package usecases_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestNewDesignEvolver(t *testing.T) {
	t.Run("should create evolver with valid dependencies", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)
		assert.NotNil(t, evolver)
	})

	t.Run("should panic with nil catalog", func(t *testing.T) {
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		assert.Panics(t, func() {
			usecases.NewDesignEvolver(nil, productSearch, repo, nil)
		})
	})

	t.Run("should panic with nil productSearch", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		repo := gateways.NewFakeRelDb()
		assert.Panics(t, func() {
			usecases.NewDesignEvolver(catalog, nil, repo, nil)
		})
	})

	t.Run("should panic with nil designRepo", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		assert.Panics(t, func() {
			usecases.NewDesignEvolver(catalog, productSearch, nil, nil)
		})
	})
}

func TestDesignEvolver_EvolveDesignForLayout(t *testing.T) {
	ctx := context.Background()

	// Helper function to create a basic design
	createBasicDesign := func() usecases.Design {
		floorTileId := uuid.New()
		return usecases.Design{
			ID:        uuid.New(),
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
			DesignOptions: usecases.DesignOptions{
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: &floorTileId,
				},
			},
		}
	}

	// Helper function to create a basic layout
	createBasicLayout := func() entities.RoomLayout {
		return entities.RoomLayout{
			Id: uuid.New(),
		}
	}

	t.Run("should handle layout with no toilets", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createBasicDesign()
		toiletId := uuid.New()
		shelvingId := uuid.New()
		design.Toilet = &toiletId
		design.Shelving = &shelvingId

		layout := createBasicLayout()
		layout.ToiletIds = []uuid.UUID{} // No toilets

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.Nil(t, result.Toilet, "Toilet should be nil when no toilets in layout")
		assert.Nil(t, result.Shelving, "Shelving should be nil when no toilets in layout")
	})

	t.Run("should add toilet when layout has toilets and design has none", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		toiletId := uuid.New()
		shelvingId := uuid.New()
		productSearch.AddResults("category=Toilet", []uuid.UUID{toiletId})
		productSearch.AddResults("category=Shelves", []uuid.UUID{shelvingId})

		design := createBasicDesign()
		design.Toilet = nil
		design.Shelving = nil

		layout := createBasicLayout()
		layout.ToiletIds = []uuid.UUID{uuid.New()} // Has toilets

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.NotNil(t, result.Toilet, "Toilet should be added when layout has toilets")
		assert.Equal(t, toiletId, *result.Toilet)
		assert.NotNil(t, result.Shelving, "Shelving should be added when layout has toilets")
		assert.Equal(t, shelvingId, *result.Shelving)
	})

	t.Run("should return error when toilet search fails", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		searchError := errors.New("toilet search failed")
		productSearch.AddError("category=Toilet", searchError)

		design := createBasicDesign()
		design.Toilet = nil

		layout := createBasicLayout()
		layout.ToiletIds = []uuid.UUID{uuid.New()} // Has toilets

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Equal(t, searchError, err)
	})

	t.Run("should return error when no toilets found via search", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		productSearch.AddResults("category=Toilet", []uuid.UUID{}) // No results

		design := createBasicDesign()
		design.Toilet = nil

		layout := createBasicLayout()
		layout.ToiletIds = []uuid.UUID{uuid.New()} // Has toilets

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "no toilets found via product search")
	})

	t.Run("should add shelving when layout has toilets and design has none", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		toiletId := uuid.New()
		shelvingId := uuid.New()
		productSearch.AddResults("category=Toilet", []uuid.UUID{toiletId})
		productSearch.AddResults("category=Shelves", []uuid.UUID{shelvingId})

		design := createBasicDesign()
		design.Toilet = nil
		design.Shelving = nil

		layout := createBasicLayout()
		layout.ToiletIds = []uuid.UUID{uuid.New()} // Has toilets

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.NotNil(t, result.Shelving, "Shelving should be added when layout has toilets")
		assert.Equal(t, shelvingId, *result.Shelving)
	})

	t.Run("should return error when shelving search fails", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		toiletId := uuid.New()
		productSearch.AddResults("category=Toilet", []uuid.UUID{toiletId})

		searchError := errors.New("shelving search failed")
		productSearch.AddError("category=Shelves", searchError)

		design := createBasicDesign()
		design.Toilet = nil
		design.Shelving = nil

		layout := createBasicLayout()
		layout.ToiletIds = []uuid.UUID{uuid.New()} // Has toilets

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Equal(t, searchError, err)
	})

	t.Run("should return error when no shelving found via search", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		toiletId := uuid.New()
		productSearch.AddResults("category=Toilet", []uuid.UUID{toiletId})
		productSearch.AddResults("category=Shelves", []uuid.UUID{}) // No results

		design := createBasicDesign()
		design.Toilet = nil
		design.Shelving = nil

		layout := createBasicLayout()
		layout.ToiletIds = []uuid.UUID{uuid.New()} // Has toilets

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "no shelving found via product search")
	})

	t.Run("should handle layout with no vanities", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createBasicDesign()
		vanityId := uuid.New()
		faucetId := uuid.New()
		mirrorId := uuid.New()
		lightingId := uuid.New()
		design.Vanity = &vanityId
		design.Faucet = &faucetId
		design.Mirror = &mirrorId
		design.Lighting = &lightingId

		layout := createBasicLayout()
		layout.Vanities = []entities.Vanity{} // No vanities

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.Nil(t, result.Vanity, "Vanity should be nil when no vanities in layout")
		assert.Nil(t, result.Faucet, "Faucet should be nil when no vanities in layout")
		assert.Nil(t, result.Mirror, "Mirror should be nil when no vanities in layout")
		assert.Nil(t, result.Lighting, "Lighting should be nil when no vanities in layout")
	})

	t.Run("should handle layout with vanity but no max length", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createBasicDesign()
		vanityId := uuid.New()
		design.Vanity = &vanityId

		layout := createBasicLayout()
		layout.Vanities = []entities.Vanity{{
			MaxLength: nil, // No max length
		}}

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.Nil(t, result.Vanity, "Vanity should be nil when no max length")
		assert.Nil(t, result.Faucet, "Faucet should be nil when no max length")
		assert.Nil(t, result.Mirror, "Mirror should be nil when no max length")
		assert.Nil(t, result.Lighting, "Lighting should be nil when no max length")
	})

	t.Run("should handle layout with vanity but zero max length", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createBasicDesign()
		vanityId := uuid.New()
		design.Vanity = &vanityId

		layout := createBasicLayout()
		maxLength := 0.0
		layout.Vanities = []entities.Vanity{{
			MaxLength: &maxLength, // Zero max length
		}}

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.Nil(t, result.Vanity, "Vanity should be nil when max length is zero")
		assert.Nil(t, result.Faucet, "Faucet should be nil when max length is zero")
		assert.Nil(t, result.Mirror, "Mirror should be nil when max length is zero")
		assert.Nil(t, result.Lighting, "Lighting should be nil when max length is zero")
	})

	t.Run("should handle layout with no wet areas", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createBasicDesign()
		showerFloorTileId := uuid.New()
		showerGlassId := uuid.New()
		design.ShowerFloorTile = &showerFloorTileId
		design.ShowerGlass = &showerGlassId

		layout := createBasicLayout()
		layout.WetAreas = []entities.WetArea{} // No wet areas

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.Nil(t, result.ShowerFloorTile, "ShowerFloorTile should be nil when no wet areas")
		assert.Nil(t, result.ShowerGlass, "ShowerGlass should be nil when no wet areas")
	})

	t.Run("should handle wet area with no shower IDs", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createBasicDesign()
		showerFloorTileId := uuid.New()
		showerGlassId := uuid.New()
		design.ShowerFloorTile = &showerFloorTileId
		design.ShowerGlass = &showerGlassId

		layout := createBasicLayout()
		layout.WetAreas = []entities.WetArea{{
			ShowerIds: []uuid.UUID{}, // No shower IDs
		}}

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.Nil(t, result.ShowerFloorTile, "ShowerFloorTile should be nil when no shower IDs")
		assert.Nil(t, result.ShowerGlass, "ShowerGlass should be nil when no shower IDs")
	})
}

func TestDesignEvolver_EvolveVanityProducts(t *testing.T) {
	ctx := context.Background()
	white := usecases.White

	// Helper function to create a design with vanity
	createDesignWithVanity := func() usecases.Design {
		floorTileId := uuid.New()
		vanityId := uuid.New()
		return usecases.Design{
			ID:        uuid.New(),
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
			DesignOptions: usecases.DesignOptions{
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: &floorTileId,
				},
				Vanity: &vanityId,
			},
		}
	}

	// Helper function to create a layout with vanity
	createLayoutWithVanity := func(maxLength float64) entities.RoomLayout {
		return entities.RoomLayout{
			Id: uuid.New(),
			Vanities: []entities.Vanity{{
				MaxLength: &maxLength,
			}},
		}
	}

	t.Run("should add vanity when design has none", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		vanityId := uuid.New()
		productSearch.AddResults("category=Vanities&vanity_length_lte=48", []uuid.UUID{vanityId})

		// Add the vanity to catalog so subsequent lookups work
		pi := usecases.ProductInfo{
			Id:       vanityId,
			Category: usecases.CategoryVanity,
			Length:   36.0,
		}
		vanityInfo := usecases.Vanity{
			ProductInfo:       pi,
			FaucetHoleSpacing: usecases.SingleHole,
			NumberOfSinks:     1,
			SinkOffset:        0.0,
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		// Add faucet search results
		faucetId := uuid.New()
		productSearch.AddResults("category=Faucets&faucet_hole_spacing_compatibility=Single Hole", []uuid.UUID{faucetId})

		// Add mirror and lighting search results
		mirrorId := uuid.New()
		productSearch.AddResults("category=Mirror&mirror_width_lte=36", []uuid.UUID{mirrorId})

		lightingId := uuid.New()
		productSearch.AddResults("category=Decorative%20Lighting&lighting_length_lte=36", []uuid.UUID{lightingId})

		design := createDesignWithVanity()
		design.Vanity = nil

		layout := createLayoutWithVanity(48.0)

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.NotNil(t, result.Vanity)
		assert.Equal(t, vanityId, *result.Vanity)
	})

	t.Run("should return error when vanity search fails", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		searchError := errors.New("vanity search failed")
		productSearch.AddError("category=Vanities&vanity_length_lte=48", searchError)

		design := createDesignWithVanity()
		design.Vanity = nil

		layout := createLayoutWithVanity(48.0)

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Equal(t, searchError, err)
	})

	t.Run("should return error when current vanity info fails", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		vanityId := uuid.New()
		catalogError := errors.New("catalog lookup failed")
		catalog.AddError("vanities", vanityId, catalogError)

		design := createDesignWithVanity()
		design.Vanity = &vanityId

		layout := createLayoutWithVanity(48.0)

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Equal(t, catalogError, err)
	})

	t.Run("should return error when vanity has no length", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		vanityId := uuid.New()
		vanityInfo := usecases.Vanity{
			ProductInfo: usecases.ProductInfo{
				Category: usecases.CategoryVanity,
				Id:       vanityId,
				Length:   0, // No length
			},
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		design := createDesignWithVanity()
		design.Vanity = &vanityId

		layout := createLayoutWithVanity(48.0)

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "no length found for vanity")
	})

	t.Run("should replace vanity when current is too long", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		oldVanityId := uuid.New()
		newVanityId := uuid.New()

		// Current vanity is 60 inches long
		oldVanityInfo := usecases.Vanity{
			ProductInfo: usecases.ProductInfo{
				Id:                oldVanityId,
				Category:          usecases.CategoryVanity,
				Length:            60.0,
				ProductFamilyName: "TestFamily",
				Color:             &white,
			},
			FaucetHoleSpacing: usecases.SingleHole,
			NumberOfSinks:     1,
			SinkOffset:        0.0,
		}
		catalog.AddProduct("vanities", oldVanityId, oldVanityInfo)

		// New vanity search returns a shorter one
		productSearch.AddResults("category=Vanities&vanity_length_lte=48&collection=TestFamily&colors=White", []uuid.UUID{newVanityId})

		// Add the new vanity to catalog so subsequent lookups work
		newVanityInfo := usecases.Vanity{
			ProductInfo: usecases.ProductInfo{
				Id:                newVanityId,
				Category:          usecases.CategoryVanity,
				Length:            36.0,
				ProductFamilyName: "TestFamily",
				Color:             &white,
			},
			FaucetHoleSpacing: usecases.SingleHole,
			NumberOfSinks:     1,
			SinkOffset:        0.0,
		}
		catalog.AddProduct("vanities", newVanityId, newVanityInfo)

		// Add faucet search results
		faucetId := uuid.New()
		productSearch.AddResults("category=Faucets&faucet_hole_spacing_compatibility=Single Hole", []uuid.UUID{faucetId})

		mirrorId := uuid.New()
		productSearch.AddResults("category=Mirror&mirror_width_lte=36", []uuid.UUID{mirrorId})

		lightingId := uuid.New()
		productSearch.AddResults("category=Decorative%20Lighting&lighting_length_lte=36", []uuid.UUID{lightingId})

		design := createDesignWithVanity()
		design.Vanity = &oldVanityId

		layout := createLayoutWithVanity(48.0) // Max length is 48

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.NotNil(t, result.Vanity)
		assert.Equal(t, newVanityId, *result.Vanity)
	})

	t.Run("should keep vanity when current fits", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		vanityId := uuid.New()

		// Current vanity is 36 inches long (fits in 48 inch space)
		vanityInfo := usecases.Vanity{
			ProductInfo: usecases.ProductInfo{
				Id:                vanityId,
				Category:          usecases.CategoryVanity,
				Length:            36.0,
				ProductFamilyName: "TestFamily",
				Color:             &white,
			},
			FaucetHoleSpacing: usecases.SingleHole,
			NumberOfSinks:     1,
			SinkOffset:        0.0,
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		// Add faucet search results
		faucetId := uuid.New()
		productSearch.AddResults("category=Faucets&faucet_hole_spacing_compatibility=Single Hole", []uuid.UUID{faucetId})

		// Add mirror search results
		mirrorId := uuid.New()
		productSearch.AddResults("category=Mirror&mirror_width_lte=36", []uuid.UUID{mirrorId})

		// Add lighting search results
		lightingId := uuid.New()
		productSearch.AddResults("category=Decorative%20Lighting&lighting_length_lte=36", []uuid.UUID{lightingId})

		design := createDesignWithVanity()
		design.Vanity = &vanityId

		layout := createLayoutWithVanity(48.0) // Max length is 48

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.NotNil(t, result.Vanity)
		assert.Equal(t, vanityId, *result.Vanity) // Should keep the same vanity
		assert.NotNil(t, result.Faucet)
		assert.Equal(t, faucetId, *result.Faucet)
		assert.NotNil(t, result.Mirror)
		assert.Equal(t, mirrorId, *result.Mirror)
		assert.NotNil(t, result.Lighting)
		assert.Equal(t, lightingId, *result.Lighting)
	})

	t.Run("should return error when vanity has no faucet hole spacing", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		vanityId := uuid.New()
		vanityInfo := usecases.Vanity{
			ProductInfo: usecases.ProductInfo{
				Id:       vanityId,
				Category: usecases.CategoryVanity,
				Length:   36.0,
			},
			NumberOfSinks: 1,
			SinkOffset:    0.0,
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		design := createDesignWithVanity()
		design.Vanity = &vanityId
		design.Faucet = nil

		layout := createLayoutWithVanity(48.0)

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "no faucet hole spacing found for vanity")
	})

	t.Run("should return error when vanity has no number of sinks", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		vanityId := uuid.New()
		vanityInfo := usecases.Vanity{
			ProductInfo: usecases.ProductInfo{
				Id:       vanityId,
				Category: usecases.CategoryVanity,
				Length:   36.0,
			},
			FaucetHoleSpacing: usecases.SingleHole,
			NumberOfSinks:     0, // No number of sinks
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		faucetId := uuid.New()
		productSearch.AddResults("category=Faucets&faucet_hole_spacing_compatibility=Single Hole", []uuid.UUID{faucetId})

		design := createDesignWithVanity()
		design.Vanity = &vanityId

		layout := createLayoutWithVanity(48.0)

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "number of sinks not provided for vanity")
	})

	t.Run("should return early when vanity has no sink offset", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		vanityId := uuid.New()
		vanityInfo := usecases.Vanity{
			ProductInfo: usecases.ProductInfo{
				Id:       vanityId,
				Category: usecases.CategoryVanity,
				Length:   36.0,
			},
			FaucetHoleSpacing: usecases.SingleHole,
			NumberOfSinks:     1,
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		faucetId := uuid.New()
		productSearch.AddResults("category=Faucets&faucet_hole_spacing_compatibility=Single Hole", []uuid.UUID{faucetId})

		design := createDesignWithVanity()
		design.Vanity = &vanityId

		layout := createLayoutWithVanity(48.0)

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)
		require.Error(t, err)

		assert.NotNil(t, result.Vanity)
		assert.NotNil(t, result.Faucet)
		// Mirror and lighting should not be set due to early return
		assert.Nil(t, result.Mirror)
		assert.Nil(t, result.Lighting)
	})
}

func TestDesignEvolver_EvolveShowerProducts(t *testing.T) {
	ctx := context.Background()

	// Helper function to create a design with floor tile
	createDesignWithFloorTile := func() usecases.Design {
		floorTileId := uuid.New()
		return usecases.Design{
			ID:        uuid.New(),
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
			DesignOptions: usecases.DesignOptions{
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: &floorTileId,
				},
			},
		}
	}

	t.Run("should clear shower products when no alcove tub and no walk-in shower", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createDesignWithFloorTile()
		showerWallTileId := uuid.New()
		showerShortWallTileId := uuid.New()
		nicheTileId := uuid.New()
		showerSystemId := uuid.New()
		design.ShowerWallTile = &showerWallTileId
		design.ShowerShortWallTile = &showerShortWallTileId
		design.NicheTile = &nicheTileId
		design.ShowerSystem = &showerSystemId

		layout := entities.RoomLayout{
			Id: uuid.New(),
			WetAreas: []entities.WetArea{{
				AlcoveTubs:           []entities.AlcoveTub{},
				ShowerIds:            []uuid.UUID{},
				FreestandingTubIds:   []uuid.UUID{},
				MaxShowerGlassLength: nil,
				MaxTubLength:         nil,
			}},
		}

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.Nil(t, result.ShowerWallTile)
		assert.Nil(t, result.ShowerShortWallTile)
		assert.Nil(t, result.NicheTile)
		assert.Nil(t, result.ShowerSystem)
	})

	t.Run("should return error when both alcove tub and walk-in shower present", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createDesignWithFloorTile()

		layout := entities.RoomLayout{
			Id: uuid.New(),
			WetAreas: []entities.WetArea{{
				AlcoveTubs:           []entities.AlcoveTub{{LayoutId: uuid.New()}},
				ShowerIds:            []uuid.UUID{uuid.New()},
				FreestandingTubIds:   []uuid.UUID{},
				MaxShowerGlassLength: nil,
				MaxTubLength:         nil,
			}},
		}

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "both alcove & walk-in shower found in layout")
	})

	t.Run("should return error when design has no floor tile", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createDesignWithFloorTile()
		design.FloorTile = nil // No floor tile

		layout := entities.RoomLayout{
			Id: uuid.New(),
			WetAreas: []entities.WetArea{{
				AlcoveTubs:           []entities.AlcoveTub{{LayoutId: uuid.New()}},
				ShowerIds:            []uuid.UUID{},
				FreestandingTubIds:   []uuid.UUID{},
				MaxShowerGlassLength: nil,
				MaxTubLength:         nil,
			}},
		}

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "no floor tile found in design")
	})
}

func TestDesignEvolver_EvolveTubProducts(t *testing.T) {
	ctx := context.Background()

	// Helper function to create a basic design
	createBasicDesign := func() usecases.Design {
		floorTileId := uuid.New()
		return usecases.Design{
			ID:        uuid.New(),
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
			DesignOptions: usecases.DesignOptions{
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: &floorTileId,
				},
			},
		}
	}

	t.Run("should clear tub products when no alcove tub and no freestanding tub", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createBasicDesign()
		tubId := uuid.New()
		tubDoorId := uuid.New()
		tubFillerId := uuid.New()
		design.Tub = &tubId
		design.TubDoor = &tubDoorId
		design.TubFiller = &tubFillerId

		layout := entities.RoomLayout{
			Id: uuid.New(),
			WetAreas: []entities.WetArea{{
				AlcoveTubs:         []entities.AlcoveTub{},
				FreestandingTubIds: []uuid.UUID{},
			}},
		}

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.Nil(t, result.Tub)
		assert.Nil(t, result.TubDoor)
		assert.Nil(t, result.TubFiller)
	})

	t.Run("should return error when both alcove and freestanding tubs present", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createBasicDesign()

		// Add floor tile info to catalog
		floorTileInfo := usecases.Tile{
			ProductInfo: usecases.ProductInfo{
				Category: usecases.CategoryTile,
				Id:       *design.FloorTile,
			},
		}
		catalog.AddProduct("tiles", *design.FloorTile, floorTileInfo)

		// Add shower system search results for alcove tub
		showerSystemId := uuid.New()
		productSearch.AddResults("category=Shower%20Systems&shower_has_tub_spout=true&handshower_kit_included=false", []uuid.UUID{showerSystemId})

		// Add shower wall tile search results
		showerWallTileId := uuid.New()
		productSearch.AddResults("category=Tile&tile_location=shower_wall", []uuid.UUID{showerWallTileId})

		layout := entities.RoomLayout{
			Id: uuid.New(),
			WetAreas: []entities.WetArea{{
				AlcoveTubs:         []entities.AlcoveTub{{LayoutId: uuid.New()}},
				FreestandingTubIds: []uuid.UUID{uuid.New()},
			}},
		}

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "both alcove & freestanding tubs found in layout")
	})

	t.Run("should handle alcove tub with short door length", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createBasicDesign()

		// Add floor tile info to catalog
		floorTileInfo := usecases.Tile{
			ProductInfo: usecases.ProductInfo{
				Category: usecases.CategoryTile,
				Id:       *design.FloorTile,
			},
		}
		catalog.AddProduct("tiles", *design.FloorTile, floorTileInfo)

		tubId := uuid.New()
		productSearch.AddResults("category=Tubs&tub_type=ALCOVE", []uuid.UUID{tubId})

		// Add shower system search results for alcove tub
		showerSystemId := uuid.New()
		productSearch.AddResults("category=Shower%20Systems&shower_has_tub_spout=true&handshower_kit_included=false", []uuid.UUID{showerSystemId})

		// Add shower wall tile search results
		showerWallTileId := uuid.New()
		productSearch.AddResults("category=Tile&tile_location=shower_wall", []uuid.UUID{showerWallTileId})

		design.Tub = nil
		design.TubDoor = nil

		maxTubLength := 50.0 // Less than 56
		layout := entities.RoomLayout{
			Id: uuid.New(),
			WetAreas: []entities.WetArea{{
				AlcoveTubs:   []entities.AlcoveTub{{LayoutId: uuid.New()}},
				MaxTubLength: &maxTubLength,
			}},
		}

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.NotNil(t, result.Tub)
		assert.Equal(t, tubId, *result.Tub)
		assert.Nil(t, result.TubDoor)   // Should be nil for short door length
		assert.Nil(t, result.TubFiller) // Should be nil for alcove tub
	})

	t.Run("should handle alcove tub with long door length", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createBasicDesign()

		// Add floor tile info to catalog
		floorTileInfo := usecases.Tile{
			ProductInfo: usecases.ProductInfo{
				Category: usecases.CategoryTile,
				Id:       *design.FloorTile,
			},
		}
		catalog.AddProduct("tiles", *design.FloorTile, floorTileInfo)

		tubId := uuid.New()
		tubDoorId := uuid.New()
		productSearch.AddResults("category=Tubs&tub_type=ALCOVE", []uuid.UUID{tubId})
		productSearch.AddResults("category=Tub%20Doors&shower_enclosure_type=Fixed", []uuid.UUID{tubDoorId})

		// Add shower system search results for alcove tub
		showerSystemId := uuid.New()
		productSearch.AddResults("category=Shower%20Systems&shower_has_tub_spout=true&handshower_kit_included=false", []uuid.UUID{showerSystemId})

		// Add shower wall tile search results
		showerWallTileId := uuid.New()
		productSearch.AddResults("category=Tile&tile_location=shower_wall", []uuid.UUID{showerWallTileId})

		design.Tub = nil
		design.TubDoor = nil

		maxTubLength := 60.0 // Greater than 56
		layout := entities.RoomLayout{
			Id: uuid.New(),
			WetAreas: []entities.WetArea{{
				AlcoveTubs:   []entities.AlcoveTub{{LayoutId: uuid.New()}},
				MaxTubLength: &maxTubLength,
			}},
		}

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.NotNil(t, result.Tub)
		assert.Equal(t, tubId, *result.Tub)
		assert.NotNil(t, result.TubDoor)
		assert.Equal(t, tubDoorId, *result.TubDoor)
		assert.Nil(t, result.TubFiller) // Should be nil for alcove tub
	})

	t.Run("should handle freestanding tub", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		tubId := uuid.New()
		tubFillerId := uuid.New()
		productSearch.AddResults("category=Tubs&tub_type=FREESTANDING", []uuid.UUID{tubId})
		productSearch.AddResults("category=Tub%%20Filler&tub_filler_mounting_position=FLOOR", []uuid.UUID{tubFillerId})

		design := createBasicDesign()
		design.Tub = nil
		design.TubFiller = nil

		layout := entities.RoomLayout{
			Id: uuid.New(),
			WetAreas: []entities.WetArea{{
				FreestandingTubIds: []uuid.UUID{uuid.New()},
			}},
		}

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.NotNil(t, result.Tub)
		assert.Equal(t, tubId, *result.Tub)
		assert.Nil(t, result.TubDoor) // Should be nil for freestanding tub
		assert.NotNil(t, result.TubFiller)
		assert.Equal(t, tubFillerId, *result.TubFiller)
	})

	t.Run("should return error when existing tub has no tub type", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		design := createBasicDesign()

		// Add floor tile info to catalog
		floorTileInfo := usecases.Tile{
			ProductInfo: usecases.ProductInfo{
				Category: usecases.CategoryTile,
				Id:       *design.FloorTile,
			},
		}
		catalog.AddProduct("tiles", *design.FloorTile, floorTileInfo)

		// Add shower system search results for alcove tub
		showerSystemId := uuid.New()
		productSearch.AddResults("category=Shower%20Systems&shower_has_tub_spout=true&handshower_kit_included=false", []uuid.UUID{showerSystemId})

		// Add shower wall tile search results
		showerWallTileId := uuid.New()
		productSearch.AddResults("category=Tile&tile_location=shower_wall", []uuid.UUID{showerWallTileId})

		tubId := uuid.New()
		tubInfo := usecases.Tub{
			ProductInfo: usecases.ProductInfo{
				Id:       tubId,
				Category: usecases.CategoryTub,
			},
		}
		catalog.AddProduct("tubs", tubId, tubInfo)

		design.Tub = &tubId

		layout := entities.RoomLayout{
			Id: uuid.New(),
			WetAreas: []entities.WetArea{{
				AlcoveTubs: []entities.AlcoveTub{{LayoutId: uuid.New()}},
			}},
		}

		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "no type found for tub")
	})
}

func TestDesignEvolver_CalculateMaxRelativeProductLength_Integration(t *testing.T) {
	ctx := context.Background()

	// Test the calculateMaxRelativeProductLength function indirectly through vanity evolution
	t.Run("should handle single sink vanity correctly", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		vanityId := uuid.New()
		vanityInfo := usecases.Vanity{
			ProductInfo: usecases.ProductInfo{
				Id:       vanityId,
				Category: usecases.CategoryVanity,
				Length:   48.0,
			},
			FaucetHoleSpacing: usecases.SingleHole,
			NumberOfSinks:     1,
			SinkOffset:        10.0,
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		faucetId := uuid.New()
		productSearch.AddResults("category=Faucets&faucet_hole_spacing_compatibility=Single Hole", []uuid.UUID{faucetId})

		// For single sink, max relative product length should be the vanity length (48.0)
		mirrorId := uuid.New()
		productSearch.AddResults("category=Mirror&mirror_width_lte=48", []uuid.UUID{mirrorId})

		lightingId := uuid.New()
		productSearch.AddResults("category=Decorative%20Lighting&lighting_length_lte=48", []uuid.UUID{lightingId})

		design := usecases.Design{
			ID:        uuid.New(),
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
			DesignOptions: usecases.DesignOptions{
				Vanity: &vanityId,
			},
		}

		maxLength := 60.0
		layout := entities.RoomLayout{
			Id: uuid.New(),
			Vanities: []entities.Vanity{{
				MaxLength: &maxLength,
			}},
		}

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.NotNil(t, result.Mirror)
		assert.NotNil(t, result.Lighting)
	})

	t.Run("should handle double sink vanity correctly", func(t *testing.T) {
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		vanityId := uuid.New()
		vanityInfo := usecases.Vanity{
			ProductInfo: usecases.ProductInfo{
				Id:       vanityId,
				Category: usecases.CategoryVanity,
				Length:   48.0,
			},
			FaucetHoleSpacing: usecases.SingleHole,
			NumberOfSinks:     2,
			SinkOffset:        12.0, // For double sink with 12" offset, max relative length = (24-12)*2 = 24
		}
		catalog.AddProduct("vanities", vanityId, vanityInfo)

		faucetId := uuid.New()
		productSearch.AddResults("category=Faucets&faucet_hole_spacing_compatibility=Single Hole", []uuid.UUID{faucetId})

		// For double sink with 12" offset, max relative product length should be 24.0
		mirrorId := uuid.New()
		productSearch.AddResults("category=Mirror&mirror_width_lte=24", []uuid.UUID{mirrorId})

		lightingId := uuid.New()
		productSearch.AddResults("category=Decorative%20Lighting&lighting_length_lte=24", []uuid.UUID{lightingId})

		design := usecases.Design{
			ID:        uuid.New(),
			ProjectID: "TEST-PROJECT",
			Status:    usecases.Preview,
			DesignOptions: usecases.DesignOptions{
				Vanity: &vanityId,
			},
		}

		maxLength := 60.0
		layout := entities.RoomLayout{
			Id: uuid.New(),
			Vanities: []entities.Vanity{{
				MaxLength: &maxLength,
			}},
		}

		result, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		require.NoError(t, err)
		assert.NotNil(t, result.Mirror)
		assert.NotNil(t, result.Lighting)
	})
}

func TestDesignEvolver_EvolveProjectDesignsForLayout(t *testing.T) {
	ctx := context.Background()

	t.Run("should present empty slice for empty input", func(t *testing.T) {
		projId := entities.ProjectId("TEST-PROJECT")
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)
		presenter := NewFakeDesignsPresenter()

		layout := entities.RoomLayout{Id: uuid.New()}
		evolver.EvolveProjectDesignsForLayout(ctx, projId, layout, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		assert.Empty(t, presenter.PresentDesignsCalls[0].Designs)
	})

	t.Run("should evolve multiple designs concurrently", func(t *testing.T) {
		projId := entities.ProjectId("TEST-PROJECT")
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)
		presenter := NewFakeDesignsPresenter()

		// Setup product search results
		toiletId := uuid.New()
		productSearch.AddResults("category=Toilet", []uuid.UUID{toiletId})
		shelvingId := uuid.New()
		productSearch.AddResults("category=Shelves", []uuid.UUID{shelvingId})

		// Create test designs
		design1 := usecases.Design{
			ID:        uuid.New(),
			ProjectID: projId,
			Status:    usecases.Preview,
		}
		design2 := usecases.Design{
			ID:        uuid.New(),
			ProjectID: projId,
			Status:    usecases.Preview,
		}
		design3 := usecases.Design{
			ID:        uuid.New(),
			ProjectID: projId,
			Status:    usecases.Preview,
		}

		designs := []usecases.Design{design1, design2, design3}
		for _, design := range designs {
			_, err := repo.UpsertDesign(ctx, design)
			require.NoError(t, err)
		}

		// Create layout with toilets
		layout := entities.RoomLayout{
			Id:        uuid.New(),
			ToiletIds: []uuid.UUID{uuid.New()},
		}

		evolver.EvolveProjectDesignsForLayout(ctx, projId, layout, presenter)

		// Verify results
		require.Len(t, presenter.PresentDesignsCalls, 1)
		require.Len(t, presenter.PresentDesignsCalls[0].Designs, 3)

		for _, design := range presenter.PresentDesignsCalls[0].Designs {
			assert.NotNil(t, design.Toilet)
			assert.Equal(t, toiletId, *design.Toilet)
			assert.NotNil(t, design.Shelving)
			assert.Equal(t, shelvingId, *design.Shelving)
		}

		// Should not have any errors
		assert.Empty(t, presenter.PresentErrorCalls)
	})

	t.Run("should handle errors in individual designs", func(t *testing.T) {
		projId := entities.ProjectId("TEST-PROJECT")
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)
		presenter := NewFakeDesignsPresenter()

		// Setup to cause an error for toilet search
		productSearch.AddError("category=Toilet", errors.New("toilet search failed"))

		// Create test designs
		design1 := usecases.Design{
			ID:        uuid.New(),
			ProjectID: projId,
			Status:    usecases.Preview,
		}
		design2 := usecases.Design{
			ID:        uuid.New(),
			ProjectID: projId,
			Status:    usecases.Preview,
		}

		designs := []usecases.Design{design1, design2}
		for _, design := range designs {
			_, err := repo.UpsertDesign(ctx, design)
			require.NoError(t, err)
		}

		// Create layout with toilets (will trigger the error)
		layout := entities.RoomLayout{
			Id:        uuid.New(),
			ToiletIds: []uuid.UUID{uuid.New()},
		}

		evolver.EvolveProjectDesignsForLayout(ctx, projId, layout, presenter)

		// Verify that errors were presented
		require.Len(t, presenter.PresentErrorCalls, 1)
		errorMsg := presenter.PresentErrorCalls[0].Error()
		assert.Contains(t, errorMsg, "failed to evolve 2 design(s)")
		assert.Contains(t, errorMsg, "toilet search failed")

		// Should not have presented any designs
		assert.Empty(t, presenter.PresentDesignsCalls)
	})

	t.Run("should maintain order of results", func(t *testing.T) {
		projId := entities.ProjectId("TEST-PROJECT")
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)
		presenter := NewFakeDesignsPresenter()

		// Create many designs to test concurrent processing
		designs := make([]usecases.Design, 10)
		for i := range 10 {
			designs[i] = usecases.Design{
				ID:        uuid.New(),
				ProjectID: projId,
				Status:    usecases.Preview,
			}
		}
		for _, design := range designs {
			_, err := repo.UpsertDesign(ctx, design)
			require.NoError(t, err)
		}

		// Create layout without toilets (simpler case)
		layout := entities.RoomLayout{
			Id:        uuid.New(),
			ToiletIds: []uuid.UUID{}, // No toilets
		}

		evolver.EvolveProjectDesignsForLayout(ctx, projId, layout, presenter)

		// Verify results were presented successfully
		require.Len(t, presenter.PresentDesignsCalls, 1)
		require.Len(t, presenter.PresentDesignsCalls[0].Designs, 10)

		// Verify all original design IDs are present (order may vary due to concurrency)
		presentedDesigns := presenter.PresentDesignsCalls[0].Designs
		originalIds := make(map[uuid.UUID]bool)
		for _, design := range designs {
			originalIds[design.ID] = true
		}

		for _, presentedDesign := range presentedDesigns {
			assert.True(t, originalIds[presentedDesign.ID], "Design ID should be from original input")
		}

		// Should not have any errors
		assert.Empty(t, presenter.PresentErrorCalls)
	})

	t.Run("should merge multiple different errors", func(t *testing.T) {
		projId := entities.ProjectId("TEST-PROJECT")
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)
		presenter := NewFakeDesignsPresenter()

		// Setup different errors for different product searches
		productSearch.AddError("category=Toilet", errors.New("toilet search failed"))
		productSearch.AddError("category=Shelves", errors.New("shelving search failed"))

		// Create test designs - one will fail on toilet, another on shelving
		design1 := usecases.Design{
			ID:        uuid.New(),
			ProjectID: projId,
			Status:    usecases.Preview,
		}
		design2 := usecases.Design{
			ID:        uuid.New(),
			ProjectID: projId,
			Status:    usecases.Preview,
		}

		designs := []usecases.Design{design1, design2}
		for _, design := range designs {
			_, err := repo.UpsertDesign(ctx, design)
			require.NoError(t, err)
		}

		// Create layout with toilets (will trigger the errors)
		layout := entities.RoomLayout{
			Id:        uuid.New(),
			ToiletIds: []uuid.UUID{uuid.New()},
		}

		evolver.EvolveProjectDesignsForLayout(ctx, projId, layout, presenter)

		// Verify that merged error was presented
		require.Len(t, presenter.PresentErrorCalls, 1)
		errorMsg := presenter.PresentErrorCalls[0].Error()
		assert.Contains(t, errorMsg, "failed to evolve 2 design(s)")
		assert.Contains(t, errorMsg, "toilet search failed")

		// Should not have presented any designs
		assert.Empty(t, presenter.PresentDesignsCalls)
	})

	t.Run("should panic with nil presenter", func(t *testing.T) {
		projId := entities.ProjectId("TEST-PROJECT")
		catalog := gateways.NewFakeCatalog()
		productSearch := gateways.NewFakeProductSearch()
		repo := gateways.NewFakeRelDb()
		evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)

		layout := entities.RoomLayout{Id: uuid.New()}

		assert.Panics(t, func() {
			evolver.EvolveProjectDesignsForLayout(ctx, projId, layout, nil)
		})
	})
}
