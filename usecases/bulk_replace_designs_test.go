package usecases_test

import (
	"context"
	"log/slog"
	"net/url"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// TestOutcomePresenter captures the outcome of operations for testing
type TestOutcomePresenter struct {
	successCalled bool
	errorCalled   bool
	lastError     error
}

func NewTestOutcomePresenter() *TestOutcomePresenter {
	return &TestOutcomePresenter{}
}

func (p *TestOutcomePresenter) ConveySuccess() {
	p.successCalled = true
}

func (p *TestOutcomePresenter) PresentError(err error) {
	p.errorCalled = true
	p.lastError = err
}

func (p *TestOutcomePresenter) WasSuccessful() bool {
	return p.successCalled && !p.errorCalled
}

func (p *TestOutcomePresenter) WasError() bool {
	return p.errorCalled
}

func (p *TestOutcomePresenter) GetLastError() error {
	return p.lastError
}

func TestNewBulkDesignReplacer(t *testing.T) {
	t.Run("should create bulk replacer with valid repository and logger", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		monolith := NewFakeMonolith()
		logger := slog.Default()
		replacer := usecases.NewBulkDesignReplacer(repo, monolith, logger)
		assert.NotNil(t, replacer)
	})

	t.Run("should create bulk replacer with nil logger (uses default)", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		monolith := NewFakeMonolith()
		replacer := usecases.NewBulkDesignReplacer(repo, monolith, nil)
		assert.NotNil(t, replacer)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		monolith := NewFakeMonolith()
		logger := slog.Default()
		assert.Panics(t, func() {
			usecases.NewBulkDesignReplacer(nil, monolith, logger)
		})
	})

	t.Run("should panic with nil monolith", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		logger := slog.Default()
		assert.Panics(t, func() {
			usecases.NewBulkDesignReplacer(repo, nil, logger)
		})
	})
}

func TestBulkDesignReplacer_ReplaceAllDesignsForProject(t *testing.T) {
	ctx := context.Background()
	projectId := entities.ProjectId("TEST-PROJECT")

	t.Run("should save multiple designs successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		monolith := NewFakeMonolith()
		presenter := NewTestOutcomePresenter()
		logger := slog.Default()
		replacer := usecases.NewBulkDesignReplacer(repo, monolith, logger)

		design1Id := uuid.New()
		design2Id := uuid.New()
		designs := []usecases.Design{
			{
				ID:                 design1Id,
				ProjectID:          projectId,
				Status:             usecases.Preview,
				WallpaperPlacement: usecases.NoWallpaper,
				WallTilePlacement:  usecases.NoWallTile,
			},
			{
				ID:                 design2Id,
				ProjectID:          projectId,
				Status:             usecases.Fave,
				WallpaperPlacement: usecases.VanityWall,
				WallTilePlacement:  usecases.HalfWall,
			},
		}

		replacer.ReplaceAllDesignsForProject(ctx, presenter, designs, projectId)

		// Verify the operation was successful
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed")
		assert.False(t, presenter.WasError(), "Expected no error")

		// Verify designs were saved to the repository
		savedDesign1, err := repo.ReadDesign(ctx, design1Id)
		require.NoError(t, err)
		assert.Equal(t, design1Id, savedDesign1.ID)
		assert.Equal(t, projectId, savedDesign1.ProjectID)
		assert.Equal(t, usecases.Preview, savedDesign1.Status)

		savedDesign2, err := repo.ReadDesign(ctx, design2Id)
		require.NoError(t, err)
		assert.Equal(t, design2Id, savedDesign2.ID)
		assert.Equal(t, projectId, savedDesign2.ProjectID)
		assert.Equal(t, usecases.Fave, savedDesign2.Status)

		// Verify that replacing the designs for the project with fewer designs deletes the orphaned designs.
		designs = designs[:1]
		replacer.ReplaceAllDesignsForProject(ctx, presenter, designs, projectId)
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed")
		assert.False(t, presenter.WasError(), "Expected no error")

		// Verify designs were saved to the repository
		savedDesign1, err = repo.ReadDesign(ctx, design1Id)
		require.NoError(t, err)
		assert.Equal(t, design1Id, savedDesign1.ID)
		assert.Equal(t, projectId, savedDesign1.ProjectID)
		assert.Equal(t, usecases.Preview, savedDesign1.Status)

		_, err = repo.ReadDesign(ctx, design2Id)
		assert.Error(t, err, "Expected orphaned design to be deleted.")
	})

	t.Run("should reject designs with zero UUID", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		monolith := NewFakeMonolith()
		presenter := NewTestOutcomePresenter()
		logger := slog.Default()
		replacer := usecases.NewBulkDesignReplacer(repo, monolith, logger)

		var zeroUUID uuid.UUID
		designs := []usecases.Design{
			{
				ID:                 zeroUUID, // Zero UUID should be rejected
				ProjectID:          projectId,
				Status:             usecases.Fave,
				WallpaperPlacement: usecases.NoWallpaper,
				WallTilePlacement:  usecases.NoWallTile,
			},
		}

		replacer.ReplaceAllDesignsForProject(ctx, presenter, designs, projectId)

		// Verify the operation failed with an error
		assert.True(t, presenter.WasError(), "Expected operation to fail")
		assert.False(t, presenter.WasSuccessful(), "Expected no success")
		assert.Equal(t, usecases.ErrInvalidPayload, presenter.GetLastError())

		// Verify no design was saved to the repository
		_, err := repo.ReadDesign(ctx, zeroUUID)
		assert.Error(t, err, "Expected design not to be saved")
	})

	t.Run("should reject designs with uuid.Nil", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		monolith := NewFakeMonolith()
		presenter := NewTestOutcomePresenter()
		logger := slog.Default()
		replacer := usecases.NewBulkDesignReplacer(repo, monolith, logger)

		designs := []usecases.Design{
			{
				ID:                 uuid.Nil, // uuid.Nil should be rejected
				ProjectID:          projectId,
				Status:             usecases.Fave,
				WallpaperPlacement: usecases.NoWallpaper,
				WallTilePlacement:  usecases.NoWallTile,
			},
		}

		replacer.ReplaceAllDesignsForProject(ctx, presenter, designs, projectId)

		// Verify the operation failed with an error
		assert.True(t, presenter.WasError(), "Expected operation to fail")
		assert.False(t, presenter.WasSuccessful(), "Expected no success")
		assert.Equal(t, usecases.ErrInvalidPayload, presenter.GetLastError())

		// Verify no design was saved to the repository
		_, err := repo.ReadDesign(ctx, uuid.Nil)
		assert.Error(t, err, "Expected design not to be saved")
	})

	t.Run("should handle empty design list", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		monolith := NewFakeMonolith()
		presenter := NewTestOutcomePresenter()
		logger := slog.Default()
		replacer := usecases.NewBulkDesignReplacer(repo, monolith, logger)

		designs := []usecases.Design{}

		replacer.ReplaceAllDesignsForProject(ctx, presenter, designs, projectId)

		// Verify the operation was successful (empty list is valid)
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed with empty list")
		assert.False(t, presenter.WasError(), "Expected no error")

		// Verify no designs exist for the project
		savedDesigns, err := repo.DesignsForProject(ctx, projectId)
		require.NoError(t, err)
		assert.Empty(t, savedDesigns, "Expected no designs to be saved")
	})

	t.Run("should handle mixed valid and invalid IDs", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		monolith := NewFakeMonolith()
		presenter := NewTestOutcomePresenter()
		logger := slog.Default()
		replacer := usecases.NewBulkDesignReplacer(repo, monolith, logger)

		validId := uuid.New()
		var zeroUUID uuid.UUID
		designs := []usecases.Design{
			{
				ID:                 validId, // Valid ID, should be saved
				ProjectID:          projectId,
				Status:             usecases.Fave,
				WallpaperPlacement: usecases.NoWallpaper,
				WallTilePlacement:  usecases.NoWallTile,
			},
			{
				ID:                 zeroUUID, // Zero UUID, should cause error
				ProjectID:          projectId,
				Status:             usecases.Archived,
				WallpaperPlacement: usecases.VanityWall,
				WallTilePlacement:  usecases.HalfWall,
			},
			{
				ID:                 uuid.Nil, // uuid.Nil, should cause error
				ProjectID:          projectId,
				Status:             usecases.Fave,
				WallpaperPlacement: usecases.AllWalls,
				WallTilePlacement:  usecases.FullWall,
			},
		}

		replacer.ReplaceAllDesignsForProject(ctx, presenter, designs, projectId)

		// Verify the operation failed due to invalid IDs
		assert.True(t, presenter.WasError(), "Expected operation to fail")
		assert.False(t, presenter.WasSuccessful(), "Expected no success")
		assert.Equal(t, usecases.ErrInvalidPayload, presenter.GetLastError())

		// The valid design should still be saved (current implementation saves valid ones first)
		savedDesign, err := repo.ReadDesign(ctx, validId)
		require.NoError(t, err)
		assert.Equal(t, validId, savedDesign.ID)
		assert.Equal(t, usecases.Fave, savedDesign.Status)
	})

	t.Run("should preserve design data integrity", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		monolith := NewFakeMonolith()
		presenter := NewTestOutcomePresenter()
		logger := slog.Default()
		replacer := usecases.NewBulkDesignReplacer(repo, monolith, logger)

		designId := uuid.New()
		design := usecases.Design{
			ID:                 designId,
			ProjectID:          projectId,
			Status:             usecases.Fave,
			WallpaperPlacement: usecases.VanityWall,
			WallTilePlacement:  usecases.HalfWall,
			DesignOptions: usecases.DesignOptions{
				ColorScheme: &[]usecases.ColorScheme{usecases.Neutral}[0],
				Style:       &[]usecases.Style{usecases.Modern}[0],
			},
		}

		replacer.ReplaceAllDesignsForProject(ctx, presenter, []usecases.Design{design}, projectId)

		// Verify the operation was successful
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed")

		// Verify all design data was preserved
		savedDesign, err := repo.ReadDesign(ctx, designId)
		require.NoError(t, err)
		assert.Equal(t, design.ID, savedDesign.ID)
		assert.Equal(t, design.ProjectID, savedDesign.ProjectID)
		assert.Equal(t, design.Status, savedDesign.Status)
		assert.Equal(t, design.WallpaperPlacement, savedDesign.WallpaperPlacement)
		assert.Equal(t, design.WallTilePlacement, savedDesign.WallTilePlacement)
		assert.Equal(t, *design.ColorScheme, *savedDesign.ColorScheme)
		assert.Equal(t, *design.Style, *savedDesign.Style)
		assert.NotZero(t, savedDesign.Created, "Created timestamp should be set")
		assert.NotZero(t, savedDesign.LastUpdated, "LastUpdated timestamp should be set")

		// Add a rendition to the design
		rendition := entities.Rendition{
			Id:     uuid.New(),
			Status: entities.RenditionCompleted,
			URL:    &url.URL{Scheme: "https", Host: "example.com", Path: "/image.webp"},
		}
		renditionId, err := repo.InsertRendition(ctx, designId, rendition)
		require.NoError(t, err)

		// Verify the rendition was saved
		savedRenditions, err := repo.Renditions(ctx, []uuid.UUID{renditionId})
		require.NoError(t, err)
		assert.Len(t, savedRenditions, 1)
		savedRendition := savedRenditions[0]
		assert.Equal(t, renditionId, savedRendition.Id)
		assert.Equal(t, designId, *savedRendition.DesignId)
		assert.Equal(t, entities.RenditionCompleted, savedRendition.Status)
		assert.Equal(t, "https://example.com/image.webp", savedRendition.URL.String())

		// Verify that rendition is preserved after replacing the design
		replacer.ReplaceAllDesignsForProject(ctx, presenter, []usecases.Design{design}, projectId)
		assert.True(t, presenter.WasSuccessful(), "Expected operation to succeed")
		savedRenditions, err = repo.RenditionsForDesign(ctx, designId)
		require.NoError(t, err)
		assert.Len(t, savedRenditions, 1)
		savedRendition = savedRenditions[0]
		assert.Equal(t, renditionId, savedRendition.Id)
		assert.Equal(t, "https://example.com/image.webp", savedRendition.URL.String())
		assert.Equal(t, entities.RenditionOutdated, savedRendition.Status)
	})

	t.Run("should handle designs for different projects independently", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		monolith := NewFakeMonolith()
		presenter := NewTestOutcomePresenter()
		logger := slog.Default()
		replacer := usecases.NewBulkDesignReplacer(repo, monolith, logger)

		project1Id := entities.ProjectId("PROJECT-1")
		project2Id := entities.ProjectId("PROJECT-2")

		design1Id := uuid.New()
		design2Id := uuid.New()

		// Save design for project 1
		design1 := usecases.Design{
			ID:                 design1Id,
			ProjectID:          project1Id,
			Status:             usecases.Preview,
			WallpaperPlacement: usecases.NoWallpaper,
			WallTilePlacement:  usecases.NoWallTile,
		}

		// Save design for project 2
		design2 := usecases.Design{
			ID:                 design2Id,
			ProjectID:          project2Id,
			Status:             usecases.Fave,
			WallpaperPlacement: usecases.VanityWall,
			WallTilePlacement:  usecases.HalfWall,
		}

		// Save designs for both projects
		replacer.ReplaceAllDesignsForProject(ctx, presenter, []usecases.Design{design1}, project1Id)
		assert.True(t, presenter.WasSuccessful(), "Expected first operation to succeed")

		presenter = NewTestOutcomePresenter() // Reset presenter
		replacer.ReplaceAllDesignsForProject(ctx, presenter, []usecases.Design{design2}, project2Id)
		assert.True(t, presenter.WasSuccessful(), "Expected second operation to succeed")

		// Verify both designs exist and are associated with correct projects
		savedDesign1, err := repo.ReadDesign(ctx, design1Id)
		require.NoError(t, err)
		assert.Equal(t, project1Id, savedDesign1.ProjectID)

		savedDesign2, err := repo.ReadDesign(ctx, design2Id)
		require.NoError(t, err)
		assert.Equal(t, project2Id, savedDesign2.ProjectID)

		// Verify designs are correctly grouped by project
		project1Designs, err := repo.DesignsForProject(ctx, project1Id)
		require.NoError(t, err)
		assert.Len(t, project1Designs, 1)
		assert.Equal(t, design1Id, project1Designs[0].ID)

		project2Designs, err := repo.DesignsForProject(ctx, project2Id)
		require.NoError(t, err)
		assert.Len(t, project2Designs, 1)
		assert.Equal(t, design2Id, project2Designs[0].ID)
	})

}
