package usecases_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Fake implementations are in test_helpers_test.go

func TestNewTemplateRetriever(t *testing.T) {
	t.Run("should create retriever with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		retriever := usecases.NewTemplateRetriever(repo)
		assert.NotNil(t, retriever)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewTemplateRetriever(nil)
		})
	})
}

func TestTemplateRetriever_RetrieveTemplate(t *testing.T) {
	ctx := context.Background()
	templateId := uuid.New()
	testTemplate := usecases.Template{
		ID:   templateId,
		Name: "Test Template",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
	}

	t.Run("should retrieve template successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeTemplatesPresenter()
		retriever := usecases.NewTemplateRetriever(repo)

		// First, save the template to the repository
		_, err := repo.InsertTemplate(ctx, testTemplate, "")
		require.NoError(t, err)

		retriever.RetrieveTemplate(ctx, presenter, templateId)

		// Verify the template was presented
		AssertPresentTemplateCalled(t, presenter, testTemplate)
		AssertPresentErrorNotCalled(t, presenter)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeTemplatesPresenter()
		retriever := usecases.NewTemplateRetriever(repo)

		// Don't save any template to the repository
		nonExistentId := uuid.New()
		retriever.RetrieveTemplate(ctx, presenter, nonExistentId)

		// Verify error was presented
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Empty(t, presenter.PresentTemplateCalls, "Expected PresentTemplate not to be called")
	})
}

func TestTemplateRetriever_RetrieveAllTemplates(t *testing.T) {
	ctx := context.Background()
	testTemplates := []usecases.Template{
		{
			ID:   uuid.New(),
			Name: "Template 1",
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.Modern,
			},
		},
		{
			ID:   uuid.New(),
			Name: "Template 2",
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Bold,
				Style:       usecases.Traditional,
			},
		},
	}

	t.Run("should retrieve all templates successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeTemplatesPresenter()
		retriever := usecases.NewTemplateRetriever(repo)

		// First, save the templates to the repository
		for _, template := range testTemplates {
			_, err := repo.InsertTemplate(ctx, template, "")
			require.NoError(t, err)
		}

		retriever.RetrieveAllTemplates(ctx, presenter)

		// Verify the templates were presented
		assert.NotEmpty(t, presenter.PresentTemplatesCalls, "Expected PresentTemplates to be called")
		if len(presenter.PresentTemplatesCalls) > 0 {
			lastCall := presenter.PresentTemplatesCalls[len(presenter.PresentTemplatesCalls)-1]
			assert.Len(t, lastCall.Templates, len(testTemplates),
				"Expected %d templates to be presented", len(testTemplates))
		}
		AssertPresentErrorNotCalled(t, presenter)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		// For integration testing with FakeRelDb, we test normal operation
		// since FakeRelDb doesn't typically fail
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeTemplatesPresenter()
		retriever := usecases.NewTemplateRetriever(repo)

		// Don't save any templates to the repository
		retriever.RetrieveAllTemplates(ctx, presenter)

		// Verify empty list was presented (not an error)
		assert.NotEmpty(t, presenter.PresentTemplatesCalls, "Expected PresentTemplates to be called")
		if len(presenter.PresentTemplatesCalls) > 0 {
			lastCall := presenter.PresentTemplatesCalls[len(presenter.PresentTemplatesCalls)-1]
			assert.Empty(t, lastCall.Templates, "Expected empty templates list")
		}
		AssertPresentErrorNotCalled(t, presenter)
	})
}
