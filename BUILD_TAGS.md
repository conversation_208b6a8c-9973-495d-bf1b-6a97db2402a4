# Build Tags for DataDog Tracing

This project uses Go build tags to conditionally include DataDog tracing libraries,
allowing for lighter development builds while maintaining full tracing capabilities in production.

## Overview

The codebase supports two build modes:

1. **Development Mode** (`-tags=dev`): Excludes DataDog tracing libraries for faster builds and smaller binaries
2. **Production Mode** (default): Includes full DataDog tracing capabilities

## Binary size comparison

- **Development build**: ~23MB (without DataDog tracing)
- **Production build**: ~49MB (with DataDog tracing)
- **Savings**: ~26MB reduction (53% smaller) for development builds

## Building

```bash
# Development build
go build -tags=dev

# Production build
go build
```

Either way you should end up with a binary called `room-design` that you can then run.
To directly build+run in one step, use `go run -tags=dev .`

## Implementation details

The conditional compilation is implemented using build tags across several files:

### Web framework layer

- `web/tracing_prod.go` (`//go:build !dev`): DataDog tracing implementation
- `web/tracing_dev.go` (`//go:build dev`): No-op tracing implementation

### Database layer

- `frameworks/db/pgx_prod.go` (`//go:build !dev`): DataDog-traced database connections
- `frameworks/db/pgx_dev.go` (`//go:build dev`): Standard database connections

## Key benefits

1. **Faster Development Builds**: Significantly reduced compilation time and binary size
2. **Reduced Memory Usage**: Lower memory footprint during local development
3. **Simplified Dependencies**: Fewer external dependencies to manage in development
4. **Production Parity**: Full tracing capabilities maintained in production builds
5. **Clean Architecture**: Tracing concerns are properly abstracted

## Testing

Both build modes should be tested to ensure compatibility:

```bash
# Test development build
go test -tags=dev ./...

# Test production build
go test ./...
```

## Docker considerations

The Dockerfile can use the appropriate build command:

```dockerfile
# For production
RUN go build -o server

# For development/testing
RUN go build -tags=dev -o server
```

## Environment variables

The tracing behavior is controlled at build time, not runtime. However, DataDog tracing can still be configured via environment variables in production builds:

- `DD_TRACE_ENABLED`: Enable/disable tracing
- `DD_SERVICE`: Service name
- `DD_ENV`: Environment name
- `DD_VERSION`: Service version
